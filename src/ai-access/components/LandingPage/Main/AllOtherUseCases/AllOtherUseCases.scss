.tw-dark {
    .ai-access-all-other-case {
        background-image: url("../../../../images/appImages/dark/all-other-use-cases-dark-theme.svg");
    }

    .ai-access-all-other-case-loading {
        background-image: url("../../../../images/appImages/dark/all-other-use-cases-dark-theme.svg");
    }

    .ai-access-all-other-use-case-pill {
        background-color: #000000;
    }

    .ai-access-all-other-case-leaf-top-banner {
        border-radius: 100px;
        background-color: #242424;
        color: #F4F5F5;
    }

    .ai-access-all-other-case-title {
        padding: 0 4px;
        color: #F4F5F5;
    }

    .ai-access-all-other-case-single-stacked-bar-title,
    .ai-access-all-other-case-single-stacked-bar-title b,
    .ai-access-all-other-case-most-used-app-title,
    .ai-access-all-other-case-application-title,
    .ai-access-all-other-case-application-count {
        color: #F4F5F5;
    }

}

.ai-access-all-other-case-pill-container {
    position: relative;
}

.ai-access-all-other-case-leaf-top-banner-wrapper {
    position: absolute;
    bottom: 3.3rem;
    right: 17.5rem;

    @media (min-width: 2000px) {
        bottom: 4rem;
        right: 21.5rem;
    }

    @media (min-width: 2560px) {
        bottom: 3.8rem;
        right: 20.5rem;
    }

    @media (min-width: 3840px) {
        bottom: 4.3rem;
        right: 24.5rem;
    }
}

.ai-access-all-other-case {
    background-size: cover;
    overflow: hidden;
    background-image: url("../../../../images/appImages/light/bottom-all-other-case-light-theme.svg");
    width: 707px;
    height: 50px;
    display: flex;
    justify-content: space-evenly;

    @media (min-width: 2000px) {
        width: 900px;
        height: 65px;
    }

    @media (min-width: 2560px) {
        width: 1128px;
        height: 80px;
        position: relative;
        left: 0.5rem;
    }

    @media (min-width: 3840px) {
        width: 1435px;
        left: 0rem;
        height: 100px;
    }
}

.ai-access-all-other-case-loading {
    background-size: cover;
    background-image: url("../../../../images/appImages/light/bottom-all-other-case-light-theme-loading.svg");
    width: 707px;
    height: 50px;
    display: flex;
    justify-content: space-evenly;

    @media (min-width: 2000px) {
        width: 990px;
        height: 72px;
    }

    @media (min-width: 2560px) {
        width: 1128px;
        height: 80px;
        position: relative;
        left: 0.5rem;
    }

    @media (min-width: 3840px) {
        width: 1435px;
        left: 0rem;
        height: 100px;
    }
}

.ai-access-all-other-case-pill-containter {
    display: flex;
    position: relative;
    top: 0.2rem;
    left: 0.9rem;
    justify-content: space-evenly;
    width: 260px;
    align-items: center;

    @media (min-width: 2560px) {
        width: 325px;
    }

    @media (min-width: 3840px) {
        width: 355px;
    }
}

.ai-access-all-other-use-case-pill {
    display: flex;
    background-color: #FFFFFF;
    border-radius: 100px;
    border: 2px solid #656F76;
    backdrop-filter: blur(3px);
    height: 30px;
    width: 30px;
    position: relative;
    display: flex;
    justify-items: center;
    align-items: center;
    cursor: pointer;

    @media (min-width: 2560px) {
        height: 35px;
        width: 35px;
    }

    @media (min-width: 3840px) {
        height: 45px;
        width: 45px;
        margin-right: .4rem;
    }
}

.ai-access-all-other-use-case-leaf-content-title-image {
    top: 3px;
    left: 6px;
    position: relative;

    @media (min-width: 2560px) {
        left: 8px;
        transform: scale(1.3);
    }

    @media (min-width: 3840px) {
        left: 13px;
        transform: scale(1.4);
    }
}

.ai-access-all-other-case-leaf-top-banner {
    position: relative;
    top: 0.7rem;
    z-index: 1;
    color: #333;
    text-align: center;
    font-family: "Lato";
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.022px;
    border-radius: 100px;
    width: 9rem;
    background-color: #FFFFFF;

    @media (min-width: 2000px) {
        font-size: 13px;
        line-height: 20px;
        width: 11rem;
        height: 20px;
    }


    @media (min-width: 2560px) {
        top: -0.3rem;
        font-size: 15px;
        line-height: 25px;
        width: 14rem;
        height: 24px;
        right: 7rem;
    }

    @media (min-width: 3840px) {
        top: -0.7rem;
        font-size: 24px;
        line-height: 28px;
        width: 19rem;
        height: 32px;
        right: 8rem;
    }
}

.ai-access-all-other-case-single-bar-chart-containter {
    position: relative;
    top: 0.6rem;
    right: 0.5rem;
    text-align: -webkit-center;
    padding: 4px;

    @media (min-width: 3840px) {
        top: 1rem;
    }
}

.ai-access-all-other-case-single-stacked-bar-title {
    font-family: Lato;
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.022px;
    color: #707070;
    margin: 2px 0;

    @media (min-width: 2000px) {
        font-size: 13px;
        margin: 4px 0;
    }

    @media (min-width: 2560px) {
        font-size: 16px;
        margin-bottom: 1rem;
    }

    @media (min-width: 3840px) {
        font-size: 24px;
    }
}

.ai-access-all-other-case-single-stacked-bar-title b {
    color: #333333;
}

.ai-access-all-other-use-case-pill-zero-state {
    cursor: unset;
}
