import ContentLoader from "react-content-loader";
import { useThemeContext } from "../../../../../../netsec-ui/utils/ThemeProvider";
import { ScreenSize } from "../../../../../constants/screenSizes";
import { useScreenSize } from "../../../../../hooks/useScreenSize";


const AllOtherUseCasesLoading = () => {
    const themeMode = useThemeContext()?.mode;
    const { size: screenSize } = useScreenSize();
    const stylesByScreenSize = {
        [ScreenSize.SMALL]: {
            width: 900,
            height: 100,
            viewBox: "0 0 500 200",
            rect1: {
                x: 120,
                y: 40,
                width: 300,
                height: 20,
            },
            rect2: {
                x: 100,
                y: 40,
                width: 300,
                height: 20,
            },
            circles: [
                { cx: 40, cy: 50, r: 25 },
                { cx: 120, cy: 50, r: 25 },
                { cx: 200, cy: 50, r: 25 },
                { cx: 280, cy: 50, r: 25 },
                { cx: 360, cy: 50, r: 25 },
                { cx: 440, cy: 50, r: 25 },
            ],
        },
        [ScreenSize.LARGE]: {
            width: 900,
            height: 100,
            viewBox: "0 0 500 200",
            rect1: {
                x: 120,
                y: 60,
                width: 300,
                height: 20,
            },
            rect2: {
                x: 100,
                y: 60,
                width: 300,
                height: 20,
            },
            circles: [
                { cx: 40, cy: 70, r: 25 },
                { cx: 120, cy: 70, r: 25 },
                { cx: 200, cy: 70, r: 25 },
                { cx: 280, cy: 70, r: 25 },
                { cx: 360, cy: 70, r: 25 },
                { cx: 440, cy: 70, r: 25 },
            ],
        },
        [ScreenSize.XLARGE]: {
            width: 1300,
            height: 100,
            viewBox: "0 0 700 200",
            rect1: {
                x: 120,
                y: 80,
                width: 400,
                height: 20,
            },
            rect2: {
                x: 100,
                y: 80,
                width: 400,
                height: 20,
            },
            circles: [
                { cx: 80, cy: 85, r: 30 },
                { cx: 160, cy: 85, r: 30 },
                { cx: 240, cy: 85, r: 30 },
                { cx: 320, cy: 85, r: 30 },
                { cx: 400, cy: 85, r: 30 },
                { cx: 480, cy: 85, r: 30 },
            ],
        },
        [ScreenSize.XXLARGE]: {
            width: 1000,
            height: 100,
            viewBox: "0 0 900 200",
            rect1: {
                x: 330,
                y: 100,
                width: 420,
                height: 20,
            },
            rect2: {
                x: 10,
                y: 90,
                width: 450,
                height: 20,
            },
            circles: [
                { cx: 180, cy: 95, r: 40 },
                { cx: 280, cy: 95, r: 40 },
                { cx: 380, cy: 95, r: 40 },
                { cx: 480, cy: 95, r: 40 },
                { cx: 580, cy: 95, r: 40 },
                { cx: 680, cy: 95, r: 40 },
            ],
        },
    };
    const currentLoaderProps = stylesByScreenSize[screenSize];
    return (
        <div className="ai-access-all-other-case-pill-container">
            <div className="ai-access-all-other-case-loading">
                <ContentLoader
                    speed={1}
                    {...currentLoaderProps}
                    backgroundColor={themeMode === "dark" ? "#484848" : "#B8B8B8"}
                    foregroundColor="#ecebeb"
                >
                    <rect
                        x={currentLoaderProps.rect1.x}
                        y={currentLoaderProps.rect1.y}
                        rx="10"
                        ry="10"
                        width={currentLoaderProps.rect1.width}
                        height={currentLoaderProps.rect1.height}
                    />
                </ContentLoader>
                <ContentLoader
                    speed={1}
                    {...currentLoaderProps}
                    backgroundColor={themeMode === "dark" ? "#484848" : "#B8B8B8"}
                    foregroundColor="#ecebeb"
                >
                    {currentLoaderProps.circles.map((circle, index) => (
                        <circle key={index} cx={circle.cx} cy={circle.cy} r={circle.r} />
                    ))}
                </ContentLoader>
                <ContentLoader
                    speed={1}
                    {...currentLoaderProps}
                    backgroundColor={themeMode === "dark" ? "#484848" : "#B8B8B8"}
                    foregroundColor="#ecebeb"
                >
                    <rect
                        x={currentLoaderProps.rect2.x}
                        y={currentLoaderProps.rect2.y}
                        rx="10"
                        ry="10"
                        width={currentLoaderProps.rect2.width}
                        height={currentLoaderProps.rect2.height}
                    />
                </ContentLoader>
            </div>
        </div>
    )
}
export default AllOtherUseCasesLoading;
