.tw-dark {
    .ai-access-tooltip-most-used-app-title {
        color: #B8B8B8;
    }

    .ai-access-all-other-case-tooltip-icon-container {
        background-color: #000000;
    }

    .ai-access-all-other-case-tooltip-title-container,
    .ai-access-all-other-case-tooltip-icon-container {
        color: #F4F5F5;
    }
}

.ai-access-all-other-case-tooltip-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 26000px;
    padding: 8px;
}

.ai-access-all-other-case-tooltip-title-container {
    display: flex;
    align-items: baseline;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    color: #000000;
    line-height: 24px;
    position: relative;
    margin-bottom: 4px;
}

.ai-access-all-other-case-tooltip-title-image {
    border-radius: 100px;
    height: 16px;
    width: 16px;
    background-color: #FFFFFF;
    backdrop-filter: blur(5px);
    cursor: pointer;
    position: relative;
    bottom: 1px;
}

.ai-access-all-other-use-case-tooltip-content-title-image {
    top: 8px;
    left: 0px;
    position: relative;
}

.ai-access-all-other-case-tooltip-title {
    padding: 0 2px;
    margin: 0;
}

.ai-access-tooltip-most-used-app-title {
    color: #333333;
    font-family: Lato;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    line-height: 16px;
    margin: 4px 0;
    text-align: start;
}

.ai-access-all-other-case-tooltip-icon-container {
    color: #000000;
    background-color: #fff;
    border-radius: 100px;
    background-color: linear-gradient(158deg, rgba(48, 57, 64, 0.80) 7.98%, rgba(14, 19, 23, 0.80) 90.83%);
    backdrop-filter: blur(3.39393949508667px);
    height: 26px;
    width: 26px;
    padding: 0 3px;
    justify-content: center;
}


.ai-access-all-other-case-most-used-app-title-container {
    color: #000000;
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    display: flex;
    align-items: center;
    font-weight: 400;
    line-height: 20px;
    margin-bottom: 4px;
}

.ai-access-all-other-case-most-used-app-title {
    font-family: Lato;
    font-size: 14px;
    font-style: normal;
    display: flex;
    align-items: baseline;
    font-weight: 400;
    line-height: 20px;
    padding: 0 4px;
    margin-bottom: 4px;
}

.ai-access-all-other-case-application-title {
    margin-bottom: 4px;
    padding-bottom: 4px;
    color: #000000;
}

.ai-access-all-other-case-application-container {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    padding-bottom: 10px;
}

.ai-access-all-other-case-application-count {
    margin-bottom: 4px;
    padding-bottom: 4px;
    color: #000000;
}
