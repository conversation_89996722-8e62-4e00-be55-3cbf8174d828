import React, { useEffect, useMemo, useState } from "react";
import { useSelector } from "react-redux";
import SingleStackedBarChart from "../../../ReusableComponents/SingleStackedBarChart";
import IntlUtil from "../../../../../netsec-ui/utils/i18n/IntlUtil";
import { getTag, getUseCases, isNil, tagsAvailable } from "../../../../Utils";
import { formatIntegersWithD3Fn } from "../../../../../netsec-ui/utils/i18n/translate";
import { useThemeContext } from "../../../../../netsec-ui/utils/ThemeProvider";
import "./AllOtherUseCases.scss";
import { RootState } from "../../../../../netsec-ui/redux/types";
import { getTimeRangeFilter } from "../../../../../netsec-ui/redux/filter-server";
import { getTimeRangeFilterFromCache } from "../../../../../netsec-ui/utils/cache/NetsecUICacheUtil";
import { fetchAiOtherUseCasesUserCountData } from "../../../../redux/sase-backend-server/api";
import { fetchAndValidateCache } from "../../../../redux/sase-backend-server/fetchCache";
import { isEmpty } from "../../../../../netsec-ui/utils/NetsecUIUtils";
import { ScreenSize } from "../../../../constants/screenSizes";
import { useScreenSize } from "../../../../hooks/useScreenSize";
import AllOtherUseCasesLoading from "./AllOtherUseCasesLoading";
import UseCasePill from "./UseCasePill";

export interface AllOtherUseCasesProps {
}

interface IOtherUseCasesUserAppCountProps {
    totalUsers: number;
    totalApplications: number;
    unsanctionedUsers: number;
    unsanctionedApplications: number;
    sanctionedUsers: number;
    sanctionedApplications: number;
    toleratedUsers: number;
    toleratedApplications: number;
}

const AllOtherUseCases: React.FC<AllOtherUseCasesProps> = (props) => {
    const { size: screenSize } = useScreenSize();
    //@ts-ignore
    const { loading, data, error } = useSelector((state) => state?.ai?.topUseCases?.default ?? {});
    const allOtherUseCases = data?.allOtherUseCases;
    const values = useMemo(
        () => allOtherUseCases?.map((data) => data.use_case),
        [allOtherUseCases],
    );
    const themeMode = useThemeContext()?.mode;
    const useCases = getUseCases(themeMode);
    const timeRange =
        useSelector((state: RootState) => getTimeRangeFilter(state)) ||
        getTimeRangeFilterFromCache();

    const [otherUserAppCount, setOtherUserAppCount] = useState<IOtherUseCasesUserAppCountProps>({
        totalUsers: 0,
        totalApplications: 0,
        unsanctionedUsers: 0,
        unsanctionedApplications: 0,
        sanctionedUsers: 0,
        sanctionedApplications: 0,
        toleratedUsers: 0,
        toleratedApplications: 0,
    });
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const intlInstance = IntlUtil.getIntl();
    const buttonGroup = getTag();
    const isTagsAvailable = tagsAvailable();
    const stylesByScreenSize = {
        [ScreenSize.SMALL]: {
            barLenWidth: "130px",
            barLenHeight: "4px",
        },
        [ScreenSize.LARGE]: {
            barLenWidth: "160px",
            barLenHeight: "6px",
        },
        [ScreenSize.XLARGE]: {
            barLenWidth: "190px",
            barLenHeight: "6px",
        },
        [ScreenSize.XXLARGE]: {
            barLenWidth: "230px",
            barLenHeight: "8px",
        },
    };
    const styles = stylesByScreenSize[screenSize];
    const translations = {
        application: intlInstance.formatMessage({
            id: "ai_access.landing_page.application",
            defaultMessage: "applications",
        }),
        users: intlInstance.formatMessage({
            id: "ai_access.landing_page.users",
            defaultMessage: "users",
        }),
        all_other_cases_title: intlInstance.formatMessage({
            id: "ai_access.all_other_cases.title",
            defaultMessage: "Other Use Cases",
        }),
    };

    useEffect(() => {
        if (values?.length > 0) {
            setIsLoading(true);
            const setAppCountData = ({ data }) => {
                if (data?.length > 0) {
                    const [
                        {
                            total_applications,
                            total_users,
                            sanctioned_applications,
                            sanctioned_users,
                            tolerated_applications,
                            tolerated_users,
                            unsanctioned_applications,
                            unsanctioned_users,
                        },
                    ] = data;
                    setOtherUserAppCount({
                        totalUsers: total_users,
                        totalApplications: total_applications,
                        unsanctionedUsers: unsanctioned_users,
                        unsanctionedApplications: unsanctioned_applications,
                        sanctionedUsers: sanctioned_users,
                        sanctionedApplications: sanctioned_applications,
                        toleratedUsers: tolerated_users,
                        toleratedApplications: tolerated_applications,
                    });
                } else {
                    setOtherUserAppCount({
                        totalUsers: 0,
                        totalApplications: 0,
                        unsanctionedUsers: 0,
                        unsanctionedApplications: 0,
                        sanctionedUsers: 0,
                        sanctionedApplications: 0,
                        toleratedUsers: 0,
                        toleratedApplications: 0,
                    })
                }
            }

            fetchAndValidateCache(
                (args) => {
                    const { timeRange, values, refreshCache } = args;
                    return fetchAiOtherUseCasesUserCountData(timeRange, values, refreshCache)
                },
                { timeRange: timeRange, values: values, setData: setAppCountData, setLoading: setIsLoading }
            )
        }
    }, [timeRange, values]);

    if (loading || (isNil(data) || isEmpty(data)) || isLoading) {
        return (
            <AllOtherUseCasesLoading />
        );
    }

    const isZeroApplication = (
        otherUserAppCount.unsanctionedApplications === 0 &&
        otherUserAppCount.sanctionedApplications === 0 &&
        otherUserAppCount.totalApplications === 0);

    const isZeroUser = (
        otherUserAppCount.unsanctionedUsers === 0 &&
        otherUserAppCount.sanctionedUsers === 0 &&
        otherUserAppCount.toleratedUsers === 0);

    return (
        <>
            <div className="ai-access-all-other-case-pill-container">
                <div className="ai-access-all-other-case-leaf-top-banner-wrapper">
                    <div className="ai-access-all-other-case-leaf-top-banner">
                        {`${translations.all_other_cases_title}`}</div>
                </div>
                <div className="ai-access-all-other-case">
                    <div className="ai-access-all-other-case-single-bar-chart-containter">
                        <p className={"ai-access-all-other-case-single-stacked-bar-title"}>
                            <b>
                                {formatIntegersWithD3Fn(
                                    Number(otherUserAppCount.totalApplications ?? 0),
                                )}
                            </b>{" "}
                            {`${translations.application}`}
                        </p>
                        <SingleStackedBarChart
                            id="ai-access-single-bar-chart"
                            isZero={!isTagsAvailable || isZeroApplication}
                            style={{
                                container: { position: "relative", left: "12px", },
                                isZeroContainer: { width: styles.barLenWidth, },
                                isZeroBar: {
                                    width: styles.barLenWidth,
                                }
                            }}
                            data={[
                                {
                                    category: `${buttonGroup.sanctioned}`,
                                    color: "#33CCB8",
                                    label: `${buttonGroup.sanctioned}`,
                                    count: otherUserAppCount.sanctionedApplications,
                                },
                                {
                                    category: `${buttonGroup.tolerated}`,
                                    color: "#289EC9",
                                    label: `${buttonGroup.tolerated}`,
                                    count: otherUserAppCount.toleratedApplications,
                                },
                                {
                                    category: `${buttonGroup.unsanctioned}`,
                                    color: "#BC246A",
                                    label: `${buttonGroup.unsanctioned}`,
                                    count: otherUserAppCount.unsanctionedApplications,
                                },
                            ]}
                            barLenWidth={styles.barLenWidth}
                            barLenHeight={styles.barLenHeight}
                            showTooltip={true}
                            width={styles.barLenWidth}
                        />
                    </div>
                    <div className="ai-access-all-other-case-pill-containter">
                        {allOtherUseCases?.map((data, i) => (
                            <UseCasePill
                                key={data?.use_case ?? `all-other-case-${i}`}
                                data={data}
                                icon={useCases?.[data?.use_case_id]?.icon}
                                mostUsedApplication={data?.most_used_application}
                            />
                        ))}
                    </div>
                    <div className="ai-access-all-other-case-single-bar-chart-containter">
                        <p className={"ai-access-all-other-case-single-stacked-bar-title"}>
                            <b>
                                {formatIntegersWithD3Fn(
                                    Number(otherUserAppCount.totalUsers ?? 0),
                                )}
                            </b>{" "}
                            {`${translations.users}`}
                        </p>
                        <SingleStackedBarChart
                            id="ai-access-single-bar-chart"
                            isZero={!isTagsAvailable || isZeroUser}
                            style={{
                                container: {
                                    position: "relative", left: "12px"
                                },
                                isZeroContainer: {
                                    width: styles.barLenWidth,
                                },
                                isZeroBar: {
                                    width: styles.barLenWidth,
                                }
                            }}
                            data={[
                                {
                                    category: `${buttonGroup.sanctioned}`,
                                    color: "#33CCB8",
                                    label: `${buttonGroup.sanctioned}`,
                                    count: otherUserAppCount.sanctionedUsers,
                                },
                                {
                                    category: `${buttonGroup.tolerated}`,
                                    color: "#289EC9",
                                    label: `${buttonGroup.tolerated}`,
                                    count: otherUserAppCount.toleratedUsers,
                                },
                                {
                                    category: `${buttonGroup.unsanctioned}`,
                                    color: "#BC246A",
                                    label: `${buttonGroup.unsanctioned}`,
                                    count: otherUserAppCount.unsanctionedUsers,
                                },
                            ]}
                            barLenWidth={styles.barLenWidth}
                            showTooltip={true}
                            width={styles.barLenWidth}
                            barLenHeight={styles.barLenHeight}
                        />
                    </div>
                </div>
            </div>
        </>
    );
};

export default AllOtherUseCases;
