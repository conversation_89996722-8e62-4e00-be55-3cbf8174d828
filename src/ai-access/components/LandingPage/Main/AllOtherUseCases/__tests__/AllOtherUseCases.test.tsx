import { render, screen, waitFor } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { useSelector } from "react-redux";
import AllOtherUseCases from "../AllOtherUseCases"; // Adjust the import as necessary
import { fetchAndValidateCache } from "../../../../../redux/sase-backend-server/fetchCache";
import configureStore from "redux-mock-store";

// Mock external dependencies and components
vi.mock("react-redux", () => ({
    useSelector: vi.fn(),
}));

vi.mock("../../../../../redux/sase-backend-server/fetchCache", () => ({
    fetchAndValidateCache: vi.fn(),
}));

vi.mock("../../../../ReusableComponents/SingleStackedBarChart", () => ({
    default: () => <div data-testid="bar-chart"></div>,
}));

vi.mock("../AllOtherUseCasesLoading", () => ({
    default: () => <div>Loading...</div>,
}));

vi.mock("../UseCasePill", () => ({
    default: ({ data }) => <div>{data.use_case}</div>,
}));

// Mock the useScreenSize hook as well
vi.mock("../../../../hooks/useScreenSize", () => ({
    useScreenSize: vi.fn(),
}));

describe("AllOtherUseCases", () => {
    const mockData = {
        allOtherUseCases: [
            { use_case: "UseCase 1", use_case_id: 1, most_used_application: "App1" },
            { use_case: "UseCase 2", use_case_id: 2, most_used_application: "App2" },
        ],
    };

    beforeEach(() => {
        useSelector.mockImplementation((callback) =>
            callback({
                ai: {
                    topUseCases: {
                        default: mockData,
                    },
                },
                filterServer: {
                    timeRange: "last_30_days",
                },
            })
        );
    });

    it("should render loading state when data is loading", () => {
        useSelector.mockImplementationOnce(() => ({
            ai: {
                topUseCases: {
                    default: { loading: true, data: null, error: null },
                },
            },
        }));

        render(<AllOtherUseCases />);
        expect(screen.getByText("Loading...")).toBeInTheDocument();
    });

    it("should handle empty data gracefully", async () => {
        useSelector.mockImplementationOnce(() => ({
            ai: {
                topUseCases: {
                    default: { loading: false, data: { allOtherUseCases: [] }, error: null },
                },
            },
        }));

        render(<AllOtherUseCases />);
        expect(screen.queryByText("UseCase 1")).not.toBeInTheDocument();
        expect(screen.queryByText("UseCase 2")).not.toBeInTheDocument();
    });
});
