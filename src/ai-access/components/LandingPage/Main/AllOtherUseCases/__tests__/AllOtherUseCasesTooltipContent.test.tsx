import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import AllOtherUseCasesTooltipContent from "../AllOtherUseCasesTooltipContent";
import * as Utils from "../../../../../Utils";
import * as translate from "../../../../../../netsec-ui/utils/i18n/translate";

vi.mock("../../../../../Utils");

vi.mock("../../../../../../netsec-ui/utils/i18n/translate");

vi.mock("../../../ReusableComponents/SingleStackedBarChart", () => ({
    default: ({ id }) => <div data-testid={id} />
}));
// Mock Tailwind CSS utilities
vi.mock("@panwds/tailwindcss", async (importOriginal) => {
    const actual = await importOriginal();
    return {
        ...actual,
        twMerge: vi.fn((...classes) => classes.join(" ")),
        tw: actual.tw,
    };
});
describe("AllOtherUseCasesTooltipContent", () => {
    const defaultProps = {
        icon: "test-icon.svg",
        mostUsedApplication: {
            application_name: "Test App",
            icon: "test-app-icon.svg",
            application_sub_type: "default",
        },
        data: {
            use_case: "Test Use Case",
            total_applications: 100,
            sanctioned_applications: 50,
            tolerated_applications: 30,
            unsanctioned_applications: 20,
            total_users: 1000,
            sanctioned_users: 500,
            tolerated_users: 300,
            unsanctioned_users: 200,
        },
    };

    beforeEach(() => {
        vi.mocked(Utils.getTag).mockReturnValue({
            sanctioned: "Sanctioned",
            tolerated: "Tolerated",
            unsanctioned: "Unsanctioned",
        });
        vi.mocked(Utils.tagsAvailable).mockReturnValue(true);
        vi.mocked(translate.formatIntegersWithD3Fn).mockImplementation((num) => num.toString());
    });

    it("renders the component with all data", () => {
        render(<AllOtherUseCasesTooltipContent {...defaultProps} />);
        
        expect(screen.getByText("Test Use Case")).toBeInTheDocument();
        expect(screen.getByText("Most Used Application")).toBeInTheDocument();
        expect(screen.getByText("Test App")).toBeInTheDocument();
        
        const applicationElement = screen.getByText((content, element) => {
            return element?.classList.contains("ai-access-all-other-case-application-title") &&
                 element?.textContent?.includes("100") &&
                 element?.textContent?.includes("applications");
        });
        expect(applicationElement).toBeInTheDocument();
        
        const userElement = screen.getByText((content, element) => {
            return element?.classList.contains("ai-access-all-other-case-application-title") &&
                 element?.textContent?.includes("1000") &&
                 element?.textContent?.includes("users");
        });
        expect(userElement).toBeInTheDocument();
        
        expect(screen.getByText("50 | 30 | 20")).toBeInTheDocument();
        expect(screen.getByText("500 | 300 | 200")).toBeInTheDocument();
    });

    it("handles missing data gracefully", () => {
        const propsWithMissingData = {
            ...defaultProps,
            data: {},
            mostUsedApplication: {},
        };
        render(<AllOtherUseCasesTooltipContent {...propsWithMissingData} />);
      
        const applicationElement = screen.getByText((content, element) => {
            return element?.classList.contains("ai-access-all-other-case-application-title") &&
                   element?.textContent?.includes("0") &&
                   element?.textContent?.includes("applications");
        });
        expect(applicationElement).toBeInTheDocument();
      
        const userElement = screen.getByText((content, element) => {
            return element?.classList.contains("ai-access-all-other-case-application-title") &&
                   element?.textContent?.includes("0") &&
                   element?.textContent?.includes("users");
        });
        expect(userElement).toBeInTheDocument();
    });

    it("does not render tag counts when tags are not available", () => {
        vi.mocked(Utils.tagsAvailable).mockReturnValue(false);
        render(<AllOtherUseCasesTooltipContent {...defaultProps} />);
    
        expect(screen.queryByText("50 | 30 | 20")).not.toBeInTheDocument();
        expect(screen.queryByText("500 | 300 | 200")).not.toBeInTheDocument();
    });

    it("renders SingleStackedBarChart components", () => {
        render(<AllOtherUseCasesTooltipContent {...defaultProps} />);
      
        const barCharts = screen.getAllByRole("generic", { id: "ai-access-single-bar-chart" });
        expect(barCharts).toHaveLength(23);
        expect(barCharts.length).toBeGreaterThanOrEqual(2);
    });
});