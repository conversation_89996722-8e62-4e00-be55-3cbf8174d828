import React from "react";
import { describe, expect, it, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import { MemoryRouter } from "@sparky/framework/router";
import UseCasePill from "../UseCasePill";

// Mock Tailwind CSS utilities
vi.mock("@panwds/tailwindcss", async (importOriginal) => {
    const actual = await importOriginal();
    return {
        ...actual,
        twMerge: vi.fn((...classes) => classes.join(" ")),
        tw: actual.tw,
    };
});

describe("UseCasePill", () => {
    const mockIcon = "test-icon.svg";
    const mockData = {
        use_case_id: "test-id",
        some_value: 10,
    };
    const mockMostUsedApplication = {
        name: "Test App",
        count: 5,
    };

    const renderWithRouter = (ui: React.ReactElement) => {
        return render(
            <MemoryRouter>
                {ui}
            </MemoryRouter>
        );
    };

    it("renders zero state when all values are zero", () => {
        const zeroData = { use_case_id: "zero-id", some_value: 0 };
        renderWithRouter(<UseCasePill data={zeroData} icon={mockIcon} />);
    
        const pillElement = screen.getByAltText("use case icon");
        expect(pillElement).toBeInTheDocument();
        expect(pillElement.closest("div")).toHaveClass("ai-access-all-other-use-case-pill-zero-state");
    });

    it("renders non-zero state with tooltip and link", () => {
        renderWithRouter(
            <UseCasePill data={mockData} icon={mockIcon} mostUsedApplication={mockMostUsedApplication} />
        );
    
        const pillElement = screen.getByAltText("use case icon");
        expect(pillElement).toBeInTheDocument();
        expect(pillElement.closest("div")).toHaveClass("ai-access-all-other-use-case-pill");
        expect(pillElement.closest("div")).not.toHaveClass("ai-access-all-other-use-case-pill-zero-state");
    
        const linkElement = screen.getByRole("link");
        expect(linkElement).toHaveAttribute("href", "/insights/ai-access/use-case/test-id");
    });

    it("renders the correct icon", () => {
        renderWithRouter(<UseCasePill data={mockData} icon={mockIcon} />);
    
        const iconElement = screen.getByAltText("use case icon");
        expect(iconElement).toHaveAttribute("src", mockIcon);
    });

    it("applies correct CSS classes", () => {
        renderWithRouter(<UseCasePill data={mockData} icon={mockIcon} />);
    
        const pillElement = screen.getByAltText("use case icon");
        expect(pillElement).toHaveClass("ai-access-all-other-use-case-leaf-content-title-image");
    });

    it("renders tooltip with correct content", () => {
        renderWithRouter(
            <UseCasePill data={mockData} icon={mockIcon} mostUsedApplication={mockMostUsedApplication} />
        );
    
        const tooltipTrigger = screen.getByRole("link");
        expect(tooltipTrigger).toBeInTheDocument();
    });
});