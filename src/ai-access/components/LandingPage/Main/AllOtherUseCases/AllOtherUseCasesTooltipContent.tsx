import React from "react";
import IntlUtil from "../../../../../netsec-ui/utils/i18n/IntlUtil";
import { getTag, tagsAvailable } from "../../../../Utils";
import SingleStackedBarChart from "../../../ReusableComponents/SingleStackedBarChart";
import "./AllOtherUseCasesTooltipContent.scss"
import EmptyStateAppIcon from "../../../../images/appImages/dark/empty-state-app-icon-dark-theme.svg";
import { formatIntegersWithD3Fn } from "../../../../../netsec-ui/utils/i18n/translate";
export interface AllOtherUseCasesTooltipContentProps {
    icon: string;
    mostUsedApplication: Record<string, unknown>;
    data: Record<string, unknown>;
}
const AllOtherUseCasesTooltipContent: React.FC<AllOtherUseCasesTooltipContentProps> = (props) => {
    const intlInstance = IntlUtil.getIntl();
    const buttonGroup = getTag();
    const isTagsAvailable = tagsAvailable();
    const translations = {
        application: intlInstance.formatMessage({
            id: "ai_access.landing_page.application",
            defaultMessage: "applications",
        }),
        users: intlInstance.formatMessage({
            id: "ai_access.landing_page.users",
            defaultMessage: "users",
        }),
        mostUsedApplication: intlInstance.formatMessage({
            id: "ai_access.top_left_leaf.tooltip.most_used_application",
            defaultMessage: "Most Used Application",
        }),
    }

    const { icon, mostUsedApplication, data } = props;
    return (
        <div className="ai-access-all-other-case-tooltip-container">
            <div className="ai-access-all-other-case-tooltip-title-container">
                <img
                    className="ai-access-all-other-use-case-tooltip-content-title-image"
                    //@ts-ignore
                    src={icon}
                />
                <p className="ai-access-all-other-case-tooltip-title">{data?.use_case}</p>
            </div>
            <p className="ai-access-tooltip-most-used-app-title">{translations.mostUsedApplication}</p>
            <div className="ai-access-all-other-case-most-used-app-title-container">
                <div className={`ai-access-all-other-case-tooltip-icon-container  ai-access-tooltip-title-${mostUsedApplication?.application_sub_type || "default"}`}>
                    <img
                        className="ai-access-all-other-case-tooltip-title-image"
                        //@ts-ignore
                        src={mostUsedApplication?.icon ?? EmptyStateAppIcon}
                    />
                </div>
                <p className="ai-access-all-other-case-most-used-app-title">{mostUsedApplication?.application_name}</p>
            </div>
            <div className="ai-access-all-other-case-application-container">
                <p className="ai-access-all-other-case-application-title">
                    <b>{formatIntegersWithD3Fn(Number(data?.total_applications || 0))}</b> {`${translations.application}`}
                </p>
                <SingleStackedBarChart
                    id="ai-access-single-bar-chart"
                    isZero={
                        (data?.unsanctioned_applications === 0 &&
                        data?.sanctioned_applications === 0 &&
                        data?.tolerated_applications === 0) || !isTagsAvailable
                    }
                    data={[
                        {
                            category: `${buttonGroup.sanctioned}`,
                            color: "#33CCB8",
                            label: `${buttonGroup.sanctioned}`,
                            count: data?.sanctioned_applications as number || 0,
                        },
                        {
                            category: `${buttonGroup.tolerated}`,
                            color: "#289EC9",
                            label: `${buttonGroup.tolerated}`,
                            count: data?.tolerated_applications as number || 0,
                        },
                        {
                            category: `${buttonGroup.unsanctioned}`,
                            color: "#BC246A",
                            label: `${buttonGroup.unsanctioned}`,
                            count: data?.unsanctioned_applications as number || 0,
                        },
                    ]}
                    barLenWidth="130px"
                    showTooltip={true}
                    width="130px"
                    height="4px"
                />
            </div>
            {isTagsAvailable && <p className="ai-access-all-other-case-application-count">
                {data?.sanctioned_applications as number || 0} | {data?.tolerated_applications as number || 0} | {data?.unsanctioned_applications as number || 0}
            </p>}
            <div className="ai-access-all-other-case-application-container">
                <p className="ai-access-all-other-case-application-title">
                    <b>{formatIntegersWithD3Fn(Number(data?.total_users || 0))}</b> {`${translations.users}`}
                </p>
                <SingleStackedBarChart
                    id="ai-access-single-bar-chart"
                    isZero={
                        (data?.unsanctioned_users === 0 &&
                        data?.sanctioned_users === 0 &&
                        data?.tolerated_users === 0) || !isTagsAvailable
                    }
                    data={[
                        {
                            category: `${buttonGroup.sanctioned}`,
                            color: "#33CCB8",
                            label: `${buttonGroup.sanctioned}`,
                            count: data?.sanctioned_users as number || 0,
                        },
                        {
                            category: `${buttonGroup.tolerated}`,
                            color: "#289EC9",
                            label: `${buttonGroup.tolerated}`,
                            count: data?.tolerated_users as number || 0,
                        },
                        {
                            category: `${buttonGroup.unsanctioned}`,
                            color: "#BC246A",
                            label: `${buttonGroup.unsanctioned}`,
                            count: data?.unsanctioned_users as number || 0,
                        },
                    ]}
                    barLenWidth="130px"
                    showTooltip={true}
                    width="130px"
                    height="4px"
                />
            </div>
            {isTagsAvailable && <p className="ai-access-all-other-case-application-count">
                {data?.sanctioned_users as number || 0} | {data?.tolerated_users as number || 0} | {data?.unsanctioned_users as number || 0}
            </p>}
        </div>
    )
}

export default AllOtherUseCasesTooltipContent;
