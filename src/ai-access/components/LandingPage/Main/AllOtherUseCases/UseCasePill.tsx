import React from "react";
import { Tooltip } from "@panwds/react-ui";
import AllOtherUseCasesTooltipContent from "./AllOtherUseCasesTooltipContent";
import { Link } from "@sparky/framework/router";

type UseCasePillProps = {
  data: Record<string, unknown>;
  icon: string;
  mostUsedApplication?: Record<string,unknown>;
};

// Helper function to check if all numerical values are zero
const isAllValuesZero = (data: Record<string, unknown>): boolean => {
    const numericValues = Object.values(data).filter(
        (value) => typeof value === "number"
    );
  
    return numericValues.every((value) => value === 0);
};
  
const UseCasePill: React.FC<UseCasePillProps> = ({ data, icon, mostUsedApplication }) => {
    const tooltipDisabled = isAllValuesZero(data);
  
    if (tooltipDisabled) {
        return (
            <div className="ai-access-all-other-use-case-pill ai-access-all-other-use-case-pill-zero-state">
                <img
                    className="ai-access-all-other-use-case-leaf-content-title-image"
                    src={icon}
                    alt="use case icon"
                />
            </div>
        );
    }
  
    return (
        <Tooltip
            defaultPlacement={"top-start"}
            singleLine={false}
            label={
                <AllOtherUseCasesTooltipContent
                    icon={icon}
                    data={data}
                    mostUsedApplication={mostUsedApplication}
                />
            }
            addClassName={"ai-access-tooltip-container-background"}
        >
            <Link to={`/insights/ai-access/use-case/${data?.use_case_id}`}>
                <div className="ai-access-all-other-use-case-pill">
                    <img
                        className="ai-access-all-other-use-case-leaf-content-title-image"
                        src={icon}
                        alt="use case icon"
                    />
                </div>
            </Link>
        </Tooltip>
    );
};

export default UseCasePill;
