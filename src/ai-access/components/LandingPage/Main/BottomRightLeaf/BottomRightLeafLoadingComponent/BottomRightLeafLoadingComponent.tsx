import React from "react";
import ContentLoader from "react-content-loader";
import { ScreenSize } from "../../../../../constants/screenSizes";
import { useScreenSize } from "../../../../../hooks/useScreenSize";

export interface BottomRightLeafLoadingComponentProps {
    themeMode: string;
}

const BottomRightLeafLoadingComponent: React.FC<BottomRightLeafLoadingComponentProps> = ({ themeMode }) => {
    const { size: screenSize } = useScreenSize();

    const baseLoaderProps = {
        width: 400,
        height: 160,
        viewBox: "0 0 400 160",
        rectWidth: 150,
        rectHeight: 15,
        circleR: 44,
    };

    const loaderProps = {
        [ScreenSize.SMALL]: {
            ...baseLoaderProps,
            circleX: 275, circleY: 90,
            rect1: { x: 90, y: 20 },
            rect2: { x: 60, y: 70 },
            rect3: { x: 30, y: 120 },
        },
        [ScreenSize.LARGE]: {
            ...baseLoaderProps,
            circleX: 310, circleY: 80,
            rect1: { x: 110, y: 20 },
            rect2: { x: 80, y: 70 },
            rect3: { x: 50, y: 120 },
        },
        [ScreenSize.XLARGE]: {
            ...baseLoaderProps,
            width: 450,
            viewBox: "0 0 450 160",
            rectWidth: 200,
            rectHeight: 20,
            circleR: 54,
            circleX: 380, circleY: 80,
            rect1: { x: 110, y: 20 },
            rect2: { x: 90, y: 80 },
            rect3: { x: 60, y: 140 },
        },
        [ScreenSize.XXLARGE]: {
            ...baseLoaderProps,
            width: 740,
            height: 300,
            viewBox: "0 0 740 300",
            rectWidth: 300,
            rectHeight: 30,
            circleR: 74,
            circleX: 640, circleY: 150,
            rect1: { x: 220, y: 20 },
            rect2: { x: 150, y: 130 },
            rect3: { x: 100, y: 240 },
        },
    };

    const currentLoaderProps = loaderProps[screenSize];
    const backgroundColor = themeMode === "dark" ? "#484848" : "#B8B8B8";

    return (
        <div className="ai-access-leaf-base-wrapper-loading-bottom-right">
            <div className="ai-access-leaf-base-container ai-access-leaf-base-container-loading-bottom-right">
                <ContentLoader
                    speed={1}
                    width={currentLoaderProps.width}
                    height={currentLoaderProps.height}
                    viewBox={currentLoaderProps.viewBox}
                    backgroundColor={backgroundColor}
                    foregroundColor="#ecebeb"
                >
                    {["rect1", "rect2", "rect3"].map((rectKey) => (
                        <rect
                            key={rectKey}
                            x={currentLoaderProps[rectKey].x}
                            y={currentLoaderProps[rectKey].y}
                            rx="5"
                            ry="5"
                            width={currentLoaderProps.rectWidth}
                            height={currentLoaderProps.rectHeight}
                        />
                    ))}
                    <circle
                        cx={currentLoaderProps.circleX}
                        cy={currentLoaderProps.circleY}
                        r={currentLoaderProps.circleR}
                    />
                </ContentLoader>
            </div>
        </div>
    );
};

export default BottomRightLeafLoadingComponent;
