import React from "react";
import { useSelector } from "react-redux";
import LeafBaseComponent from "../../../ReusableComponents/LeafBaseComponent";
import { emptyStateLandingPageIcon, getConstant, getTag, isNil, tagsAvailable } from "../../../../Utils";
import { useThemeContext } from "../../../../../netsec-ui/utils/ThemeProvider";
import LeafComponentTooltipContent from "../../../ReusableComponents/LeafComponentTooltipContent"
import "./BottomRightLeaf.scss";
import { isEmpty } from "../../../../../netsec-ui/utils/NetsecUIUtils";
import BottomRightLeafLoadingComponent from "./BottomRightLeafLoadingComponent";
import { ScreenSize } from "../../../../constants/screenSizes";
import { useScreenSize } from "../../../../hooks/useScreenSize";
export interface BottomRightLeafProps {
}

const BottomRightLeaf: React.FC<BottomRightLeafProps> = (props) => {
    const { size: screenSize } = useScreenSize();
    //@ts-ignore
    const { loading, data, error } = useSelector((state) => state?.ai?.topUseCases?.default ?? {});
    const fourthUseCase = data?.fourthUseCase;
    const mostUsedApplication = fourthUseCase?.most_used_application || {};
    const buttonGroup = getTag();
    const themeMode = useThemeContext()?.mode;
    const constants = getConstant();

    const isTagsAvailable = tagsAvailable();

    const isZeroApplication = (
        fourthUseCase?.sanctioned_applications === 0 &&
        fourthUseCase?.unsanctioned_applications === 0 &&
        fourthUseCase?.tolerated_applications === 0);

    const isZeroUser = (
        fourthUseCase?.unsanctioned_users === 0 &&
        fourthUseCase?.sanctioned_users === 0 &&
        fourthUseCase?.tolerated_users === 0);

    let count = 0;
    if (mostUsedApplication.enterprise_plan_offered === "true") {
        count++;
    }
    if (mostUsedApplication.data_used_in_models === "true") {
        count++;
    }
    const stylesByScreenSize = {
        [ScreenSize.SMALL]: {
            barLenWidth: "160px",
            applicationContainerStyle: { left: "4rem" },
            usersContainerStyle: { left: "2.7rem" },
            zeroApplicationContainerStyle: { left: "3.7rem" },
            zeroUsersContainerStyle: { left: "2.9rem" },
            barLenHeight: "6px"
        },
        [ScreenSize.LARGE]: {
            barLenWidth: "180px",
            applicationContainerStyle: { left: "5.5rem" },
            usersContainerStyle: { left: "3.5rem" },
            zeroApplicationContainerStyle: { left: "4.7rem" },
            zeroUsersContainerStyle: { left: "3.3rem" },
            barLenHeight: "6px"
        },
        [ScreenSize.XLARGE]: {
            barLenWidth: "220px",
            applicationContainerStyle: { left: "6.2rem" },
            usersContainerStyle: { left: "4.3rem" },
            zeroApplicationContainerStyle: { left: "5.7rem" },
            zeroUsersContainerStyle: { left: "4.3rem" },
            barLenHeight: "8px"
        },
        [ScreenSize.XXLARGE]: {
            barLenWidth: "350px",
            applicationContainerStyle: { left: "9rem" },
            usersContainerStyle: { left: "6.5rem" },
            zeroApplicationContainerStyle: { left: "8.7rem" },
            zeroUsersContainerStyle: { left: "6.3rem" },
            barLenHeight: "9px"
        },
    };

    const styles = stylesByScreenSize[screenSize];

    if ((isNil(data) || isEmpty(data)) || loading) {
        return (
            <BottomRightLeafLoadingComponent
                themeMode={themeMode}
            />
        );
    }

    if (isZeroApplication && isZeroUser) {
        return (
            <LeafBaseComponent
                icon={fourthUseCase?.most_used_application?.icon ?? emptyStateLandingPageIcon(themeMode)}
                loading={loading}
                useCaseId={fourthUseCase?.use_case_id}
                position={"bottom-right"}
                tooltip={<LeafComponentTooltipContent mostUsedApplication={mostUsedApplication} useCase={fourthUseCase?.use_case}/>}
                isZero={isZeroApplication && isZeroUser}
                data={{
                    topBannerText: "4th",
                    id: fourthUseCase?.use_case_id,
                    contentTitle: fourthUseCase?.use_case,
                    iconText: fourthUseCase?.most_used_application?.application_name,
                    label: !isTagsAvailable ? "" : mostUsedApplication.application_sub_type,
                    singleStackedBar: [
                        {
                            stackedCount: fourthUseCase?.total_applications || 0,
                            stackedType: constants.application,
                            isZero: isZeroApplication,
                            stackedData: [
                                {
                                    category: `${buttonGroup.sanctioned}`,
                                    color: "#33CCB8",
                                    label: buttonGroup.sanctioned,
                                    count: fourthUseCase?.sanctioned_applications || 0,
                                },
                                {
                                    category: `${buttonGroup.tolerated}`,
                                    color: "#289EC9",
                                    label: buttonGroup.tolerated,
                                    count: fourthUseCase?.tolerated_applications || 0,
                                },
                                {
                                    category: `${buttonGroup.unsanctioned}`,
                                    color: "#BC246A",
                                    label: buttonGroup.unsanctioned,
                                    count: fourthUseCase?.unsanctioned_applications || 0,
                                },
                            ],
                            showTooltip: true,
                            barLenHeight: styles.barLenHeight,
                            style: {
                                container: {
                                    position: "relative",
                                    ...styles.zeroApplicationContainerStyle
                                },
                                isZeroContainer: {
                                    width: styles.barLenWidth,
                                },
                                isZeroBar: {
                                    width: styles.barLenWidth,
                                }
                            },
                        },
                        {
                            stackedCount: fourthUseCase?.total_users,
                            stackedType: "users",
                            isZero: isZeroUser,
                            stackedData: [
                                {
                                    category: `${buttonGroup.sanctioned}`,
                                    color: "#33CCB8",
                                    label: buttonGroup.sanctioned,
                                    count: fourthUseCase?.sanctioned_users || 0,
                                },
                                {
                                    category: `${buttonGroup.tolerated}`,
                                    color: "#289EC9",
                                    label: buttonGroup.tolerated,
                                    count: fourthUseCase?.tolerated_users || 0,
                                },
                                {
                                    category: `${buttonGroup.unsanctioned}`,
                                    color: "#BC246A",
                                    label: buttonGroup.unsanctioned,
                                    count: fourthUseCase?.unsanctioned_users || 0,
                                },
                            ],
                            barLenWidth: "160px",
                            barLenHeight: styles.barLenHeight,
                            showTooltip: true,
                            style: {
                                container: {
                                    position: "relative",
                                    ...styles.zeroUsersContainerStyle,
                                },
                                isZeroContainer: {
                                    width: styles.barLenWidth,
                                },
                                isZeroBar: {
                                    width: styles.barLenWidth,
                                }
                            },
                        },
                    ],
                }}
                error={error}
            />
        )
    }

    return (
        <LeafBaseComponent
            icon={fourthUseCase?.most_used_application?.icon ?? emptyStateLandingPageIcon(themeMode)}
            loading={loading}
            useCaseId={fourthUseCase?.use_case_id}
            position={"bottom-right"}
            tooltip={<LeafComponentTooltipContent mostUsedApplication={mostUsedApplication} useCase={fourthUseCase?.use_case} />}
            data={{
                topBannerText: "4th",
                id: fourthUseCase?.use_case_id,
                contentTitle: fourthUseCase?.use_case,
                iconText: fourthUseCase?.most_used_application?.application_name,
                label: !isTagsAvailable ? "" : mostUsedApplication.application_sub_type,
                singleStackedBar: [
                    {
                        stackedCount: fourthUseCase?.total_applications || 0,
                        stackedType: constants.application,
                        isZero: !isTagsAvailable || isZeroApplication,
                        stackedData: [
                            {
                                category: `${buttonGroup.sanctioned}`,
                                color: "#33CCB8",
                                label: buttonGroup.sanctioned,
                                count: fourthUseCase?.sanctioned_applications || 0,
                            },
                            {
                                category: `${buttonGroup.tolerated}`,
                                color: "#289EC9",
                                label: buttonGroup.tolerated,
                                count: fourthUseCase?.tolerated_applications || 0,
                            },
                            {
                                category: `${buttonGroup.unsanctioned}`,
                                color: "#BC246A",
                                label: buttonGroup.unsanctioned,
                                count: fourthUseCase?.unsanctioned_applications || 0,
                            },
                        ],
                        barLenWidth: styles.barLenWidth,
                        barLenHeight: styles.barLenHeight,
                        showTooltip: true,
                        style: {
                            container: {
                                position: "relative",
                                ...styles.applicationContainerStyle,
                            },
                        },
                    },
                    {
                        stackedCount: fourthUseCase?.total_users,
                        stackedType: "users",
                        isZero: !isTagsAvailable || isZeroUser,
                        stackedData: [
                            {
                                category: `${buttonGroup.sanctioned}`,
                                color: "#33CCB8",
                                label: buttonGroup.sanctioned,
                                count: fourthUseCase?.sanctioned_users || 0,
                            },
                            {
                                category: `${buttonGroup.tolerated}`,
                                color: "#289EC9",
                                label: buttonGroup.tolerated,
                                count: fourthUseCase?.tolerated_users || 0,
                            },
                            {
                                category: `${buttonGroup.unsanctioned}`,
                                color: "#BC246A",
                                label: buttonGroup.unsanctioned,
                                count: fourthUseCase?.unsanctioned_users || 0,
                            },
                        ],
                        barLenWidth: styles.barLenWidth,
                        barLenHeight: styles.barLenHeight,
                        showTooltip: true,
                        style: {
                            container: {
                                position: "relative",
                                ...styles.usersContainerStyle,
                            },
                        },
                    },
                ],
            }}
            error={error}
        />
    );
};

export default BottomRightLeaf;
