.tw-dark {
    .ai-access-leaf-base-container-bottom-left {
        background-image: url("../../../../images/appImages/dark/left-bottom-leaf-dark-theme.svg");
    }

    .ai-access-leaf-base-container-loading-bottom-left {
        background-image: url("../../../../images/appImages/dark/left-bottom-leaf-dark-theme-loading.svg");
    }

    .ai-access-leaf-top-banner-bottom-left {
        border-radius: 100px;
        background-color: #242424;
        color: #F4F5F5;
    }
}

.ai-access-leaf-base-wrapper-loading-bottom-left {
    position: absolute;
    top: 12.5rem;
    right: 22rem;

    @media (min-width: 2000px) {
        position: absolute;
        top: 18.5rem;
        right: 28rem;
    }

    @media (min-width: 2560px) {
        position: absolute;
        top: 21.5rem;
        right: 32rem;
    }

    @media (min-width: 3840px) {
        position: absolute;
        top: 31.5rem;
        right: 40.5rem;
    }
}

.ai-access-leaf-base-wrapper-bottom-left {
    position: absolute;
    top: 11.5rem;
    right: 22rem;

    @media (min-width: 2000px) {
        position: absolute;
        top: 17.5rem;
        right: 27rem;
    }

    @media (min-width: 2560px) {
        position: absolute;
        top: 20.5rem;
        right: 31rem;
    }

    @media (min-width: 3840px) {
        position: absolute;
        top: 30.5rem;
        right: 40.5rem;
    }
}

.ai-access-leaf-base-container-bottom-left {
    background-image: url("../../../../images/appImages/light/left-bottom-leaf-light-theme.svg");
}

.ai-access-leaf-base-container-loading-bottom-left {
    background-image: url("../../../../images/appImages/light/left-bottom-leaf-light-theme-loading.svg");
}

.ai-access-leaf-content-container-bottom-left {
    display: flex;
    flex-direction: row;

    @media (min-width: 3840px) {
        height: 180px;
    }
}

.ai-access-leaf-icon-wrapper-bottom-left {
    @media (min-width: 3840px) {
        margin-right: 4rem;
    }
}

.ai-access-leaf-top-banner-bottom-left {
    left: 8rem;
    width: 40px;
    background-color: #FFFFFF;

    @media (min-width: 2000px) {
        left: 10rem;
        width: 50px;
        height: 18px;
    }

    @media (min-width: 2560px) {
        width: 60px;
        left: 12rem;
    }

    @media (min-width: 3840px) {
        width: 70px;
        left: 14rem;
        height: 30px;
        line-height: 26px;
    }
}

.ai-access-single-stacked-bar-container-bottom-left {
    padding-bottom: 0.5rem;
}

.ai-access-leaf-content-title-bottom-left {
    position: relative;
    right: 5.5rem;
    top: .5rem;
    text-align: end;

    @media (min-width: 2000px) {
        right: 6.5rem;
    }

    @media (min-width: 2560px) {
        top: 1rem;
        right: 8.5rem;
    }

    @media (min-width: 3840px) {
        right: 13.5rem;
    }
}

.ai-access-leaf-description-container .ai-access-single-stacked-bar-container-bottom-left:nth-child(2) .ai-access-single-stacked-bar-title-bottom-left {
    position: relative;
    right: 3.2rem;

    @media (min-width: 2000px) {
        right: 4.5rem;
    }

    @media (min-width: 2560px) {
        right: 4.4rem;
    }

    @media (min-width: 3840px) {
        right: 7.4rem;
    }
}

.ai-access-single-stacked-bar-title-bottom-left {
    position: relative;
    right: 4.2rem;
    text-align: right;

    @media (min-width: 2000px) {
        right: 6rem;
    }

    @media (min-width: 2560px) {
        right: 5.8rem;
    }

    @media (min-width: 3840px) {
        right: 9rem;
    }
}

.ai-access-leaf-content-review-case-bottom-left {
    position: relative;
    bottom: 0.5rem;
    text-align: end;
    right: 2rem;

    @media (min-width: 3840px) {
        right: 3rem;
    }
}
