import React from "react";
import ContentLoader from "react-content-loader";
import { ScreenSize } from "../../../../../constants/screenSizes";
import { useScreenSize } from "../../../../../hooks/useScreenSize";

export interface BottomLeftLeafLoadingComponentProps {
  themeMode: string;
}

const BottomLeftLeafLoadingComponent: React.FC<BottomLeftLeafLoadingComponentProps> = ({ themeMode }) => {
    const { size: screenSize } = useScreenSize();

    const baseLoaderProps = {
        width: 400,
        height: 160,
        viewBox: "0 0 400 160",
        rectWidth: 150,
        rectHeight: 15,
        circleR: 44,
    };

    const loaderProps = {
        [ScreenSize.SMALL]: {
            ...baseLoaderProps,
            circleX: 70, circleY: 90,
            rect1: { x: 100, y: 20 },
            rect2: { x: 130, y: 70 },
            rect3: { x: 160, y: 120 },
        },
        [ScreenSize.LARGE]: {
            ...baseLoaderProps,
            circleX: 90, circleY: 80,
            rect1: { x: 140, y: 20 },
            rect2: { x: 170, y: 70 },
            rect3: { x: 200, y: 120 },
        },
        [ScreenSize.XLARGE]: {
            ...baseLoaderProps,
            width: 450,
            viewBox: "0 0 450 160",
            rectWidth: 200,
            rectHeight: 20,
            circleR: 54,
            circleX: 90, circleY: 80,
            rect1: { x: 160, y: 20 },
            rect2: { x: 190, y: 80 },
            rect3: { x: 220, y: 140 },
        },
        [ScreenSize.XXLARGE]: {
            ...baseLoaderProps,
            width: 740,
            height: 300,
            viewBox: "0 0 740 300",
            rectWidth: 300,
            rectHeight: 30,
            circleR: 74,
            circleX: 150, circleY: 150,
            rect1: { x: 260, y: 10 },
            rect2: { x: 310, y: 120 },
            rect3: { x: 360, y: 230 },
        },
    };

    const currentLoaderProps = loaderProps[screenSize];
    const backgroundColor = themeMode === "dark" ? "#484848" : "#B8B8B8";

    return (
        <div className="ai-access-leaf-base-wrapper-loading-bottom-left">
            <div className="ai-access-leaf-base-container ai-access-leaf-base-container-loading-bottom-left">
                <ContentLoader
                    speed={1}
                    width={currentLoaderProps.width}
                    height={currentLoaderProps.height}
                    viewBox={currentLoaderProps.viewBox}
                    backgroundColor={backgroundColor}
                    foregroundColor="#ecebeb"
                >
                    <circle
                        cx={currentLoaderProps.circleX}
                        cy={currentLoaderProps.circleY}
                        r={currentLoaderProps.circleR}
                    />
                    {["rect1", "rect2", "rect3"].map((rectKey) => (
                        <rect
                            key={rectKey}
                            x={currentLoaderProps[rectKey].x}
                            y={currentLoaderProps[rectKey].y}
                            rx="5"
                            ry="5"
                            width={currentLoaderProps.rectWidth}
                            height={currentLoaderProps.rectHeight}
                        />
                    ))}
                </ContentLoader>
            </div>
        </div>
    );
};

export default BottomLeftLeafLoadingComponent;
