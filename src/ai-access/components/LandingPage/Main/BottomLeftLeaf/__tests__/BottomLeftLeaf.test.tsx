import React from "react";
import { render, screen } from "@testing-library/react";
import { describe, expect, it, vi } from "vitest";
import { useSelector } from "react-redux";
import { useScreenSize } from "../../../../../hooks/useScreenSize";
import BottomLeftLeaf from "../BottomLeftLeaf";
import { ScreenSize } from "../../../../../constants/screenSizes";
import { MemoryRouter } from "@sparky/framework/router";

// Mock dependencies
vi.mock("react-redux", () => ({
    useSelector: vi.fn(),
}));

vi.mock("../../../../../hooks/useScreenSize", () => ({
    useScreenSize: vi.fn(),
}));

vi.mock('../../ReusableComponents/LeafBaseComponent', () => ({
    __esModule: true,
    default: ({ children, ...props }) => (
      <div 
        data-testid="leaf-base-component"
        data-is-zero={props.isZero ? 'true' : undefined}
        {...props}
      >
        {children}
      </div>
    ),
  }));
  
  

vi.mock("../BottomLeftLeafLoadingComponent", () => ({
    __esModule: true,
    default: () => <div data-testid="bottom-left-leaf-loading-component" />,
}));

// Mock Tailwind CSS utilities
vi.mock("@panwds/tailwindcss", async (importOriginal) => {
    const actual = await importOriginal();
    return {
        ...actual,
        twMerge: vi.fn((...classes) => classes.join(" ")),
        tw: actual.tw,
    };
});

const Wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <MemoryRouter>
        {children}
    </MemoryRouter>
);

const renderWithRouter = (ui, { route = "/" } = {}) => {
    window.history.pushState({}, "Test page", route);
    return render(ui, { wrapper: Wrapper });
};

describe("BottomLeftLeaf", () => {
    it("renders loading component when data is loading", () => {
        vi.mocked(useSelector).mockReturnValue({ loading: true, data: null, error: null });
        vi.mocked(useScreenSize).mockReturnValue({ size: ScreenSize.LARGE });

        render(<BottomLeftLeaf />, { wrapper: Wrapper });
        expect(screen.getByTestId("bottom-left-leaf-loading-component")).toBeDefined();
    });
    it("handles error state", () => {
        vi.mocked(useSelector).mockReturnValue({
            loading: true,
            data: null,
            error: "Test error",
        });
        vi.mocked(useScreenSize).mockReturnValue({ size: ScreenSize.LARGE });

        renderWithRouter(<BottomLeftLeaf />);
        expect(screen.getByTestId("bottom-left-leaf-loading-component")).toBeDefined();
    });
});
