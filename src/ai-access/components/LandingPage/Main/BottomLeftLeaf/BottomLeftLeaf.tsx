import React from "react";
import { useSelector } from "react-redux";
import LeafBaseComponent from "../../../ReusableComponents/LeafBaseComponent";
import { emptyStateLandingPageIcon, getConstant, getTag, isNil, tagsAvailable } from "../../../../Utils";
import { useThemeContext } from "../../../../../netsec-ui/utils/ThemeProvider";
import LeafComponentTooltipContent from "../../../ReusableComponents/LeafComponentTooltipContent"
import { isEmpty } from "../../../../../netsec-ui/utils/NetsecUIUtils";
import BottomLeftLeafLoadingComponent from "./BottomLeftLeafLoadingComponent";
import { ScreenSize } from "../../../../constants/screenSizes";
import { useScreenSize } from "../../../../hooks/useScreenSize";
import "./BottomLeftLeaf.scss";

export interface BottomLeftLeafProps {
}

const BottomLeftLeaf: React.FC<BottomLeftLeafProps> = (props) => {
    const { size: screenSize } = useScreenSize();
    //@ts-ignore
    const { loading, data, error } = useSelector((state) => state?.ai?.topUseCases?.default ?? {});
    const thirdUseCase = data?.thirdUseCase;
    const mostUsedApplication = thirdUseCase?.most_used_application || {};
    const buttonGroup = getTag();
    const constants = getConstant();
    const themeMode = useThemeContext()?.mode;
    const isTagsAvailable = tagsAvailable();

    const isZeroApplication = (
        thirdUseCase?.sanctioned_applications === 0 &&
        thirdUseCase?.unsanctioned_applications === 0 &&
        thirdUseCase?.tolerated_applications === 0);

    const isZeroUser = (
        thirdUseCase?.unsanctioned_users === 0 &&
        thirdUseCase?.sanctioned_users === 0 &&
        thirdUseCase?.tolerated_users === 0);

    const stylesByScreenSize = {
        [ScreenSize.SMALL]: {
            barLenWidth: "160px",
            applicationContainerStyle: { right: "0.7rem" },
            usersContainerStyle: { left: "0rem" },
            zeroApplicationContainerStyle: { right: "1.4rem" },
            zeroUsersContainerStyle: { right: "0.2rem" },
            barLenHeight: "6px"
        },
        [ScreenSize.LARGE]: {
            barLenWidth: "180px",
            applicationContainerStyle: { right: "1.4rem" },
            usersContainerStyle: { right: "0" },
            zeroApplicationContainerStyle: { right: "1.9rem" },
            zeroUsersContainerStyle: { right: "0.3rem" },
            barLenHeight: "6px"
        },
        [ScreenSize.XLARGE]: {
            barLenWidth: "220px",
            applicationContainerStyle: { right: "2.3rem" },
            usersContainerStyle: { right: "0.8rem" },
            zeroApplicationContainerStyle: { right: "2.5rem" },
            zeroUsersContainerStyle: { right: "1.3rem" },
            barLenHeight: "8px"
        },
        [ScreenSize.XXLARGE]: {
            barLenWidth: "350px",
            applicationContainerStyle: { right: "6.6rem" },
            usersContainerStyle: { right: "5.1rem" },
            zeroApplicationContainerStyle: { right: "6.8rem" },
            zeroUsersContainerStyle: { right: "5.3rem" },
            barLenHeight: "9px"
        },
    };

    const styles = stylesByScreenSize[screenSize];

    if ((isNil(data) || isEmpty(data)) || loading) {
        return (
            <BottomLeftLeafLoadingComponent
                themeMode={themeMode}
            />
        );
    }

    if (isZeroApplication && isZeroUser) {
        return (
            <LeafBaseComponent
                icon={thirdUseCase?.most_used_application?.icon ?? emptyStateLandingPageIcon(themeMode)}
                loading={loading}
                useCaseId={thirdUseCase?.use_case_id}
                isZero={isZeroApplication && isZeroUser}
                position={"bottom-left"}
                tooltip={<LeafComponentTooltipContent mostUsedApplication={mostUsedApplication} useCase={thirdUseCase?.use_case} />}
                data={{
                    topBannerText: "3rd",
                    id: thirdUseCase?.use_case_id,
                    contentTitle: thirdUseCase?.use_case,
                    label: !isTagsAvailable ? "" : mostUsedApplication.application_sub_type,
                    iconText: thirdUseCase?.most_used_application?.application_name,
                    singleStackedBar: [
                        {
                            stackedCount: thirdUseCase?.total_applications || 0,
                            stackedType: constants.application,
                            isZero: isZeroApplication,
                            stackedData: [
                                {
                                    category: `${buttonGroup.sanctioned}`,
                                    color: "#33CCB8",
                                    label: buttonGroup.sanctioned,
                                    count: thirdUseCase?.sanctioned_applications || 0,
                                },
                                {
                                    category: `${buttonGroup.tolerated}`,
                                    color: "#289EC9",
                                    label: buttonGroup.tolerated,
                                    count: thirdUseCase?.tolerated_applications || 0,
                                },
                                {
                                    category: `${buttonGroup.unsanctioned}`,
                                    color: "#BC246A",
                                    label: buttonGroup.unsanctioned,
                                    count: thirdUseCase?.unsanctioned_applications || 0,
                                },
                            ],
                            barLenWidth: styles.barLenWidth,
                            barLenHeight: styles.barLenHeight,
                            showTooltip: true,
                            style: {
                                container: {
                                    position: "relative",
                                    ...styles.zeroApplicationContainerStyle
                                },
                                isZeroContainer: {
                                    width: styles.barLenWidth,
                                },
                                isZeroBar: {
                                    width: styles.barLenWidth,
                                }
                            },
                        },
                        {
                            stackedCount: thirdUseCase?.total_users || 0,
                            stackedType: "users",
                            isZero: isZeroUser,
                            stackedData: [
                                {
                                    category: `${buttonGroup.sanctioned}`,
                                    color: "#33CCB8",
                                    label: buttonGroup.sanctioned,
                                    count: thirdUseCase?.sanctioned_users || 0,
                                },
                                {
                                    category: `${buttonGroup.tolerated}`,
                                    color: "#289EC9",
                                    label: buttonGroup.tolerated,
                                    count: thirdUseCase?.tolerated_users || 0,
                                },
                                {
                                    category: `${buttonGroup.unsanctioned}`,
                                    color: "#BC246A",
                                    label: buttonGroup.unsanctioned,
                                    count: thirdUseCase?.unsanctioned_users || 0,
                                },
                            ],
                            barLenWidth: styles.barLenWidth,
                            barLenHeight: styles.barLenHeight,
                            showTooltip: true,
                            style: {
                                container: {
                                    position: "relative",
                                    ...styles.zeroUsersContainerStyle,
                                },
                                isZeroContainer: {
                                    width: styles.barLenWidth,
                                },
                                isZeroBar: {
                                    width: styles.barLenWidth,
                                }
                            },
                        },
                    ],
                }}
                error={error}
            />
        )
    }

    return (
        <LeafBaseComponent
            icon={thirdUseCase?.most_used_application?.icon ?? emptyStateLandingPageIcon(themeMode)}
            loading={loading}
            useCaseId={thirdUseCase?.use_case_id}
            position={"bottom-left"}
            tooltip={<LeafComponentTooltipContent
                mostUsedApplication={mostUsedApplication}
                useCase={thirdUseCase?.use_case}
            />}
            data={{
                topBannerText: "3rd",
                id: thirdUseCase?.use_case_id,
                contentTitle: thirdUseCase?.use_case,
                label: !isTagsAvailable ? "" : mostUsedApplication.application_sub_type,
                iconText: thirdUseCase?.most_used_application?.application_name,
                singleStackedBar: [
                    {
                        stackedCount: thirdUseCase?.total_applications || 0,
                        stackedType: constants.application,
                        isZero: !isTagsAvailable || isZeroApplication,
                        stackedData: [
                            {
                                category: `${buttonGroup.sanctioned}`,
                                color: "#33CCB8",
                                label: buttonGroup.sanctioned,
                                count: thirdUseCase?.sanctioned_applications || 0,
                            },
                            {
                                category: `${buttonGroup.tolerated}`,
                                color: "#289EC9",
                                label: buttonGroup.tolerated,
                                count: thirdUseCase?.tolerated_applications || 0,
                            },
                            {
                                category: `${buttonGroup.unsanctioned}`,
                                color: "#BC246A",
                                label: buttonGroup.unsanctioned,
                                count: thirdUseCase?.unsanctioned_applications || 0,
                            },
                        ],
                        barLenWidth: styles.barLenWidth,
                        barLenHeight: styles.barLenHeight,
                        showTooltip: true,
                        style: {
                            container: {
                                position: "relative",
                                ...styles.applicationContainerStyle,
                            },
                        },
                    },
                    {
                        stackedCount: thirdUseCase?.total_users,
                        stackedType: "users",
                        isZero: !isTagsAvailable || isZeroUser,
                        stackedData: [
                            {
                                category: `${buttonGroup.sanctioned}`,
                                color: "#33CCB8",
                                label: buttonGroup.sanctioned,
                                count: thirdUseCase?.sanctioned_users || 0,
                            },
                            {
                                category: `${buttonGroup.tolerated}`,
                                color: "#289EC9",
                                label: buttonGroup.tolerated,
                                count: thirdUseCase?.tolerated_users || 0,
                            },
                            {
                                category: `${buttonGroup.unsanctioned}`,
                                color: "#BC246A",
                                label: buttonGroup.unsanctioned,
                                count: thirdUseCase?.unsanctioned_users || 0,
                            },
                        ],
                        barLenWidth: styles.barLenWidth,
                        barLenHeight: styles.barLenHeight,
                        showTooltip: true,
                        style: {
                            container: {
                                position: "relative",
                                ...styles.usersContainerStyle,
                            },
                        },
                    },
                ],
            }}
            error={error}
        />
    );
};

export default BottomLeftLeaf;
