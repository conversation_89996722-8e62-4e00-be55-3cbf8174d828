import ContentLoader from "react-content-loader";
import { useThemeContext } from "../../../../../../netsec-ui/utils/ThemeProvider";
import { useScreenSize } from "../../../../../hooks/useScreenSize";
import { ScreenSize } from "../../../../../constants/screenSizes";

const AggregatedLegendLoading = () => {
    const themeMode = useThemeContext()?.mode;
    const { size: screenSize } = useScreenSize();

    const loaderProps = {
        [ScreenSize.SMALL]: {
            speed: 1,
            width: 540,
            height: 16,
            viewBox: "0 0 300 16",
            rect: { x: 40, y: 0, rx: 10, ry: 10, width: 200, height: 16 }
        },
        [ScreenSize.LARGE]: {
            speed: 1,
            width: 540,
            height: 16,
            viewBox: "0 0 300 16",
            rect: { x: 40, y: 0, rx: 10, ry: 10, width: 200, height: 16 }
        },
        [ScreenSize.XLARGE]: {
            speed: 1,
            width: 540,
            height: 16,
            viewBox: "0 0 540 16",
            rect: { x: 100, y: 0, rx: 10, ry: 10, width: 400, height: 16 }
        },
        [ScreenSize.XXLARGE]: {
            speed: 1,
            width: 800,
            height: 20,
            viewBox: "0 0 1000 20",
            rect: { x: 150, y: 0, rx: 10, ry: 10, width: 800, height: 20 }
        },
    };

    const currentLoaderProps = loaderProps[screenSize];
    return (
        <div className="ai-access-all-other-case-status-container">
            <ContentLoader
                {...currentLoaderProps}
                backgroundColor={themeMode === "dark" ? "#484848" : "#B8B8B8"}
                foregroundColor="#ecebeb"
            >
                <rect {...currentLoaderProps.rect} />
            </ContentLoader>
        </div>
    )
}

export default AggregatedLegendLoading;
