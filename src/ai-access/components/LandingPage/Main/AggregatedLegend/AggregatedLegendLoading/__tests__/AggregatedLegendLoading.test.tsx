import { describe, expect, it, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import AggregatedLegendLoading from "../AggregatedLegendLoading";
import { ThemeProvider } from "../../../../../../../netsec-ui/utils/ThemeProvider";
import { ScreenSize } from "../../../../../../constants/screenSizes";
import { useScreenSize } from "../../../../../../hooks/useScreenSize";
import React from "react";

vi.mock("../../../../../../hooks/useScreenSize", () => ({
    useScreenSize: vi.fn()
}));

describe("AggregatedLegendLoading", () => {
    it("renders without crashing", () => {
        vi.mocked(useScreenSize).mockReturnValue({ size: ScreenSize.LARGE });
        render(
            <ThemeProvider>
                <AggregatedLegendLoading />
            </ThemeProvider>
        );
        expect(screen.getByRole("img")).toBeInTheDocument();
    });

    it("uses correct props for small screen", () => {
        vi.mocked(useScreenSize).mockReturnValue({ size: ScreenSize.SMALL });
        const { container } = render(
            <ThemeProvider>
                <AggregatedLegendLoading />
            </ThemeProvider>
        );
        const svg = container.querySelector("svg");
        expect(svg).toHaveAttribute("viewBox", "0 0 300 16");
    });

    it("uses correct props for large screen", () => {
        vi.mocked(useScreenSize).mockReturnValue({ size: ScreenSize.LARGE });
        const { container } = render(
            <ThemeProvider>
                <AggregatedLegendLoading />
            </ThemeProvider>
        );
        const svg = container.querySelector("svg");
        expect(svg).toHaveAttribute("viewBox", "0 0 300 16");
    });

    it("uses correct props for xlarge screen", () => {
        vi.mocked(useScreenSize).mockReturnValue({ size: ScreenSize.XLARGE });
        const { container } = render(
            <ThemeProvider>
                <AggregatedLegendLoading />
            </ThemeProvider>
        );
        const svg = container.querySelector("svg");
        expect(svg).toHaveAttribute("viewBox", "0 0 540 16");
    });

    it("uses correct props for xxlarge screen", () => {
        vi.mocked(useScreenSize).mockReturnValue({ size: ScreenSize.XXLARGE });
        const { container } = render(
            <ThemeProvider>
                <AggregatedLegendLoading />
            </ThemeProvider>
        );
        const svg = container.querySelector("svg");
        expect(svg).toHaveAttribute("viewBox", "0 0 1000 20");
    });
    
    it("uses consistent background color regardless of theme mode", () => {
        vi.mocked(useScreenSize).mockReturnValue({ size: ScreenSize.LARGE });
        const { container } = render(
            <ThemeProvider mode="dark">
                <AggregatedLegendLoading />
            </ThemeProvider>
        );
        const stopElement = container.querySelector("stop");
        expect(stopElement).toHaveAttribute("stop-color", "#B8B8B8");
    });
});