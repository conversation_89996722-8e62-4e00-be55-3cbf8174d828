import React from "react";
import { useSelector } from "react-redux";
import { Tooltip } from "@panwds/react-ui";
import "./AggregatedLegend.scss";
import { getTag, getUseCaseFilter } from "../../../../Utils";
import AggregatedLegendLoading from "./AggregatedLegendLoading";
export interface AggregatedLegendProps { }

const AggregatedLegend: React.FC<AggregatedLegendProps> = (props) => {
    //@ts-ignore
    const { loading, data } = useSelector((state) => state?.ai?.appClassification?.default ?? {});
    const buttonGroup = getTag();
    const constants = getUseCaseFilter();

    if (loading) {
        return (
            <AggregatedLegendLoading />
        );
    }

    const total_applications = data?.sanctioned_applications + data?.tolerated_applications + data?.unsanctioned_applications;
    if (!total_applications) { return null; }
    return (
        <div className="ai-access-all-other-case-status-container">
            <Tooltip
                addClassName={"ai-access-all-other-case-tooltip"}
                label={constants.aggregated_legend_tooltip}
            >
                <div className="ai-access-all-other-case-status ai-access-all-other-case-sanctioned">
                    {`${buttonGroup.sanctioned}`}&nbsp;
                    {`(${data?.sanctioned_applications || 0}/${total_applications || 0})`}
                </div>
            </Tooltip>
            <Tooltip
                addClassName={"ai-access-all-other-case-tooltip"}
                label={constants.aggregated_legend_tooltip}
            >
                <div className="ai-access-all-other-case-status ai-access-all-other-case-tolerated">
                    {`${buttonGroup.tolerated}`}&nbsp;
                    {`(${data?.tolerated_applications || 0}/${total_applications || 0})`}
                </div>
            </Tooltip>
            <Tooltip
                addClassName={"ai-access-all-other-case-tooltip"}
                label={constants.aggregated_legend_tooltip}
            >
                <div className="ai-access-all-other-case-status ai-access-all-other-case-unsanctioned">
                    {`${buttonGroup.unsanctioned}`}&nbsp;
                    {`(${data?.unsanctioned_applications || 0}/${total_applications || 0})`}
                </div>
            </Tooltip>
        </div>
    );
};

export default AggregatedLegend;
