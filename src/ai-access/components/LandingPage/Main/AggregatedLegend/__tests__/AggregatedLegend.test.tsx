// File: src/ai-access/components/LandingPage/Main/AggregatedLegend/__tests__/AggregatedLegend.test.tsx
import React from "react";
import { render, screen } from "@testing-library/react";
import { afterEach, describe, expect, it, vi } from "vitest";
import * as ReactRedux from "react-redux";
import AggregatedLegend from "../AggregatedLegend";

// ----------------------------------------------------------------------
// Module Mocks
// ----------------------------------------------------------------------

// Override react-redux to provide mock implementations for useDispatch, useSelector, and Provider.
vi.mock("react-redux", () => ({
    useDispatch: vi.fn(() => vi.fn()),
    useSelector: vi.fn(),
    Provider: ({ children }: { children: React.ReactNode }) => <>{children}</>,
}));

// Mock the utility functions from the correct (updated) relative path.
// From the test file location, "src/ai-access/components/LandingPage/Main/AggregatedLegend/__tests__",
// we need to go up 6 levels to reach the Utils module.
vi.mock("../../../../../../Utils", () => ({
    getTag: vi.fn(),
    getUseCaseFilter: vi.fn(),
}));

// Mock the loading component.
vi.mock("../AggregatedLegendLoading", () => ({
    default: () => <div>Loading Legend</div>,
}));

// ----------------------------------------------------------------------
// Test Suite
// ----------------------------------------------------------------------
describe("AggregatedLegend", () => {
    afterEach(() => {
        vi.clearAllMocks();
    });

    it("renders loading state when loading is true", () => {
        // Set Redux selector to return a loading state.
        (ReactRedux.useSelector as vi.Mock).mockReturnValue({ loading: true, data: null });
        render(<AggregatedLegend />);
        // Expect the mocked loading component to be rendered.
        expect(screen.getByText("Loading Legend")).toBeInTheDocument();
    });

    it("returns null when total_applications is falsy", () => {
        // Provide state with loading false and data counts all equal to zero.
        (ReactRedux.useSelector as vi.Mock).mockReturnValue({
            loading: false,
            data: {
                sanctioned_applications: 0,
                tolerated_applications: 0,
                unsanctioned_applications: 0,
            },
        });
        const { container } = render(<AggregatedLegend />);
        // When the total applications count is zero, the component should return null (render nothing).
        expect(container.innerHTML).toBe("");
    });

    it.todo("renders aggregated legend correctly when valid data is available", () => {
        // Provide Redux state with loading = false and valid application data.
        const data = {
            sanctioned_applications: 10,
            tolerated_applications: 20,
            unsanctioned_applications: 30,
        }; // Total applications = 60
        (ReactRedux.useSelector as vi.Mock).mockReturnValue({ loading: false, data });

        // Obtain the mocked utility module from the updated correct path.
        const utils = require("../../../../../../Utils");
        // Set up return values for getTag and getUseCaseFilter.
        utils.getTag.mockReturnValue({
            sanctioned: "SanctionedLabel",
            tolerated: "ToleratedLabel",
            unsanctioned: "UnsanctionedLabel",
        });
        utils.getUseCaseFilter.mockReturnValue({
            aggregated_legend_tooltip: "Tooltip message",
        });

        render(<AggregatedLegend />);

        // Verify that the expected texts appear inside the rendered tooltips.
        expect(screen.getByText("SanctionedLabel (10/60)")).toBeInTheDocument();
        expect(screen.getByText("ToleratedLabel (20/60)")).toBeInTheDocument();
        expect(screen.getByText("UnsanctionedLabel (30/60)")).toBeInTheDocument();
    });
});
