.tw-dark {
    .ai-access-all-other-case-status-container {
        color: #F4F5F5;
    }

    .ai-access-all-other-case-tooltip {
        border-radius: 8px;
        border: 1px solid #333;
        background-color: rgba(26, 26, 26, 0.80);
        backdrop-filter: blur(4px);
        color: #B8B8B8;
    }
}

.ai-access-all-other-case-status-container {
    display: flex;
    text-align: center;
    justify-content: space-between;
    align-items: center;
}

.ai-access-all-other-case-status {
    display: flex;
    align-items: center;
    gap: 10px;
    font-family: Lato;
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    letter-spacing: 0.022px;

    @media (min-width: 2000px) {
        font-size: 14px;
    }

    @media (min-width: 2560px) {
        font-size: 18px;
    }

    @media (min-width: 3840px) {
        font-size: 26px;
    }
}

.ai-access-all-other-case-sanctioned::before {
    content: '';
    width: 8px;
    height: 8px;
    background-color: #33CCB8;
    border-radius: 50%;
    display: inline-block;
}

.ai-access-all-other-case-tolerated::before {
    content: '';
    width: 8px;
    height: 8px;
    background-color: #289EC9;
    border-radius: 50%;
    display: inline-block;
}

.ai-access-all-other-case-unsanctioned::before {
    content: '';
    width: 8px;
    height: 8px;
    background-color: #BC246A;
    border-radius: 50%;
    display: inline-block;
}

.ai-access-all-other-case-tooltip {
    background-color: #FFFFFF;
    color: #333;
    border-radius: 8px;
    border: 1px solid #333;
    backdrop-filter: blur(4px);
}
