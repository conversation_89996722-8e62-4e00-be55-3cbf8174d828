import React, { ReactElement } from "react";
import IntlUtil from "../../../../netsec-ui/utils/i18n/IntlUtil";
import Header from "../../ReusableComponents/Header";

export default function LandingPageHeader(): ReactElement {
    const intlInstance = IntlUtil.getIntl();
    return (
        <Header
            title={intlInstance.formatMessage({
                id: "ai_access.landing_page.landingPageTitle",
                defaultMessage: "AI Access Security",
            })}
            subtitle={intlInstance.formatMessage({
                id: "ai_access.landing_page.landing_page_subtitle",
                defaultMessage: "Precise control, complete visibility, and robust protection for your GenAI apps—all in one place",
            })}
            className={"landing-page"}
        />
    );
}