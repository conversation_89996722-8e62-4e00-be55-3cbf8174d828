import React from "react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { render, screen } from "@testing-library/react";
import LandingPageHeader from "../LandingPageHeader";
import IntlUtil from "../../../../../netsec-ui/utils/i18n/IntlUtil";
import Header from "../../../ReusableComponents/Header";

// Mock dependencies
vi.mock("../../../../../netsec-ui/utils/i18n/IntlUtil", () => ({
    default: {
        getIntl: vi.fn(),
    },
}));

vi.mock("../../../ReusableComponents/Header", () => ({
    default: vi.fn(() => <div data-testid="mock-header" />),
}));

describe("LandingPageHeader", () => {
    const mockFormatMessage = vi.fn((params) => params.defaultMessage);
    const mockIntl = { formatMessage: mockFormatMessage };

    beforeEach(() => {
        vi.clearAllMocks();
        (IntlUtil.getIntl as jest.Mock).mockReturnValue(mockIntl);
    });

    it("renders the Header component with correct props", () => {
        render(<LandingPageHeader />);

        expect(Header).toHaveBeenCalledWith(
            {
                title: "AI Access Security",
                subtitle: "Precise control, complete visibility, and robust protection for your GenAI apps—all in one place",
                className: "landing-page",
            },
            expect.anything()
        );

        expect(screen.getByTestId("mock-header")).toBeInTheDocument();
    });

    it("uses IntlUtil to get translations", () => {
        render(<LandingPageHeader />);

        expect(IntlUtil.getIntl).toHaveBeenCalled();
        expect(mockFormatMessage).toHaveBeenCalledWith({
            id: "ai_access.landing_page.landingPageTitle",
            defaultMessage: "AI Access Security",
        });
        expect(mockFormatMessage).toHaveBeenCalledWith({
            id: "ai_access.landing_page.landing_page_subtitle",
            defaultMessage: "Precise control, complete visibility, and robust protection for your GenAI apps—all in one place",
        });
    });

    it("uses default messages when translations are missing", () => {
        mockFormatMessage.mockImplementation(({ defaultMessage }) => defaultMessage);
        render(<LandingPageHeader />);

        expect(Header).toHaveBeenCalledWith(
            expect.objectContaining({
                title: "AI Access Security",
                subtitle: "Precise control, complete visibility, and robust protection for your GenAI apps—all in one place",
            }),
            expect.anything()
        );
    });

    it("passes the correct className to Header", () => {
        render(<LandingPageHeader />);

        expect(Header).toHaveBeenCalledWith(
            expect.objectContaining({
                className: "landing-page",
            }),
            expect.anything()
        );
    });
});
