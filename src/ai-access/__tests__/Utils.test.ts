import { beforeEach, describe, expect, it, vi } from "vitest";
import * as Utils from "../Utils";
import { getFrameworkVars, getState } from "@sparky/framework";
import { isPrivateApp } from "../../netsec-ui/utils";
import { TimeRange } from "../../netsec-ui/redux/threats-server";
import { isAppTaggingAvailable } from "../../netsec-ui/components/NetsecUIChart/NetsecUILicenseUtil";
import DarkEmptyAppIcon from "../images/appImages/dark/empty-landing-page-dark-theme.svg";
import LightEmptyAppIcon from "../images/appImages/light/empty-landing-page-light-theme.svg";
import IntlUtil from "../../netsec-ui/utils/i18n/IntlUtil";
import * as useCasesModule from "../Constant";

vi.mock("@sparky/framework", () => ({
    getState: vi.fn(),
    getFrameworkVars: vi.fn(),
}));

vi.mock("../../netsec-ui/utils/i18n/IntlUtil", async () => {
    const actual = await vi.importActual("../../netsec-ui/utils/i18n/IntlUtil");
    return {
        ...actual,
        getIntl: vi.fn(() => ({
            formatMessage: vi.fn()
        })),
    };
});

vi.mock("../../netsec-ui/utils", () => ({
    isPrivateApp: vi.fn(),
}));

vi.mock("../Utils", async () => {
    const actual = await vi.importActual("../Utils");
    return {
        ...actual,
        getTag: vi.fn(),
        getConstant: vi.fn(),
    };
});

vi.mock("../../netsec-ui/components/NetsecUIChart/NetsecUILicenseUtil", () => ({
    isAppTaggingAvailable: vi.fn(),
}));
  
describe("Utils", () => {

    describe("isNil", () => {
        it("should return true for undefined", () => {
            expect(Utils.isNil(undefined)).toBe(true);
        });

        it("should return true for null", () => {
            expect(Utils.isNil(null)).toBe(true);
        });

        it("should return true for NaN", () => {
            expect(Utils.isNil(NaN)).toBe(true);
        });

        it("should return true for empty string", () => {
            expect(Utils.isNil("")).toBe(true);
        });

        it("should return false for non-nil values", () => {
            expect(Utils.isNil(0)).toBe(false);
            expect(Utils.isNil("hello")).toBe(false);
            expect(Utils.isNil({})).toBe(false);
        });
    });

    describe("getUseCases", () => {
        let mockUseCases;
        let mockIntlInstance;
      
        beforeEach(() => {
            // Reset mocks before each test
            vi.restoreAllMocks();
      
            mockIntlInstance = IntlUtil.getIntl();
            mockUseCases = vi.spyOn(useCasesModule, "USE_CASES").mockReturnValue({ testKey: "testValue" });
        });
      
        it("should return the result of USE_CASES function with intlInstance and themeMode", () => {
            const result = Utils?.getUseCases("dark");
      
            expect(mockUseCases).toHaveBeenCalledWith(mockIntlInstance, "dark");
            expect(result).toEqual({ testKey: "testValue" });
        });
      
        it("should call USE_CASES with undefined themeMode if no argument is provided", () => {
            mockUseCases.mockReturnValue({ testKey: "defaultTestValue" });
      
            const result = Utils?.getUseCases();
      
            expect(mockUseCases).toHaveBeenCalledWith(mockIntlInstance, undefined);
            expect(result).toEqual({ testKey: "defaultTestValue" });
        });
    });

    describe("getErrorMessage", () => {
        let mockIntlInstance;
      
        beforeEach(() => {
            vi.restoreAllMocks();
            mockIntlInstance = IntlUtil.getIntl();
        });
      
        it("should return the error message if provided", () => {
            const error = { message: "An error occurred" };
            expect(Utils?.getErrorMessage(error)).toBe("An error occurred");
        });
      
        it("should return parsed statusText if message is missing", () => {
            const error = { statusText: "Internal Server Error" };
            expect(Utils?.getErrorMessage(error)).toBe("Internal Server Error");
        });
      
        it("should return a default error message when error is an empty object", () => {
            expect(Utils?.getErrorMessage({})).toBe("API call was not successful. Please try again.");
        });
      
        it("should return a default error message when error is null", () => {
            expect(Utils?.getErrorMessage(null as unknown as Record<string, unknown>)).toBe("API call was not successful. Please try again.");
        });
      
        it("should return a default error message when error is undefined", () => {
            expect(Utils?.getErrorMessage(undefined as unknown as Record<string, unknown>)).toBe("API call was not successful. Please try again.");
        });
      
        it("should return an error message even when `message` is undefined but `statusText` is valid", () => {
            const error = { statusText: "Bad Request" };
            expect(Utils?.getErrorMessage(error)).toBe("Bad Request");
        });
    });

    describe("threatSupported", () => {
        beforeEach(() => {
            // Reset the mock before each test
            vi.resetAllMocks();
        });
      
        it("should return true when panw_netsec:threat_subscription feature is present", () => {
            vi.mocked(getState).mockReturnValue({
                saseIAMainState: {
                    cosmosFeatures: [
                        { feature: "panw_netsec:threat_subscription" },
                        { feature: "some_other_feature" },
                    ],
                },
            });
      
            expect(Utils.threatSupported()).toBe(true);
        });
      
        it("should return false when panw_netsec:threat_subscription feature is not present", () => {
            vi.mocked(getState).mockReturnValue({
                saseIAMainState: {
                    cosmosFeatures: [
                        { feature: "some_other_feature" },
                        { feature: "another_feature" },
                    ],
                },
            });
      
            expect(Utils.threatSupported()).toBe(false);
        });
      
        it("should return false when cosmosFeatures is an empty array", () => {
            vi.mocked(getState).mockReturnValue({
                saseIAMainState: {
                    cosmosFeatures: [],
                },
            });
      
            expect(Utils.threatSupported()).toBe(false);
        });
      
        it("should return false when cosmosFeatures is undefined", () => {
            vi.mocked(getState).mockReturnValue({
                saseIAMainState: {},
            });
      
            expect(Utils.threatSupported()).toBe(false);
        });

    });

    describe("isUserSidePanelAllowed", () => {
        beforeEach(() => {
            vi.resetAllMocks();
            vi.mocked(getFrameworkVars).mockReturnValue({ private_site_origin: "" });
        });
      
        it("should return true when not a private app", () => {
            vi.mocked(getState).mockReturnValue(false);
            expect(Utils.isUserSidePanelAllowed()).toBe(true);
        });
      
        it("should return true when private app and panw_sase:users feature is present", () => {
            vi.mocked(getState).mockReturnValue({
                saseIAMainState: {
                    cosmosFeatures: [
                        { feature: "panw_sase:users" },
                        { feature: "some_other_feature" },
                    ],
                },
            });
      
            expect(Utils.isUserSidePanelAllowed()).toBe(true);
        });
      
        it("should return false when private app and panw_sase:users feature is not present", () => {
            vi.mocked(isPrivateApp).mockReturnValue(true);
            vi.mocked(getState).mockReturnValue({
                saseIAMainState: {
                    cosmosFeatures: [
                        { feature: "some_other_feature" },
                    ],
                },
            });

            expect(Utils.isUserSidePanelAllowed()).toBe(false);
        });
      
        it("should return false when private app and cosmosFeatures is an empty array", () => {
            vi.mocked(isPrivateApp).mockReturnValue(true);
            vi.mocked(getState).mockReturnValue({
                saseIAMainState: {
                    cosmosFeatures: [],
                },
            });
      
            expect(Utils.isUserSidePanelAllowed()).toBe(false);
        });
      
        it("should return false when private app and cosmosFeatures is undefined", () => {
            vi.mocked(isPrivateApp).mockReturnValue(true);
            vi.mocked(getState).mockReturnValue({
                saseIAMainState: {},
            });
      
            expect(Utils.isUserSidePanelAllowed()).toBe(false);
        });
    });

    describe("tagsAvailable", () => {
        it("should return true when app tagging is available", () => {
            vi.mocked(isAppTaggingAvailable).mockReturnValue(true);
            expect(Utils?.tagsAvailable()).toBe(true);
        });
      
        it("should return false when app tagging is not available", () => {
            vi.mocked(isAppTaggingAvailable).mockReturnValue(false);
            expect(Utils?.tagsAvailable()).toBe(false);
        });
      
        it("should call isAppTaggingAvailable function", () => {
            Utils?.tagsAvailable();
            expect(isAppTaggingAvailable).toHaveBeenCalled();
        });
    });

    describe("copilotFlag", () => {
        it("should return true when copilot is enabled", () => {
            vi.mocked(getState).mockReturnValue({
                copilot: {
                    featureFlags: {
                        isCopilotEnabled: true
                    }
                }
            });
            expect(Utils.copilotFlag()).toBe(true);
        });

        it("should return false when copilot is disabled", () => {
            vi.mocked(getState).mockReturnValue({
                copilot: {
                    featureFlags: {
                        isCopilotEnabled: false
                    }
                }
            });
            expect(Utils.copilotFlag()).toBe(false);
        });
    });

    describe("appRiskMapping", () => {
        it("should return correct color class for risk level 1", () => {
            expect(Utils.appRiskMapping(1)).toBe("cc-bg-yellow-300");
        });
    
        it("should return correct color class for risk level 2", () => {
            expect(Utils.appRiskMapping(2)).toBe("cc-bg-yellow-400");
        });
    
        it("should return correct color class for risk level 3", () => {
            expect(Utils.appRiskMapping(3)).toBe("cc-bg-orange-400");
        });
    
        it("should return correct color class for risk level 4", () => {
            expect(Utils.appRiskMapping(4)).toBe("cc-bg-red-500");
        });
    
        it("should return correct color class for risk level 5", () => {
            expect(Utils.appRiskMapping(5)).toBe("cc-bg-magenta");
        });
    
        it("should return undefined for risk level 0", () => {
            expect(Utils.appRiskMapping(0)).toBeUndefined();
        });
    
        it("should return undefined for risk level 6", () => {
            expect(Utils.appRiskMapping(6)).toBeUndefined();
        });
    
        it("should return undefined for non-integer risk levels", () => {
            expect(Utils.appRiskMapping(2.5)).toBeUndefined();
        });
    
        it("should return undefined for negative risk levels", () => {
            expect(Utils.appRiskMapping(-1)).toBeUndefined();
        });
    });

    describe("getMessage", () => {
        it("should return the first message from an array", () => {
            const extra = { message: ["First message", "Second message"] };
            expect(Utils?.getMessage(extra, "Default error")).toBe("First message");
        });
      
        it("should return the message string if not an array", () => {
            const extra = { message: "Single message" };
            expect(Utils?.getMessage(extra, "Default error")).toBe("Single message");
        });
      
        it("should return the default error message if extra.message is undefined", () => {
            const extra = {};
            expect(Utils?.getMessage(extra, "Default error")).toBe("Default error");
        });
      
        it("should return default error for null extra", () => {
            expect(Utils?.getMessage(null, "Default error")).toBe("Default error");
        });
    });

    describe("emptyStateLandingPageIcon", () => {
        it("should return dark theme icon when theme mode is dark", () => {
            expect(Utils?.emptyStateLandingPageIcon("dark")).toBe(DarkEmptyAppIcon);
        });
      
        it("should return light theme icon when theme mode is light", () => {
            expect(Utils?.emptyStateLandingPageIcon("light")).toBe(LightEmptyAppIcon);
        });
      
        it("should return light theme icon for any other theme mode", () => {
            expect(Utils?.emptyStateLandingPageIcon("custom")).toBe(LightEmptyAppIcon);
        });
      
        it("should return light theme icon when theme mode is empty string", () => {
            expect(Utils?.emptyStateLandingPageIcon("")).toBe(LightEmptyAppIcon);
        });
      
        it("should return light theme icon when theme mode is undefined", () => {
            expect(Utils?.emptyStateLandingPageIcon(undefined)).toBe(LightEmptyAppIcon);
        });
    });

    describe("listViewData", () => {
        beforeEach(() => {
            vi.mocked(Utils.getTag).mockReturnValue({
                sanctioned: "Sanctioned",
                tolerated: "Tolerated",
                unsanctioned: "Unsanctioned",
            });
        });
      
        it("should return correct data for default keyPrefix", () => {
            const data = {
                sanctioned_applications: 10,
                tolerated_applications: 5,
                unsanctioned_applications: 3,
            };
            const result = Utils?.listViewData(data);
            expect(result).toEqual([
                { name: "Sanctioned", value: 10, color: "#33CCB8" },
                { name: "Tolerated", value: 5, color: "#289EC9" },
                { name: "Unsanctioned", value: 3, color: "#BC246A" },
            ]);
        });
      
        it("should return correct data for custom keyPrefix", () => {
            const data = {
                sanctioned_users: 20,
                tolerated_users: 15,
                unsanctioned_users: 8,
            };
            const result = Utils?.listViewData(data, "users");
            expect(result).toEqual([
                { name: "Sanctioned", value: 20, color: "#33CCB8" },
                { name: "Tolerated", value: 15, color: "#289EC9" },
                { name: "Unsanctioned", value: 8, color: "#BC246A" },
            ]);
        });
      
        it("should handle missing data", () => {
            const data = {};
            const result = Utils?.listViewData(data);
            expect(result).toEqual([
                { name: "Sanctioned", value: 0, color: "#33CCB8" },
                { name: "Tolerated", value: 0, color: "#289EC9" },
                { name: "Unsanctioned", value: 0, color: "#BC246A" },
            ]);
        });
      
        it("should handle partial data", () => {
            const data = {
                sanctioned_applications: 5,
            };
            const result = Utils?.listViewData(data);
            expect(result).toEqual([
                { name: "Sanctioned", value: 5, color: "#33CCB8" },
                { name: "Tolerated", value: 0, color: "#289EC9" },
                { name: "Unsanctioned", value: 0, color: "#BC246A" },
            ]);
        });
    });

    describe("getUseCaseFilter", () => {
        let mockUseCaseFilter: ReturnType<typeof vi.fn>;
        let mockIntlInstance: ReturnType<typeof IntlUtil.getIntl>;
      
        beforeEach(() => {
            // Reset mocks before each test
            vi.restoreAllMocks();
      
            mockIntlInstance = IntlUtil.getIntl();
            mockUseCaseFilter = vi
                .spyOn(useCasesModule, "GET_USECASE_FILTER")
                .mockReturnValue({ filter1: "value1", filter2: "value2" });
        });
      
        it("should call GET_USECASE_FILTER with intlInstance", () => {
            const result = Utils?.getUseCaseFilter();
      
            expect(mockUseCaseFilter).toHaveBeenCalledWith(mockIntlInstance);
            expect(result).toEqual({ filter1: "value1", filter2: "value2" });
        });
    });

    describe("isTaggingNotAllowed", () => {
        it("should return false when user has superuser role", () => {
            vi.mocked(getState).mockReturnValue({
                auth: { access: { tsg: { roles: [{ name: "superuser" }] } } },
            });
            expect(Utils?.isTaggingNotAllowed()).toBe(false);
        });
      
        it("should return false when user has security_admin role", () => {
            vi.mocked(getState).mockReturnValue({
                auth: { access: { tsg: { roles: [{ name: "security_admin" }] } } },
            });
            expect(Utils?.isTaggingNotAllowed()).toBe(false);
        });
      
        it("should return false when user has msp_superuser role", () => {
            vi.mocked(getState).mockReturnValue({
                auth: { access: { tsg: { roles: [{ name: "msp_superuser" }] } } },
            });
            expect(Utils?.isTaggingNotAllowed()).toBe(false);
        });
      
        it("should return true when user has no allowed roles", () => {
            vi.mocked(getState).mockReturnValue({
                auth: { access: { tsg: { roles: [{ name: "other_role" }] } } },
            });
            expect(Utils?.isTaggingNotAllowed()).toBe(true);
        });
      
        it("should return true when roles array is empty", () => {
            vi.mocked(getState).mockReturnValue({
                auth: { access: { tsg: { roles: [] } } },
            });
            expect(Utils?.isTaggingNotAllowed()).toBe(true);
        });
      
        it("should return true when auth object is undefined", () => {
            vi.mocked(getState).mockReturnValue({});
            expect(Utils?.isTaggingNotAllowed()).toBe(true);
        });
    });

    describe("generateSensitiveAssetsUrl", () => {
        it("should generate a correct URL with given parameters", () => {
            const sensitiveAssets = 5;
            const title = "TestApp";
            const timeRange = "HOURS24";
            const expectedUrl = "/data-loss-prevention/data-asset-explorer?filterString=ApplicationName+in+%28%27TestApp%27%29+%5B%5B%3A%3AAND%3A%3A%5D%5D+AssetType+in+%28%27Data+in+Motion%27%29&duration=HOUR_24&isGenAiOnly=true";
          
            expect(Utils?.generateSensitiveAssetsUrl(sensitiveAssets, title, timeRange)).toBe(expectedUrl);
        });
      
        it("should handle different time ranges correctly", () => {
            const sensitiveAssets = 10;
            const title = "AnotherApp";
            const timeRange = "DAYS7";
            const expectedUrl = "/data-loss-prevention/data-asset-explorer?filterString=ApplicationName+in+%28%27AnotherApp%27%29+%5B%5B%3A%3AAND%3A%3A%5D%5D+AssetType+in+%28%27Data+in+Motion%27%29&duration=DAY_7&isGenAiOnly=true";
          
            expect(Utils?.generateSensitiveAssetsUrl(sensitiveAssets, title, timeRange)).toBe(expectedUrl);
        });
      
        it("should encode special characters in app name", () => {
            const sensitiveAssets = 3;
            const title = "App With Spaces & Special Chars";
            const timeRange = "HOURS24";
            const expectedUrl = "/data-loss-prevention/data-asset-explorer?filterString=ApplicationName+in+%28%27App+With+Spaces+%26+Special+Chars%27%29+%5B%5B%3A%3AAND%3A%3A%5D%5D+AssetType+in+%28%27Data+in+Motion%27%29&duration=HOUR_24&isGenAiOnly=true";
          
            expect(Utils?.generateSensitiveAssetsUrl(sensitiveAssets, title, timeRange)).toBe(expectedUrl);
        });
      
        it("should use default time range for unknown time range input", () => {
            const sensitiveAssets = 7;
            const title = "DefaultApp";
            const timeRange = "UNKNOWN";
            const expectedUrl = "/data-loss-prevention/data-asset-explorer?filterString=ApplicationName+in+%28%27DefaultApp%27%29+%5B%5B%3A%3AAND%3A%3A%5D%5D+AssetType+in+%28%27Data+in+Motion%27%29&duration=default&isGenAiOnly=true";
          
            expect(Utils?.generateSensitiveAssetsUrl(sensitiveAssets, title, timeRange)).toBe(expectedUrl);
        });
    });
    

    describe("generateAppDetailsUrl", () => {
        it("should generate a correct URL with given app name and time range", () => {
            const appName = "TestApp";
            const timeRange = "HOURS24";
            const expectedUrl = "/insights/activity_insights_app/apps/details?appName=TestApp&timeInterval=24h&una_threat_severity=Critical%2CHigh%2CMedium%2CLow";
          
            expect(Utils?.generateAppDetailsUrl(appName, timeRange)).toBe(expectedUrl);
        });
      
        it("should handle different time ranges correctly", () => {
            const appName = "AnotherApp";
            const timeRange = "DAYS7";
            const expectedUrl = "/insights/activity_insights_app/apps/details?appName=AnotherApp&timeInterval=7d&una_threat_severity=Critical%2CHigh%2CMedium%2CLow";
          
            expect(Utils?.generateAppDetailsUrl(appName, timeRange)).toBe(expectedUrl);
        });
      
        it("should encode special characters in app name", () => {
            const appName = "App With Spaces & Special Chars";
            const timeRange = "HOURS24";
            const expectedUrl = "/insights/activity_insights_app/apps/details?appName=App+With+Spaces+%26+Special+Chars&timeInterval=24h&una_threat_severity=Critical%2CHigh%2CMedium%2CLow";
          
            expect(Utils?.generateAppDetailsUrl(appName, timeRange)).toBe(expectedUrl);
        });
      
        it("should use default time interval for unknown time range", () => {
            const appName = "DefaultApp";
            const timeRange = "UNKNOWN";
            const expectedUrl = "/insights/activity_insights_app/apps/details?appName=DefaultApp&timeInterval=default&una_threat_severity=Critical%2CHigh%2CMedium%2CLow";
          
            expect(Utils?.generateAppDetailsUrl(appName, timeRange)).toBe(expectedUrl);
        });
    });
      
    describe("generateThreatsUrl", () => {
        it("should generate a correct URL with given app name and time range", () => {
            const appName = "TestApp";
            const timeRange = "HOURS24";
            const expectedUrl = "/insights/activity_insights/threats?appName=TestApp&timeInterval=24h&una_threat_severity=Critical%2CHigh%2CMedium%2CLow";
          
            expect(Utils?.generateThreatsUrl(appName, timeRange)).toBe(expectedUrl);
        });
      
        it("should handle different time ranges correctly", () => {
            const appName = "AnotherApp";
            const timeRange = "DAYS7";
            const expectedUrl = "/insights/activity_insights/threats?appName=AnotherApp&timeInterval=7d&una_threat_severity=Critical%2CHigh%2CMedium%2CLow";
          
            expect(Utils?.generateThreatsUrl(appName, timeRange)).toBe(expectedUrl);
        });
      
        it("should encode special characters in app name", () => {
            const appName = "App With Spaces & Special Chars";
            const timeRange = "HOURS24";
            const expectedUrl = "/insights/activity_insights/threats?appName=App+With+Spaces+%26+Special+Chars&timeInterval=24h&una_threat_severity=Critical%2CHigh%2CMedium%2CLow";
            
            expect(Utils.generateThreatsUrl(appName, timeRange)).toBe(expectedUrl);
        });
      
        it("should use default time interval for unknown time range", () => {
            const appName = "DefaultApp";
            const timeRange = "UNKNOWN";
            const expectedUrl = "/insights/activity_insights/threats?appName=DefaultApp&timeInterval=default&una_threat_severity=Critical%2CHigh%2CMedium%2CLow";
          
            expect(Utils?.generateThreatsUrl(appName, timeRange)).toBe(expectedUrl);
        });
    });

    describe("generateActivityInsightsUrl", () => {
        it("should generate a correct URL with given parameters", () => {
            const timeRange = "HOURS24";
            const elementName = "TestElement";
            const useCase = "TestUseCase";
            const expectedUrl = "/insights/activity_insights/apps?timeInterval=24h&una_app_tags=testelement&una_use_cases=TestUseCase&sourceType=Users";
          
            expect(Utils?.generateActivityInsightsUrl(timeRange, elementName, useCase)).toBe(expectedUrl);
        });
      
        it("should handle different time ranges correctly", () => {
            const timeRange = "DAYS7";
            const elementName = "AnotherElement";
            const useCase = "AnotherUseCase";
            const expectedUrl = "/insights/activity_insights/apps?timeInterval=7d&una_app_tags=anotherelement&una_use_cases=AnotherUseCase&sourceType=Users";
          
            expect(Utils?.generateActivityInsightsUrl(timeRange, elementName, useCase)).toBe(expectedUrl);
        });
      
        it("should handle undefined elementName", () => {
            const timeRange = "HOURS24";
            const useCase = "TestUseCase";
            const expectedUrl = "/insights/activity_insights/apps?timeInterval=24h&una_app_tags=&una_use_cases=TestUseCase&sourceType=Users";
          
            expect(Utils?.generateActivityInsightsUrl(timeRange, undefined, useCase)).toBe(expectedUrl);
        });
      
        it("should encode special characters in parameters", () => {
            const timeRange = "HOURS24";
            const elementName = "Element With Spaces & Special Chars";
            const useCase = "Use Case/With:Special#Chars";
            const expectedUrl = "/insights/activity_insights/apps?timeInterval=24h&una_app_tags=element+with+spaces+%26+special+chars&una_use_cases=Use+Case%2FWith%3ASpecial%23Chars&sourceType=Users";
          
            expect(Utils?.generateActivityInsightsUrl(timeRange, elementName, useCase)).toBe(expectedUrl);
        });
    });

    describe("getTableHeading", () => {
        let mockTableHeading: ReturnType<typeof vi.fn>;
        let mockIntlInstance: ReturnType<typeof IntlUtil.getIntl>;
      
        beforeEach(() => {
            // Reset mocks before each test
            vi.restoreAllMocks();
      
            mockIntlInstance = IntlUtil.getIntl();
            mockTableHeading = vi
                .spyOn(useCasesModule, "GET_TABLE_HEADING")
                .mockReturnValue({ heading1: "value1", heading2: "value2" });
        });
      
        it("should call GET_TABLE_HEADING with intlInstance", () => {
            const result = Utils?.getTableHeading();
      
            expect(mockTableHeading).toHaveBeenCalledWith(mockIntlInstance);
            expect(result).toEqual({ heading1: "value1", heading2: "value2" });
        });
    });

    describe("formatTimeFilterForSensitiveAssets", () => {
        beforeEach(() => {
            vi.mocked(Utils.getConstant).mockReturnValue({
                hours: "hours",
                days: "days",
                last_n_hours: "last_n_hours",
                last_n_days: "last_n_days",
            });
        });
      
        it("should format HOURS correctly", () => {
            const result = Utils?.formatTimeFilterForSensitiveAssets("HOURS24");
            expect(result).toEqual({ timeRangeType: "last_n_hours", intervalDuration: 24 });
        });
      
        it("should format DAYS correctly", () => {
            const result = Utils?.formatTimeFilterForSensitiveAssets("DAYS7");
            expect(result).toEqual({ timeRangeType: "last_n_days", intervalDuration: 7 });
        });
      
        it("should return default values for invalid input", () => {
            const result = Utils?.formatTimeFilterForSensitiveAssets("INVALID");
            expect(result).toEqual({ timeRangeType: "last_n_hours", intervalDuration: 1 });
        });
      
        it("should handle empty string input", () => {
            const result = Utils?.formatTimeFilterForSensitiveAssets("");
            expect(result).toEqual({ timeRangeType: "last_n_hours", intervalDuration: 1 });
        });
    });

    describe("tagColorMapping", () => {
        it("should return the correct color for sanctioned tag", () => {
            const result = Utils?.tagColorMapping();
            expect(result.sanctioned).toBe("#33CCB8");
        });
      
        it("should return the correct color for unsanctioned tag", () => {
            const result = Utils?.tagColorMapping();
            expect(result.unsanctioned).toBe("#BC246A");
        });
      
        it("should return the correct color for tolerated tag", () => {
            const result = Utils?.tagColorMapping();
            expect(result.tolerated).toBe("#4290F5");
        });
      
        it("should return an object with exactly three properties", () => {
            const result = Utils?.tagColorMapping();
            expect(Object.keys(result)).toHaveLength(3);
        });
      
        it("should return a new object each time it is called", () => {
            const result1 = Utils?.tagColorMapping();
            const result2 = Utils?.tagColorMapping();
            expect(result1).not.toBe(result2);
            expect(result1).toEqual(result2);
        });
    });

    describe("getShapedTagIcon", () => {
        it("should return correct properties for square shape", () => {
            const result = Utils?.getShapedTagIcon("square", "sanctioned");
            expect(result).toEqual({
                width: 10,
                height: 10,
                color: "#33CCB8"
            });
        });
      
        it("should return correct properties for unsanctioned tag", () => {
            const result = Utils?.getShapedTagIcon("square", "unsanctioned");
            expect(result).toEqual({
                width: 10,
                height: 10,
                color: "#BC246A"
            });
        });
      
        it("should return correct properties for tolerated tag", () => {
            const result = Utils?.getShapedTagIcon("square", "tolerated");
            expect(result).toEqual({
                width: 10,
                height: 10,
                color: "#4290F5"
            });
        });
      
        it("should use default width and height when not provided", () => {
            const result = Utils?.getShapedTagIcon("square", "sanctioned");
            expect(result.width).toBe(10);
            expect(result.height).toBe(10);
        });
      
        it("should use custom width and height when provided", () => {
            const result = Utils?.getShapedTagIcon("square", "sanctioned", 20, 15);
            expect(result.width).toBe(20);
            expect(result.height).toBe(15);
        });
      
        it("should return default properties for unknown shape", () => {
            const result = Utils?.getShapedTagIcon("circle", "sanctioned");
            expect(result).toEqual({
                width: 10,
                height: 10,
                color: "#33CCB8"
            });
        });
      
        it("should return black color for unknown tag", () => {
            const result = Utils?.getShapedTagIcon("square", "unknown" as any);
            expect(result.color).toBe("#000000");
        });
    });

    describe("getReportsTableColumn", () => {
        let mockReportsTableColumn: ReturnType<typeof vi.fn>;
        let mockIntlInstance: ReturnType<typeof IntlUtil.getIntl>;
      
        beforeEach(() => {
            // Reset mocks before each test
            vi.restoreAllMocks();
      
            mockIntlInstance = IntlUtil.getIntl();
            mockReportsTableColumn = vi
                .spyOn(useCasesModule, "GET_REPORTS_TABLE_COLUMN")
                .mockReturnValue({ column1: "value1", column2: "value2" });
        });
      
        it("should call GET_REPORTS_TABLE_COLUMN with intlInstance", () => {
            const result = Utils?.getReportsTableColumn();
      
            expect(mockReportsTableColumn).toHaveBeenCalledWith(mockIntlInstance);
            expect(result).toEqual({ column1: "value1", column2: "value2" });
        });
    });

    describe("getKeyInsightData", () => {
        let mockKeyInsightData: ReturnType<typeof vi.fn>;
        let mockIntlInstance: ReturnType<typeof IntlUtil.getIntl>;
      
        beforeEach(() => {
            // Reset mocks before each test
            vi.restoreAllMocks();
      
            mockIntlInstance = IntlUtil.getIntl();
            mockKeyInsightData = vi
                .spyOn(useCasesModule, "GET_KEY_INSIGHT_DATA")
                .mockReturnValue({ key: "value" });
        });
      
        it("should call GET_KEY_INSIGHT_DATA with intlInstance and provided variable", () => {
            const mockVariable = { test: "data" };
            const result = Utils?.getKeyInsightData(mockVariable);
      
            expect(mockKeyInsightData).toHaveBeenCalledWith(mockIntlInstance, mockVariable);
            expect(result).toEqual({ key: "value" });
        });
      
        it("should call GET_KEY_INSIGHT_DATA with an empty object when variable is undefined", () => {
            const result = Utils?.getKeyInsightData();
      
            expect(mockKeyInsightData).toHaveBeenCalledWith(mockIntlInstance, {});
            expect(result).toEqual({ key: "value" });
        });
    });

    describe("getHistogramConfig", () => {
        it("should return correct config for HOURS1", () => {
            const result = Utils?.getHistogramConfig(TimeRange.HOURS1);
            expect(result).toEqual({ range: "minute", value: "5" });
        });
      
        it("should return correct config for HOURS3", () => {
            const result = Utils?.getHistogramConfig(TimeRange.HOURS3);
            expect(result).toEqual({ range: "minute", value: "5" });
        });
      
        it("should return correct config for HOURS24", () => {
            const result = Utils?.getHistogramConfig(TimeRange.HOURS24);
            expect(result).toEqual({ range: "minute", value: "30" });
        });
      
        it("should return correct config for DAYS7", () => {
            const result = Utils?.getHistogramConfig(TimeRange.DAYS7);
            expect(result).toEqual({ range: "hour", value: "3" });
        });
      
        it("should return correct config for DAYS30", () => {
            const result = Utils?.getHistogramConfig(TimeRange.DAYS30);
            expect(result).toEqual({ range: "hour", value: "12" });
        });
      
        it("should throw an error for invalid time range", () => {
            expect(() => Utils?.getHistogramConfig("INVALID" as TimeRange)).toThrow("Invalid time range option");
        });
    });

    describe("formatTimeIntervalForPDF", () => {
        it("should format DAYS intervals correctly", () => {
            expect(Utils?.formatTimeIntervalForPDF("DAYS30")).toBe("30 Days");
            expect(Utils?.formatTimeIntervalForPDF("DAYS7")).toBe("7 Days");
            expect(Utils?.formatTimeIntervalForPDF("DAYS1")).toBe("1 Days");
        });
      
        it("should format HOURS intervals correctly", () => {
            expect(Utils?.formatTimeIntervalForPDF("HOURS24")).toBe("24 Hours");
            expect(Utils?.formatTimeIntervalForPDF("HOURS1")).toBe("1 Hours");
            expect(Utils?.formatTimeIntervalForPDF("HOURS48")).toBe("48 Hours");
        });
      
        it("should handle lowercase input", () => {
            expect(Utils?.formatTimeIntervalForPDF("days30")).toBe("Invalid Interval");
            expect(Utils?.formatTimeIntervalForPDF("hours24")).toBe("Invalid Interval");
        });
      
        it("should return \"Invalid Interval\" for malformed input", () => {
            expect(Utils?.formatTimeIntervalForPDF("30DAYS")).toBe("Invalid Interval");
            expect(Utils?.formatTimeIntervalForPDF("24HOURS")).toBe("Invalid Interval");
            expect(Utils?.formatTimeIntervalForPDF("DAYSHOURS")).toBe("Invalid Interval");
        });
      
        it("should return \"Unknown Time Unit\" for unrecognized time units", () => {
            expect(Utils?.formatTimeIntervalForPDF("WEEKS2")).toBe("Unknown Time Unit");
            expect(Utils?.formatTimeIntervalForPDF("MONTHS1")).toBe("Unknown Time Unit");
        });
      
        it("should handle empty string input", () => {
            expect(Utils?.formatTimeIntervalForPDF("")).toBe("Invalid Interval");
        });
    });
});
