import React from "react";
import { render, screen } from "@testing-library/react";
import { beforeEach, describe, expect, it, vi } from "vitest";
import { MemoryRouter } from "@sparky/framework/router";
import AiAccess from "../AiAccess";
import * as sparkyFramework from "@sparky/framework";

// Mock dependencies
vi.mock("@sparky/framework", () => ({
    getFeatureFlag: vi.fn(),
    useStoreSelector: vi.fn(),
}));

vi.mock("../components/LandingPage/LandingPage", () => ({
    default: () => <div data-testid="landing-page">Landing Page</div>,
}));

vi.mock("../components/UseCaseDetail/UseCaseDetail", () => ({
    default: () => <div data-testid="use-case-detail">Use Case Detail</div>,
}));

vi.mock("../../netsec-ui/utils/ThemeProvider", () => ({
    default: {
        Provider: ({ children, value }) => (
            <div data-testid="theme-provider" data-theme={value.mode}>
                {children}
            </div>
        ),
    },
}));

describe("AiAccess", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    it("renders LandingPage for root path with V3 navigation", () => {
        vi.mocked(sparkyFramework.getFeatureFlag).mockReturnValue(true);
        vi.mocked(sparkyFramework.useStoreSelector).mockReturnValue("light");

        render(
            <MemoryRouter initialEntries={["/insights/security/ai-access"]}>
                <AiAccess />
            </MemoryRouter>
        );

        expect(screen.getByTestId("landing-page")).toBeInTheDocument();
    });

    it("renders LandingPage for root path without V3 navigation", () => {
        vi.mocked(sparkyFramework.getFeatureFlag).mockReturnValue(false);
        vi.mocked(sparkyFramework.useStoreSelector).mockReturnValue("light");

        render(
            <MemoryRouter initialEntries={["/insights/ai-access"]}>
                <AiAccess />
            </MemoryRouter>
        );

        expect(screen.getByTestId("landing-page")).toBeInTheDocument();
    });

    it("renders UseCaseDetail for use case path with V3 navigation", () => {
        vi.mocked(sparkyFramework.getFeatureFlag).mockReturnValue(true);
        vi.mocked(sparkyFramework.useStoreSelector).mockReturnValue("light");

        render(
            <MemoryRouter initialEntries={["/insights/security/ai-access/use-case/123"]}>
                <AiAccess />
            </MemoryRouter>
        );

        expect(screen.getByTestId("use-case-detail")).toBeInTheDocument();
    });

    it("renders UseCaseDetail for use case path without V3 navigation", () => {
        vi.mocked(sparkyFramework.getFeatureFlag).mockReturnValue(false);
        vi.mocked(sparkyFramework.useStoreSelector).mockReturnValue("light");

        render(
            <MemoryRouter initialEntries={["/insights/ai-access/use-case/123"]}>
                <AiAccess />
            </MemoryRouter>
        );

        expect(screen.getByTestId("use-case-detail")).toBeInTheDocument();
    });

    it("uses dark theme when default theme is dark", () => {
        vi.mocked(sparkyFramework.getFeatureFlag).mockReturnValue(false);
        vi.mocked(sparkyFramework.useStoreSelector).mockReturnValue("dark");

        render(
            <MemoryRouter initialEntries={["/insights/ai-access"]}>
                <AiAccess />
            </MemoryRouter>
        );

        const themeProvider = screen.getByTestId("theme-provider");
        expect(themeProvider).toHaveAttribute("data-theme", "dark");
    });

    it("uses light theme when default theme is not dark", () => {
        vi.mocked(sparkyFramework.getFeatureFlag).mockReturnValue(false);
        vi.mocked(sparkyFramework.useStoreSelector).mockReturnValue("light");

        render(
            <MemoryRouter initialEntries={["/insights/ai-access"]}>
                <AiAccess />
            </MemoryRouter>
        );

        const themeProvider = screen.getByTestId("theme-provider");
        expect(themeProvider).toHaveAttribute("data-theme", "light");
    });
});