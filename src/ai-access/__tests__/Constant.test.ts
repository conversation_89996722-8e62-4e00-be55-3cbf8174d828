import { describe, expect, it } from "vitest";
import { createIntl, IntlShape } from "react-intl";
import {
    GET_CONSTANT,
    GET_CONSTANT_REPORT,
    GET_KEY_INSIGHT_DATA,
    GET_REPORTS_TABLE_COLUMN,
    GET_TABLE_HEADING,
    GET_TAG,
    GET_USECASE_FILTER,
    USE_CASE_ID,
    USE_CASES
} from "../Constant";

const messages = {
    "ai_access.usecase_card.unsanctioned": "Mock Unsanctioned",
    "ai_access.usecase_card.sanctioned": "Mock Sanctioned",
    "ai_access.usecase_card.tolerated": "Mock Tolerated",
    "ai_access.use_case_highlights.audio-generator.subtitle": "Audio Generators leverage AI models to create sound effects,\n                 music, and other audio clips from text prompts or audio reference inputs.",
};

const intl = createIntl({ locale: "en", messages });

describe("Constant", () => {
    const mockIntlShape: IntlShape = {
        formatMessage: ({ defaultMessage }, values) => {
            let message = defaultMessage;
            if (values) {
                Object.keys(values).forEach(key => {
                    message = message.replace(`{${key}}`, values[key]);
                });
            }
            return message;
        },
    } as IntlShape;

    it("should return a valid object structure for a given themeMode", () => {
        const themeMode = "dark";
        const useCases = USE_CASES(intl, themeMode);

        expect(useCases).toBeInstanceOf(Object);
        expect(useCases).toHaveProperty("audio-generator");
        expect(useCases["audio-generator"]).toHaveProperty("title", "Audio Generator");
        expect(useCases["audio-generator"]).toHaveProperty("icon");
        expect(useCases["audio-generator"].icon).not.toBeNull();
    });

    it("should return different icons based on themeMode", () => {
        const darkModeUseCases = USE_CASES(intl, "dark");
        const lightModeUseCases = USE_CASES(intl, "light");

        expect(darkModeUseCases["audio-generator"].icon).not.toEqual(
            lightModeUseCases["audio-generator"].icon
        );
    });

    it("should correctly format messages using intlInstance", () => {
        const useCases = USE_CASES(intl, "dark");
        
        expect(useCases["audio-generator"].subtitle).toBe(intl.formatMessage({
            id: "ai_access.use_case_highlights.audio-generator.subtitle",
            defaultMessage:
            "Audio Generators leverage AI models to create sound effects,\n                 music, and other audio clips from text prompts or audio reference inputs.",
        }));
    });

    it("should include expected use cases", () => {
        const useCases = USE_CASES(intl, "dark");
        const expectedUseCases = [
            "audio-generator",
            "conversational-agent",
            "code-assistant-generator",
            "developer-platform",
            "enterprise-search",
            "image-editor-generator",
            "meeting-assistant",
            "productivity-assistant",
        ];

        expectedUseCases.forEach((useCase) => {
            expect(useCases).toHaveProperty(useCase);
            expect(useCases[useCase]).toHaveProperty("title");
            expect(useCases[useCase]).toHaveProperty("subtitle");
            expect(useCases[useCase]).toHaveProperty("icon");
        });
    });

    it("should return GET_TAG object", () => {
        const tags = GET_TAG(mockIntlShape);
        expect(tags).toBeTypeOf("object");
        expect(tags.unsanctioned).toBe("Unsanctioned");
        expect(tags.sanctioned).toBe("Sanctioned");
        expect(tags.tolerated).toBe("Tolerated");
    });

    it("should return GET_CONSTANT object", () => {
        const constants = GET_CONSTANT(mockIntlShape);
        expect(constants).toBeTypeOf("object");
        expect(constants.cancel).toBe("Cancel");
        expect(constants.confirmChange).toBe("Confirm Change");
    });

    it("should return GET_USECASE_FILTER object", () => {
        const filters = GET_USECASE_FILTER(mockIntlShape);
        expect(filters).toBeTypeOf("object");
        expect(filters.All).toBe("All");
        expect(filters.risk).toBe("Risk");
    });

    it("should return GET_TABLE_HEADING object", () => {
        const headings = GET_TABLE_HEADING(mockIntlShape);
        expect(headings).toBeTypeOf("object");
        expect(headings.name).toBe("Name");
        expect(headings.classification).toBe("Classification");
    });

    it("should return GET_CONSTANT_REPORT object", () => {
        const variable = {
            totalApplications: "10",
            totalUsers: "100",
            applications: "5"
        };
        const report = GET_CONSTANT_REPORT(mockIntlShape, variable);
        expect(report).toBeTypeOf("object");
        expect(report.strataCloudManager).toBe("STRATA CLOUD MANAGER");
        expect(report.executiveSummary).toBe("Executive Summary");
        expect(report.executiveSummaryContent).toContain("10");
        expect(report.executiveSummaryContent).toContain("100");
    });

    it("should return GET_REPORTS_TABLE_COLUMN object", () => {
        const columns = GET_REPORTS_TABLE_COLUMN(mockIntlShape);
        expect(columns).toBeTypeOf("object");
        expect(columns.application).toBe("Application");
        expect(columns.risk).toBe("Risk");
    });

    it("should return GET_KEY_INSIGHT_DATA object", () => {
        const variable = { allowedCount: 5, total: 10, sanctioned: 3, tolerated: 2, percentage: 20, dlpSupportedApp: "TestApp" };
        const insights = GET_KEY_INSIGHT_DATA(mockIntlShape, variable);
        expect(insights).toBeTypeOf("object");
        expect(insights.traffic_none).toBeDefined();
        expect(insights.gen_ai_app_total).toContain("10");
        expect(insights.gen_ai_app_total).toContain("3");
        expect(insights.gen_ai_app_total).toContain("2");
    });
});
