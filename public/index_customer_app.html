<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1,shrink-to-fit=no">
  <title>command-center - Local</title>
</head>

<body><noscript>You need to enable JavaScript to run this app.</noscript>
  <sparky-root id="fawkes-root" class="loading"></sparky-root>
  <script data-sparky-loader
    data-micro-apps="sparky-framework,pai_oneapp,command-center,sparky-one-nav,aiops-for-ngfw,reporting_oneapp,cloud-mgmt,dashboards-core-service,cloud-mgmt,saas-inline-fawkes,ztna-connector,sdwan,sase_ia,dem_oneapp,policy-analyzer,netsec-co-pilot,dlp"
    data-overrides='[{
      "name": "command-center",
      "exports": "__COMMAND_CENTER",
      "entry": "/loader.js"
      }, {
      "name": "sparky-framework",
      "+variables": {
        "default_route": "/dashboards"
      }
      },
      {
        "name": "pai",
        "id": "pai_oneapp",
        "variables": {
            "apiServer":"https://pa-api-us-qa01.tools.panclouddev.com"
        }
    }]'
    src="https://stratacloudmanager.qa.appsvc.paloaltonetworks.com/loader.js"></script>
</body>

</html>
