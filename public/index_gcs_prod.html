<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <style>
        iframe {
            display: none;
        }
    </style>
    <title>SASE IA Unified App</title>
</head>
<body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="fawkes-root" class="loading"></div>

    <script data-sparky-loader
        data-micro-apps="sparky-framework,sparky-one-nav,pai_oneapp,netsec-co-pilot,saas-inline-fawkes,management-app,cloud-mgmt,sase_ia,dashboards-core-service,dem_oneapp"
        data-overrides='[
            {
                "name": "sase_ia",
                "entry": "/loader.js"
            },
            {
                "name": "command-center",
                "exports": "__COMMAND_CENTER",
                "generated_id": "",
                "entry": "https://localhost.appsvc.paloaltonetworks.com:5443/loader.js",
                "variables": {
                    "apiServer": "https://pa-us01.api.prismaaccess.com",
                    "gc_fqdn": "https://pa-us01.api.prismaaccess.com"
                }
            },
            {
                "name": "sparky-framework",
                "+variables": {
                    "use_new_left_nav": true,
                    "private_site_origin": "https://sparky.dev.localhost:3443",
                    "oidc_client":{
                        "client_id": "pa-gcs",
                        "config_url": "https://forgerock.dev.appsvc.pan.local/am/oauth2/realms/root/.well-known/openid-configuration",
                        "scope": "openid profile email"
                    }
                }
            }]'
        src="https://pan-gov-sparky-shell-pa-gcs.web.app/loader.js">
    </script>
</body>
</html>
