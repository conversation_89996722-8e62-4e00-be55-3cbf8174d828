localizationDiff="$(git diff --shortstat src/i18n/extractedMessages/messages.json)"
localizationDiffCount="$(git diff --shortstat src/i18n/extractedMessages/messages.json | wc -l)"
if [ $localizationDiffCount -eq 0 ]
then
  echo "No Localization changes identified"
else
  echo "Identified unstaged localization changes. These have to be staged before commit."
  echo "Changes: $localizationDiff"
  exit 1
fi