#!/usr/bin/env bash

# This script needs to be run before the end of the month so that translations team can pick up the new strings and translate
# Usage: ./i18n_upload_files_to_be_translated.sh <param1> <param2> 
# param1 is directory where https://code.pan.run/fawkes/ui_localization repo is located
# param2 is directory where cc repo is located
# Example - ./i18n_upload_files_to_translate.sh /Users/<USER>/ui_localization /Users/<USER>/command-center/command-center-ui
# Output - 1. Script will provide a MR link for the localization repo which can be used to merge to that repo
# 2. Once merged the feature/publish artifact pipeline will generate a version which will need to be updated in the package.json
localizationRepoPath=$1
ccRepoPath=$2
datetimeText=`date +"%Y.%m.%d.%H%M%S"`
newBranch="command-center_localization_${datetimeText}"

echo '*** Getting latest CC UI repo release branch'
cd $ccRepoPath
git checkout release; git pull
result=$?
echo $result
if [ "$result" != "0" ]
then
  echo  '*** Operation failed'
  exit 1
fi


echo '*** Get latest localization repo feature/publish branch'
cd $localizationRepoPath
git checkout feature/publish; git pull
result=$?
if [ "$result" != "0" ]
then
  echo  '*** Operation failed'
  exit 1
fi

echo '*** Creating $newBranch branch'
git checkout -b $newBranch
if [ "$result" != "0" ]
then
  echo  '*** Operation failed'
  exit 1
fi

echo *** Copying the messages.json to $localizationRepoPath/src/translate/command-center/ui/en/
cp -f $ccRepoPath/src/i18n/extractedMessages/messages.json $localizationRepoPath/src/translate/command-center/ui/en/
git add $localizationRepoPath/src/translate/command-center/ui/en/messages.json
checkinMessage="feat: command-center text extraction as of ${datetimeText}"
echo '*** Committing and pushing the message.json '
git commit -m "${checkinMessage}"
git push

echo
echo "Open https://code.pan.run/fawkes/ui_localization/-/merge_requests to create an MR to merge ${newBranch} to feature/publish."


