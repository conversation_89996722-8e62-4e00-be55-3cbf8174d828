{"url": "https://sase-saas-api.staging3.cirrotester.com/inline/v2/netsec/top_data_profiles", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418843053, 1738010843053]}, {"property": "is_gen_ai_only", "operator": "equals", "values": [true]}]}}, "response": [{"channel_name": "all_channels", "top_data_profiles": [{"severity": "very_high", "data_profile_name": "Default Data Patterns", "assets_by_channels": [{"name": "data_in_motion", "total_assets": 413}, {"name": "data_at_rest", "total_assets": 85}]}, {"severity": "very_high", "data_profile_name": "Prisma-Inline-DLP-NGFW", "assets_by_channels": [{"name": "data_in_motion", "total_assets": 41}, {"name": "data_at_rest", "total_assets": 11}]}, {"severity": "very_high", "data_profile_name": "Test-DLP-Profile", "assets_by_channels": [{"name": "data_in_motion", "total_assets": 3}, {"name": "data_at_rest", "total_assets": 9}]}, {"severity": "very_high", "data_profile_name": "PANW - Source Code ML only", "assets_by_channels": [{"name": "data_in_motion", "total_assets": 45}, {"name": "data_at_rest", "total_assets": 47}]}, {"severity": "very_high", "data_profile_name": "PANW - Customer Data v3", "assets_by_channels": [{"name": "data_in_motion", "total_assets": 4138}, {"name": "data_at_rest", "total_assets": 306}]}]}, {"channel_name": "data_in_motion", "top_data_profiles": [{"severity": "very_high", "data_profile_name": "Default Data Patterns", "assets_by_channels": [{"name": "data_in_motion", "total_assets": 413}]}, {"severity": "very_high", "data_profile_name": "Prisma-Inline-DLP-NGFW", "assets_by_channels": [{"name": "data_in_motion", "total_assets": 41}]}, {"severity": "very_high", "data_profile_name": "Test-DLP-Profile", "assets_by_channels": [{"name": "data_in_motion", "total_assets": 3}]}, {"severity": "very_high", "data_profile_name": "PANW - Source Code ML only", "assets_by_channels": [{"name": "data_in_motion", "total_assets": 45}]}, {"severity": "very_high", "data_profile_name": "PANW - Customer Data v3", "assets_by_channels": [{"name": "data_in_motion", "total_assets": 4138}]}]}, {"channel_name": "data_at_rest", "top_data_profiles": [{"severity": "very_high", "data_profile_name": "Default Data Patterns", "assets_by_channels": [{"name": "data_at_rest", "total_assets": 85}]}, {"severity": "very_high", "data_profile_name": "Prisma-Inline-DLP-NGFW", "assets_by_channels": [{"name": "data_at_rest", "total_assets": 11}]}, {"severity": "very_high", "data_profile_name": "Test-DLP-Profile", "assets_by_channels": [{"name": "data_at_rest", "total_assets": 9}]}, {"severity": "very_high", "data_profile_name": "PANW - Source Code ML only", "assets_by_channels": [{"name": "data_at_rest", "total_assets": 47}]}, {"severity": "very_high", "data_profile_name": "PANW - Customer Data v3", "assets_by_channels": [{"name": "data_at_rest", "total_assets": 306}]}]}], "status": 200, "headers": {"content-length": "2418", "content-type": "application/json; charset=utf-8"}}