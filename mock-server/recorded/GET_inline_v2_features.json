{"url": "https://sase-saas-api.staging3.cirrotester.com/inline/v2/features", "method": "GET", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418831468, 1738010831468]}]}}, "response": {"overall_status": "enabled", "datasecurity_status": "enabled", "aiaccess_status": "enabled", "saas_inline_only_status": "no_license", "products": [{"name": "saas_api", "status": "enabled"}, {"name": "email_dlp", "status": "enabled"}, {"name": "prisma_access_browser", "status": "enabled"}, {"name": "inline_dlp", "status": "enabled"}, {"name": "sspm", "status": "enabled"}, {"name": "saas_inline", "status": "enabled"}, {"name": "endpoint_dlp", "status": "enabled"}], "pab_display_mode": "enterprise"}, "status": 200, "headers": {"content-length": "464", "content-type": "application/json; charset=utf-8"}}