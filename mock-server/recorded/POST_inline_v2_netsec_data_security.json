{"url": "https://sase-saas-api.staging3.cirrotester.com/inline/v2/netsec/data_security", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418843050, 1738010843050]}]}}, "response": {"users": {"total_sensitive_users": 24341}, "total_assets": 23549818, "total_assets_trend": 0, "channels": [{"users": {"total_sensitive_users": 15007}, "channel_name": "data_in_motion", "status": "enabled", "total_assets": 243095, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 19402, "assets_by_action": [{"action": "allowed", "total_assets": 2}, {"action": "blocked", "total_assets": 19350}]}, {"severity": "low", "total_assets": 224451, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 224411}]}], "total_assets": 243095, "total_users": 15007}, "channel_details": {"asset_actions": [{"action": "allowed", "total_assets": 2}, {"action": "blocked", "total_assets": 243761}], "sub_channels_details": [{"name": "inline_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 100, "total_incidents": 7347}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 3936, "total_incidents": 64752}], "assets_by_actions": [{"action": "allowed", "trend": -33, "total_assets": 2}, {"action": "blocked", "trend": 1667, "total_assets": 26790}], "assets_by_exposure": [], "total_users": 3264, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 19352, "assets_by_action": [{"action": "allowed", "total_assets": 2}, {"action": "blocked", "total_assets": 19350}]}, {"severity": "low", "total_assets": 7440, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 7440}]}], "total_assets": 26920, "total_users": 3264}}, {"name": "prisma_access_browser", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 18, "total_incidents": 216873}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 18, "total_assets": 216920}], "assets_by_exposure": [], "total_users": 14303, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 51, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 216958, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 216920}]}], "total_assets": 216981, "total_users": 14303}}, {"name": "email_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}, {"name": "endpoint_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}]}}, {"users": {"total_sensitive_users": 24216}, "channel_name": "data_at_rest", "status": "enabled", "total_assets": 23306723, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "high", "total_assets": 1964, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 19876237, "assets_by_action": [{"action": "quarantined", "total_assets": 2050}, {"action": "sharing_revoked", "total_assets": 3}]}, {"severity": "low", "total_assets": 3587377, "assets_by_action": [{"action": "quarantined", "total_assets": 1267}, {"action": "sharing_revoked", "total_assets": 0}]}], "total_assets": 23306723, "total_users": 24216}, "channel_details": {"asset_actions": [{"action": "sharing_revoked", "total_assets": 3}, {"action": "quarantined", "total_assets": 3317}, {"action": "exposed", "total_assets": 9864798}], "sub_channels_details": [{"name": "saas_api", "incidents_by_severity": [{"severity": "very_high", "trend": 38, "total_incidents": 39260}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 311, "total_incidents": 93134}], "assets_by_actions": [{"action": "quarantined", "trend": -9, "total_assets": 3317}, {"action": "sharing_revoked", "trend": -57, "total_assets": 3}], "assets_by_exposure": [{"exposure": "public", "trend": 48, "total_assets": 3400}, {"exposure": "external", "trend": 1, "total_assets": 9861398}], "total_users": 24216, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "high", "total_assets": 1964, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 19876237, "assets_by_action": [{"action": "quarantined", "total_assets": 2050}, {"action": "sharing_revoked", "total_assets": 3}]}, {"severity": "low", "total_assets": 3587377, "assets_by_action": [{"action": "quarantined", "total_assets": 1267}, {"action": "sharing_revoked", "total_assets": 0}]}], "total_assets": 23306723, "total_users": 24216}}]}}], "tags": [{"users": {}, "tag_name": "sanctioned", "total_apps": 0, "total_discovered_apps": 59311, "top_apps": [], "name": "sanctioned", "type": "tag", "tag": {"total_apps": 0, "total_discovered_apps": 59311, "top_apps": []}}, {"users": {"total_sensitive_users": 24341}, "tag_name": "unsanctioned", "total_apps": 149, "total_discovered_apps": 59311, "top_apps": ["gmail-base", "google-drive-web-base", "slack-base", "amazon-simple-storag", "ms-office365-base", "unknown", "zoom-base", "salesforce-base", "google-docs-base", "jira-base", "google-cloud-console", "ssl", "openai-chatgpt", "confluence-base", "web-browsing", "github-base", "amazon-aws-console", "slack-uploading", "deepl-base", "notion-base", "service-now-base", "canva-base", "github-copilot", "google-base", "windows-azure-base", "sourcegraph-base", "openai-base", "grammarly", "concur-base", "outreach-base", "whatsapp-base", "gitlab-base", "sourcegraph-cody", "google-keep-base", "linkedin-base", "virustotal-base", "youtube-livechat-viewing", "facebook-base", "lucid", "asana-base", "paloalto-strata-cloud-manager", "workday-base", "office365-consumer-access", "yahoo-mail-base", "google-translate-base", "microsoft-e-online", "cortex-xdr", "quillbot", "amazon-sagemaker-groundtruth", "claude-base", "adobe-express-base", "claude-post", "brightcove", "workday-downloading", "ms-teams", "github-copilot-chat-business", "splash-base", "google-app-engine", "ms-visual-studio-tfs-base", "deepl-write", "openai-chatgpt-uploading", "amazon-bedrock-base", "slack-editing", "glassdoor-base", "slack-sharing", "docusign-base", "google-meet-base", "github-posting", "cloudshare", "google-app-script-base", "browserstack", "censysgpt", "lucidchart", "jasper-ai-base", "linkedin-mail", "prisma-cloud-compute-defender", "claude-upload", "snowflake-base", "stackoverflow-base", "datadog", "airtable-base", "cigna-envoy", "amazon-titan-base", "openai-api", "evernote-base", "figma-base", "disneyplus", "ms-teams-posting", "wufoo", "speedtest", "notion-delete", "excalidraw", "azure-openai", "perplexity-ai-base", "klue", "splunk", "slides", "draw.io-base", "http-audio", "yahoo-calendar", "amazon-chime-base", "zoominfo", "canva-uploading", "smartsheet-base", "bonusly-base", "reddit-base", "microsoft-outlook", "ms-office365-copilot", "onetrust-com", "okta", "ms-onenote-base", "smallpdf-base", "google-gemini", "monica-im", "glean-app", "confluence-downloading", "office365-enterprise-access", "youtube-base", "confluence-atlassian-intel", "drift-base", "ms-teams-downloading", "linkedin-uploading", "bing-ai-base", "checkout.com", "resemble", "pandora", "workday-editing", "figma-figjam-ai", "monday", "lumapps", "goblin-tools", "appdynamics", "pagerduty-base", "paloalto-logging-service", "royal-mail", "cisco-umbrella-base", "http-video", "twitter-posting", "ms-outlook-personal-uploading", "paypal", "celonis-base", "bing-chat", "<PERSON><PERSON>", "lastpass", "google-classroom", "perplexity-ai-post", "telegram-base", "hotmail", "workday-uploading"], "name": "unsanctioned", "type": "tag", "tag": {"total_apps": 149, "total_discovered_apps": 59311, "top_apps": ["gmail-base", "google-drive-web-base", "slack-base", "amazon-simple-storag", "ms-office365-base", "unknown", "zoom-base", "salesforce-base", "google-docs-base", "jira-base", "google-cloud-console", "ssl", "openai-chatgpt", "confluence-base", "web-browsing", "github-base", "amazon-aws-console", "slack-uploading", "deepl-base", "notion-base", "service-now-base", "canva-base", "github-copilot", "google-base", "windows-azure-base", "sourcegraph-base", "openai-base", "grammarly", "concur-base", "outreach-base", "whatsapp-base", "gitlab-base", "sourcegraph-cody", "google-keep-base", "linkedin-base", "virustotal-base", "youtube-livechat-viewing", "facebook-base", "lucid", "asana-base", "paloalto-strata-cloud-manager", "workday-base", "office365-consumer-access", "yahoo-mail-base", "google-translate-base", "microsoft-e-online", "cortex-xdr", "quillbot", "amazon-sagemaker-groundtruth", "claude-base", "adobe-express-base", "claude-post", "brightcove", "workday-downloading", "ms-teams", "github-copilot-chat-business", "splash-base", "google-app-engine", "ms-visual-studio-tfs-base", "deepl-write", "openai-chatgpt-uploading", "amazon-bedrock-base", "slack-editing", "glassdoor-base", "slack-sharing", "docusign-base", "google-meet-base", "github-posting", "cloudshare", "google-app-script-base", "browserstack", "censysgpt", "lucidchart", "jasper-ai-base", "linkedin-mail", "prisma-cloud-compute-defender", "claude-upload", "snowflake-base", "stackoverflow-base", "datadog", "airtable-base", "cigna-envoy", "amazon-titan-base", "openai-api", "evernote-base", "figma-base", "disneyplus", "ms-teams-posting", "wufoo", "speedtest", "notion-delete", "excalidraw", "azure-openai", "perplexity-ai-base", "klue", "splunk", "slides", "draw.io-base", "http-audio", "yahoo-calendar", "amazon-chime-base", "zoominfo", "canva-uploading", "smartsheet-base", "bonusly-base", "reddit-base", "microsoft-outlook", "ms-office365-copilot", "onetrust-com", "okta", "ms-onenote-base", "smallpdf-base", "google-gemini", "monica-im", "glean-app", "confluence-downloading", "office365-enterprise-access", "youtube-base", "confluence-atlassian-intel", "drift-base", "ms-teams-downloading", "linkedin-uploading", "bing-ai-base", "checkout.com", "resemble", "pandora", "workday-editing", "figma-figjam-ai", "monday", "lumapps", "goblin-tools", "appdynamics", "pagerduty-base", "paloalto-logging-service", "royal-mail", "cisco-umbrella-base", "http-video", "twitter-posting", "ms-outlook-personal-uploading", "paypal", "celonis-base", "bing-chat", "<PERSON><PERSON>", "lastpass", "google-classroom", "perplexity-ai-post", "telegram-base", "hotmail", "workday-uploading"]}}, {"users": {}, "tag_name": "tolerated", "total_apps": 0, "total_discovered_apps": 59311, "top_apps": [], "name": "tolerated", "type": "tag", "tag": {"total_apps": 0, "total_discovered_apps": 59311, "top_apps": []}}, {"name": "peripherals", "type": "peripherals"}], "channels_to_tags": [{"users": {}, "channel_name": "data_in_motion", "status": "enabled", "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}, "channel_details": {"asset_actions": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}], "sub_channels_details": [{"name": "inline_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}, {"name": "prisma_access_browser", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}, {"name": "email_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}, {"name": "endpoint_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}]}, "tag": {"users": {}, "tag_name": "sanctioned", "total_apps": 0, "total_discovered_apps": 59311, "top_apps": [], "name": "sanctioned", "type": "tag", "tag": {"total_apps": 0, "total_discovered_apps": 59311, "top_apps": []}}}, {"users": {"total_sensitive_users": 15007}, "channel_name": "data_in_motion", "status": "enabled", "total_assets": 243095, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 19402, "assets_by_action": [{"action": "allowed", "total_assets": 2}, {"action": "blocked", "total_assets": 19350}]}, {"severity": "low", "total_assets": 224451, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 224411}]}], "total_assets": 243095, "total_users": 15007}, "channel_details": {"asset_actions": [{"action": "allowed", "total_assets": 2}, {"action": "blocked", "total_assets": 243761}], "sub_channels_details": [{"name": "inline_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 100, "total_incidents": 7347}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 3936, "total_incidents": 64752}], "assets_by_actions": [{"action": "allowed", "trend": -33, "total_assets": 2}, {"action": "blocked", "trend": 1667, "total_assets": 26790}], "assets_by_exposure": [], "total_users": 3264, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 19352, "assets_by_action": [{"action": "allowed", "total_assets": 2}, {"action": "blocked", "total_assets": 19350}]}, {"severity": "low", "total_assets": 7440, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 7440}]}], "total_assets": 26920, "total_users": 3264}}, {"name": "prisma_access_browser", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 18, "total_incidents": 216873}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 18, "total_assets": 216920}], "assets_by_exposure": [], "total_users": 14303, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 51, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 216958, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 216920}]}], "total_assets": 216981, "total_users": 14303}}, {"name": "email_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}, {"name": "endpoint_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}]}, "tag": {"users": {"total_sensitive_users": 15007}, "tag_name": "unsanctioned", "total_apps": 146, "total_discovered_apps": 59311, "top_apps": ["unknown", "salesforce-base", "google-docs-base", "google-cloud-console", "gmail-base", "ssl", "openai-chatgpt", "slack-base", "web-browsing", "amazon-aws-console", "slack-uploading", "deepl-base", "notion-base", "service-now-base", "canva-base", "github-copilot", "google-base", "windows-azure-base", "sourcegraph-base", "openai-base", "grammarly", "google-drive-web-base", "concur-base", "outreach-base", "whatsapp-base", "gitlab-base", "ms-office365-base", "sourcegraph-cody", "google-keep-base", "linkedin-base", "virustotal-base", "youtube-livechat-viewing", "facebook-base", "lucid", "asana-base", "paloalto-strata-cloud-manager", "workday-base", "office365-consumer-access", "yahoo-mail-base", "google-translate-base", "cortex-xdr", "quillbot", "github-base", "amazon-sagemaker-groundtruth", "claude-base", "adobe-express-base", "claude-post", "brightcove", "workday-downloading", "ms-teams", "github-copilot-chat-business", "splash-base", "google-app-engine", "ms-visual-studio-tfs-base", "deepl-write", "openai-chatgpt-uploading", "amazon-bedrock-base", "slack-editing", "glassdoor-base", "slack-sharing", "docusign-base", "google-meet-base", "github-posting", "cloudshare", "lucidchart", "google-app-script-base", "censysgpt", "browserstack", "jasper-ai-base", "linkedin-mail", "prisma-cloud-compute-defender", "snowflake-base", "claude-upload", "stackoverflow-base", "datadog", "airtable-base", "confluence-base", "cigna-envoy", "amazon-titan-base", "openai-api", "ms-teams-posting", "evernote-base", "figma-base", "speedtest", "disneyplus", "wufoo", "notion-delete", "excalidraw", "perplexity-ai-base", "azure-openai", "splunk", "jira-base", "bonusly-base", "canva-uploading", "zoominfo", "amazon-chime-base", "reddit-base", "klue", "smartsheet-base", "http-audio", "draw.io-base", "yahoo-calendar", "slides", "office365-enterprise-access", "onetrust-com", "google-gemini", "ms-onenote-base", "monica-im", "smallpdf-base", "microsoft-outlook", "ms-office365-copilot", "drift-base", "okta", "confluence-downloading", "youtube-base", "confluence-atlassian-intel", "glean-app", "http-video", "paypal", "bing-ai-base", "perplexity-ai-post", "lastpass", "appdynamics", "hotmail", "paloalto-logging-service", "royal-mail", "cisco-umbrella-base", "workday-editing", "pagerduty-base", "checkout.com", "ms-teams-downloading", "celonis-base", "workday-uploading", "lumapps", "twitter-posting", "goblin-tools", "linkedin-uploading", "ms-outlook-personal-uploading", "<PERSON><PERSON>", "figma-figjam-ai", "google-classroom", "monday", "bing-chat", "resemble", "pandora", "telegram-base"], "name": "unsanctioned", "type": "tag", "tag": {"total_apps": 146, "total_discovered_apps": 59311, "top_apps": ["unknown", "salesforce-base", "google-docs-base", "google-cloud-console", "gmail-base", "ssl", "openai-chatgpt", "slack-base", "web-browsing", "amazon-aws-console", "slack-uploading", "deepl-base", "notion-base", "service-now-base", "canva-base", "github-copilot", "google-base", "windows-azure-base", "sourcegraph-base", "openai-base", "grammarly", "google-drive-web-base", "concur-base", "outreach-base", "whatsapp-base", "gitlab-base", "ms-office365-base", "sourcegraph-cody", "google-keep-base", "linkedin-base", "virustotal-base", "youtube-livechat-viewing", "facebook-base", "lucid", "asana-base", "paloalto-strata-cloud-manager", "workday-base", "office365-consumer-access", "yahoo-mail-base", "google-translate-base", "cortex-xdr", "quillbot", "github-base", "amazon-sagemaker-groundtruth", "claude-base", "adobe-express-base", "claude-post", "brightcove", "workday-downloading", "ms-teams", "github-copilot-chat-business", "splash-base", "google-app-engine", "ms-visual-studio-tfs-base", "deepl-write", "openai-chatgpt-uploading", "amazon-bedrock-base", "slack-editing", "glassdoor-base", "slack-sharing", "docusign-base", "google-meet-base", "github-posting", "cloudshare", "lucidchart", "google-app-script-base", "censysgpt", "browserstack", "jasper-ai-base", "linkedin-mail", "prisma-cloud-compute-defender", "snowflake-base", "claude-upload", "stackoverflow-base", "datadog", "airtable-base", "confluence-base", "cigna-envoy", "amazon-titan-base", "openai-api", "ms-teams-posting", "evernote-base", "figma-base", "speedtest", "disneyplus", "wufoo", "notion-delete", "excalidraw", "perplexity-ai-base", "azure-openai", "splunk", "jira-base", "bonusly-base", "canva-uploading", "zoominfo", "amazon-chime-base", "reddit-base", "klue", "smartsheet-base", "http-audio", "draw.io-base", "yahoo-calendar", "slides", "office365-enterprise-access", "onetrust-com", "google-gemini", "ms-onenote-base", "monica-im", "smallpdf-base", "microsoft-outlook", "ms-office365-copilot", "drift-base", "okta", "confluence-downloading", "youtube-base", "confluence-atlassian-intel", "glean-app", "http-video", "paypal", "bing-ai-base", "perplexity-ai-post", "lastpass", "appdynamics", "hotmail", "paloalto-logging-service", "royal-mail", "cisco-umbrella-base", "workday-editing", "pagerduty-base", "checkout.com", "ms-teams-downloading", "celonis-base", "workday-uploading", "lumapps", "twitter-posting", "goblin-tools", "linkedin-uploading", "ms-outlook-personal-uploading", "<PERSON><PERSON>", "figma-figjam-ai", "google-classroom", "monday", "bing-chat", "resemble", "pandora", "telegram-base"]}}}, {"users": {}, "channel_name": "data_in_motion", "status": "enabled", "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}, "channel_details": {"asset_actions": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}], "sub_channels_details": [{"name": "inline_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}, {"name": "prisma_access_browser", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}, {"name": "email_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}, {"name": "endpoint_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}]}, "tag": {"users": {}, "tag_name": "tolerated", "total_apps": 0, "total_discovered_apps": 59311, "top_apps": [], "name": "tolerated", "type": "tag", "tag": {"total_apps": 0, "total_discovered_apps": 59311, "top_apps": []}}}, {"users": {}, "channel_name": "data_at_rest", "status": "enabled", "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}, "channel_details": {"asset_actions": [{"action": "sharing_revoked", "total_assets": 0}, {"action": "quarantined", "total_assets": 0}, {"action": "exposed", "total_assets": 0}], "sub_channels_details": [{"name": "saas_api", "incidents_by_severity": [{"severity": "very_high", "trend": 0}, {"severity": "high", "trend": 0}, {"severity": "medium", "trend": 0}, {"severity": "low", "trend": 0}], "assets_by_actions": [{"action": "quarantined", "trend": 0, "total_assets": 0}, {"action": "sharing_revoked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [{"exposure": "public", "trend": 0}, {"exposure": "external", "trend": 0}], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}]}, "tag": {"users": {}, "tag_name": "sanctioned", "total_apps": 0, "total_discovered_apps": 59311, "top_apps": [], "name": "sanctioned", "type": "tag", "tag": {"total_apps": 0, "total_discovered_apps": 59311, "top_apps": []}}}, {"users": {"total_sensitive_users": 24216}, "channel_name": "data_at_rest", "status": "enabled", "total_assets": 23306723, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "high", "total_assets": 1964, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 19876237, "assets_by_action": [{"action": "quarantined", "total_assets": 2050}, {"action": "sharing_revoked", "total_assets": 3}]}, {"severity": "low", "total_assets": 3587377, "assets_by_action": [{"action": "quarantined", "total_assets": 1267}, {"action": "sharing_revoked", "total_assets": 0}]}], "total_assets": 23306723, "total_users": 24216}, "channel_details": {"asset_actions": [{"action": "sharing_revoked", "total_assets": 3}, {"action": "quarantined", "total_assets": 3317}, {"action": "exposed", "total_assets": 9864798}], "sub_channels_details": [{"name": "saas_api", "incidents_by_severity": [{"severity": "very_high", "trend": 38, "total_incidents": 39260}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 311, "total_incidents": 93134}], "assets_by_actions": [{"action": "quarantined", "trend": -9, "total_assets": 3317}, {"action": "sharing_revoked", "trend": -57, "total_assets": 3}], "assets_by_exposure": [{"exposure": "public", "trend": 48, "total_assets": 3400}, {"exposure": "external", "trend": 1, "total_assets": 9861398}], "total_users": 24216, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "high", "total_assets": 1964, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 19876237, "assets_by_action": [{"action": "quarantined", "total_assets": 2050}, {"action": "sharing_revoked", "total_assets": 3}]}, {"severity": "low", "total_assets": 3587377, "assets_by_action": [{"action": "quarantined", "total_assets": 1267}, {"action": "sharing_revoked", "total_assets": 0}]}], "total_assets": 23306723, "total_users": 24216}}]}, "tag": {"users": {"total_sensitive_users": 24216}, "tag_name": "unsanctioned", "total_apps": 10, "total_discovered_apps": 59311, "top_apps": ["gmail-base", "google-drive-web-base", "slack-base", "amazon-simple-storag", "ms-office365-base", "zoom-base", "jira-base", "confluence-base", "github-base", "microsoft-e-online"], "name": "unsanctioned", "type": "tag", "tag": {"total_apps": 10, "total_discovered_apps": 59311, "top_apps": ["gmail-base", "google-drive-web-base", "slack-base", "amazon-simple-storag", "ms-office365-base", "zoom-base", "jira-base", "confluence-base", "github-base", "microsoft-e-online"]}}}, {"users": {}, "channel_name": "data_at_rest", "status": "enabled", "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}, "channel_details": {"asset_actions": [{"action": "sharing_revoked", "total_assets": 0}, {"action": "quarantined", "total_assets": 0}, {"action": "exposed", "total_assets": 0}], "sub_channels_details": [{"name": "saas_api", "incidents_by_severity": [{"severity": "very_high", "trend": 0}, {"severity": "high", "trend": 0}, {"severity": "medium", "trend": 0}, {"severity": "low", "trend": 0}], "assets_by_actions": [{"action": "quarantined", "trend": 0, "total_assets": 0}, {"action": "sharing_revoked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [{"exposure": "public", "trend": 0}, {"exposure": "external", "trend": 0}], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}]}, "tag": {"users": {}, "tag_name": "tolerated", "total_apps": 0, "total_discovered_apps": 59311, "top_apps": [], "name": "tolerated", "type": "tag", "tag": {"total_apps": 0, "total_discovered_apps": 59311, "top_apps": []}}}, {"users": {}, "channel_name": "data_in_motion", "status": "enabled", "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}, "channel_details": {"asset_actions": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}], "sub_channels_details": [{"name": "inline_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [], "total_assets": 0, "total_users": 0}}, {"name": "prisma_access_browser", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [], "total_assets": 0, "total_users": 0}}, {"name": "email_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [], "total_assets": 0, "total_users": 0}}, {"name": "endpoint_dlp", "incidents_by_severity": [{"severity": "very_high", "trend": 0, "total_incidents": 0}, {"severity": "high", "trend": 0, "total_incidents": 0}, {"severity": "medium", "trend": 0, "total_incidents": 0}, {"severity": "low", "trend": 0, "total_incidents": 0}], "assets_by_actions": [{"action": "allowed", "trend": 0, "total_assets": 0}, {"action": "blocked", "trend": 0, "total_assets": 0}], "assets_by_exposure": [], "total_users": 0, "assets_severity_details": {"severities": [{"severity": "very_high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "high", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "medium", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}, {"severity": "low", "total_assets": 0, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 0}]}], "total_assets": 0, "total_users": 0}}]}, "tag": {"name": "peripherals", "type": "peripherals"}}]}, "status": 200, "headers": {"content-length": "44282", "content-type": "application/json; charset=utf-8"}}