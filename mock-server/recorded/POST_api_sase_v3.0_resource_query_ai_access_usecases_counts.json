{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/ai_access/usecases_counts", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1738000344601, 1738011144601]}, {"property": "use_case", "operator": "in", "values": ["Meeting Assistant", "Developer Platform", "Enterprise Search", "Image Editor & Generator", "Video Editor & Generator", "Productivity Assistant"]}]}}, "response": {"header": {"createdAt": "2025-01-27T20:52:29Z", "dataCount": 1, "requestId": "c0714499-a978-4222-9e12-6802b8c176b8", "clientRequestId": "b04cd999-5210-4b2d-8141-ba84630a18db", "queryInput": {"time_range": "custom", "event_time": {"from": "2025-01-27T17:52:24Z", "to": "2025-01-27T20:52:24Z", "from_epoch": 1738000344000, "to_epoch": 1738011144000}}, "isResourceDataOverridden": false, "fieldList": [{"property": "unsanctioned_applications", "alias": "unsanctioned_applications", "dataType": "integer", "dataClass": "integer", "sequence": "1", "type": "integer"}, {"property": "tolerated_applications", "alias": "tolerated_applications", "dataType": "integer", "dataClass": "integer", "sequence": "2", "type": "integer"}, {"property": "sanctioned_applications", "alias": "sanctioned_applications", "dataType": "integer", "dataClass": "integer", "sequence": "3", "type": "integer"}, {"property": "total_applications", "alias": "total_applications", "dataType": "integer", "dataClass": "integer", "sequence": "4", "type": "integer"}, {"property": "unsanctioned_users", "alias": "unsanctioned_users", "dataType": "integer", "dataClass": "integer", "sequence": "5", "type": "integer"}, {"property": "tolerated_users", "alias": "tolerated_users", "dataType": "integer", "dataClass": "integer", "sequence": "6", "type": "integer"}, {"property": "sanctioned_users", "alias": "sanctioned_users", "dataType": "integer", "dataClass": "integer", "sequence": "7", "type": "integer"}, {"property": "total_users", "alias": "total_users", "dataType": "integer", "dataClass": "integer", "sequence": "8", "type": "integer"}], "status": {"subCode": 200}, "name": "ai_access/usecases_counts", "cache_time": 1738011144899, "cache_age_in_seconds": 4, "cache_operator": "inline", "cache_operation": "WRITE"}, "data": [{"unsanctioned_applications": 1, "tolerated_applications": 1, "sanctioned_applications": 1, "total_applications": 3, "unsanctioned_users": 1, "tolerated_users": 1, "sanctioned_users": 1, "total_users": 3}]}, "status": 200, "headers": {"cache-time": "1738011144899", "content-length": "546", "content-type": "application/json"}}