{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/netsec/operational_health_domain_count", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418845202, 1738010845202]}]}}, "response": {"header": {"createdAt": "2025-01-27T20:47:25Z", "dataCount": 1, "requestId": "4f316506-f180-49a6-98bf-60ad6034dbcc", "clientRequestId": "8a06d55a-5994-46ab-9cb3-d6978eadb8d9", "isResourceDataOverridden": false, "status": {"subCode": 200}, "name": "netsec/operational_health_domain_count", "cache_operation": "IGNORED"}, "data": [{"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "internet", "platform_type": "all", "top_domains": ["${jndi", "'", "*.adobe.com", "*.ads.shopee.io", "*.agora.io:8443", "*.alicdn.com", "*.aliexpress.com", "*.alipay.com", "*.anyconnect.host", "*.api.fantasysports.yahoo.com", "*.aras-innovator.net", "*.azureedge.net", "*.between.us", "*.botim.me", "*.brightcloud.com", "*.cc.tmobile-digital.com", "*.ccs.miami.edu", "*.cdn.myqcloud.com", "*.codoon.com", "*.contentupdates.paloaltonetworks.com"], "domain_count": 67902, "source_type": "other", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "internet", "platform_type": "ngfw", "top_domains": ["${jndi", "'", "*.adobe.com", "*.ads.shopee.io", "*.agora.io:8443", "*.alicdn.com", "*.aliexpress.com", "*.alipay.com", "*.anyconnect.host", "*.api.fantasysports.yahoo.com", "*.aras-innovator.net", "*.azureedge.net", "*.between.us", "*.botim.me", "*.brightcloud.com", "*.cc.tmobile-digital.com", "*.ccs.miami.edu", "*.cdn.myqcloud.com", "*.codoon.com", "*.contentupdates.paloaltonetworks.com"], "domain_count": 67685, "source_type": "other", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "private", "platform_type": "all", "top_domains": ["${jndi", "${jndi:3387", "${jndi:443", "${jndi:5985", "${jndi:8005", "${jndi:8080", "${jndi:9100", "'", "'ansys%20licensing%20server%20certificate':2325", "*.artifactory.paloaltonetworks.com", "*.contentupdates.paloaltonetworks.com", "*.engqa.contentupdates.paloaltonetworks.com", "*.paloaltonetworks.com", "*.paloaltonetworks.com:80", "007900000512885", "007900000514152", "007900000514153", "007900000514154", "007958000480261", "007958000480262"], "domain_count": 5508, "source_type": "iot", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "private", "platform_type": "ngfw", "top_domains": ["${jndi", "${jndi:3387", "${jndi:443", "${jndi:5985", "${jndi:8005", "${jndi:8080", "${jndi:9100", "'", "'ansys%20licensing%20server%20certificate':2325", "*.artifactory.paloaltonetworks.com", "*.contentupdates.paloaltonetworks.com", "*.engqa.contentupdates.paloaltonetworks.com", "*.paloaltonetworks.com", "*.paloaltonetworks.com:80", "007900000512885", "007900000514152", "007900000514153", "007900000514154", "007958000480261", "007958000480262"], "domain_count": 5508, "source_type": "iot", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "private", "platform_type": "ngfw", "top_domains": ["${jndi", "${jndi:10001", "${jndi:10002", "${jndi:1128", "${jndi:1777", "${jndi:1947", "${jndi:2020", "${jndi:20201", "${jndi:20202", "${jndi:2021", "${jndi:2301", "${jndi:2323", "${jndi:30001", "${jndi:32768", "${jndi:3387", "${jndi:40080", "${jndi:443", "${jndi:47001", "${jndi:5000", "${jndi:50000"], "domain_count": 40054, "source_type": "other", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "private", "platform_type": "all", "top_domains": ["${jndi", "${jndi:10001", "${jndi:10002", "${jndi:1128", "${jndi:1777", "${jndi:1947", "${jndi:2020", "${jndi:20201", "${jndi:20202", "${jndi:2021", "${jndi:2301", "${jndi:2323", "${jndi:30001", "${jndi:32768", "${jndi:3387", "${jndi:40080", "${jndi:443", "${jndi:47001", "${jndi:5000", "${jndi:50000"], "domain_count": 40059, "source_type": "other", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "internet", "platform_type": "prisma_access", "top_domains": ["*.gdms.cloud", "*.google.com", "*.pagerduty.com", "*.ser.proofpoint.com:587", "*.zoom.us", "086ed8050ff3a6e3065c3665808098b7.gr7.eu-west-3.eks.amazonaws.com", "11283378.fls.doubleclick.net", "************", "11988414.fls.doubleclick.net", "12123386.fls.doubleclick.net", "127541-41.chat.api.drift.com", "**************", "************", "**************", "**************", "14611606.fls.doubleclick.net", "**************", "17de4c1b.akstat.io", "193597-ipv4.gr.global.aa-rt.sharepoint.com", "194073-ipv4.gr.global.aa-rt.sharepoint.com"], "domain_count": 1817, "source_type": "other", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "internet", "platform_type": "prisma_access", "top_domains": ["facebook.com", "gateway.talon-sec.com", "paloaltonetworks.enterprise.slack.com", "mail.google.com", "paloaltonetworks.okta.com", "www.google.com", "slack.com", "app.smartsheet.com", "slackb.com", "classifier-auf.talon-sec.com", "accounts.google.com", "bfe078e7921507bb.talon-sec.com", "login.talon-sec.com", "play.google.com", "ext-proxy.talon-sec.com", "lh3.googleusercontent.com", "auth.talon-sec.com", "waa-pa.clients6.google.com", "panwit.pagerduty.com", "edgeapi.slack.com"], "domain_count": 93915, "source_type": "user", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "internet", "platform_type": "prisma_access", "top_domains": ["facebook.com", "gateway.talon-sec.com", "paloaltonetworks.enterprise.slack.com", "mail.google.com", "paloaltonetworks.okta.com", "www.google.com", "slack.com", "app.smartsheet.com", "slackb.com", "classifier-auf.talon-sec.com", "accounts.google.com", "bfe078e7921507bb.talon-sec.com", "login.talon-sec.com", "play.google.com", "ext-proxy.talon-sec.com", "lh3.googleusercontent.com", "auth.talon-sec.com", "waa-pa.clients6.google.com", "panwit.pagerduty.com", "edgeapi.slack.com"], "domain_count": 94127, "source_type": "all", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "internet", "platform_type": "all", "top_domains": ["facebook.com", "paloaltonetworks.enterprise.slack.com", "gateway.talon-sec.com", "mail.google.com", "www.google.com", "slack.com", "classifier-auf.talon-sec.com", "accounts.google.com", "bfe078e7921507bb.talon-sec.com", "play.google.com", "lh3.googleusercontent.com", "slackb.com", "paloaltonetworks.okta.com", "waa-pa.clients6.google.com", "login.talon-sec.com", "ext-proxy.talon-sec.com", "auth.talon-sec.com", "edgeapi.slack.com", "app.smartsheet.com", "www.gstatic.com"], "domain_count": 192983, "source_type": "all", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "internet", "platform_type": "all", "top_domains": ["facebook.com", "paloaltonetworks.enterprise.slack.com", "gateway.talon-sec.com", "mail.google.com", "www.google.com", "slack.com", "classifier-auf.talon-sec.com", "accounts.google.com", "bfe078e7921507bb.talon-sec.com", "play.google.com", "lh3.googleusercontent.com", "slackb.com", "paloaltonetworks.okta.com", "waa-pa.clients6.google.com", "login.talon-sec.com", "ext-proxy.talon-sec.com", "auth.talon-sec.com", "edgeapi.slack.com", "app.smartsheet.com", "www.gstatic.com"], "domain_count": 143743, "source_type": "user", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "private", "platform_type": "prisma_access", "top_domains": ["***************", "************", "10.101.10.246:3978", "10.101.10.247:3978", "10.101.10.248:3978", "10.101.10.249:3978", "10.101.10.250:3978", "10.101.10.251:3978", "10.101.10.34:5007", "10.101.2.93", "10.101.4.142:52311", "10.130.4.11:636", "10.208.34.54:3000", "10.55.66.10:636", "34.19.18.195", "34.91.114.209", "8.0.0.0:3978", "api.chat.pan.dev", "art.code.pan.run", "cats-server.cyvera.local"], "domain_count": 38, "source_type": "other", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "private", "platform_type": "prisma_access", "top_domains": ["api.chat.pan.dev", "code-ai.pan.run", "code.pan.run", "chat.pan.dev", "forgerock.appsvc.pan.local", "gitlab.xdr.pan.local", "pa-gcs.appsvc.pan.local", "sourcegraph.xdr.pan.local", "sre-tools.pan.local", "prismaaccess.space.core.pan.run", "grafana.garuda-saas.core.pan.run", "grafana.garuda-pa-cosmos-stg.core.pan.run", "art.code.pan.run", "sre.tools.panclouddev.com", "vault.code.pan.run", "grafana.garuda-saas-2.core.pan.run", "cis", "grafana.pa-cosmos.core.pan.run", "jenkins.xdr.pan.local:8080", "grafana.xdr.pan.local:3443"], "domain_count": 3893, "source_type": "all", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "private", "platform_type": "prisma_access", "top_domains": ["api.chat.pan.dev", "code-ai.pan.run", "code.pan.run", "chat.pan.dev", "forgerock.appsvc.pan.local", "gitlab.xdr.pan.local", "pa-gcs.appsvc.pan.local", "sourcegraph.xdr.pan.local", "sre-tools.pan.local", "prismaaccess.space.core.pan.run", "grafana.garuda-saas.core.pan.run", "grafana.garuda-pa-cosmos-stg.core.pan.run", "art.code.pan.run", "sre.tools.panclouddev.com", "vault.code.pan.run", "grafana.garuda-saas-2.core.pan.run", "cis", "grafana.pa-cosmos.core.pan.run", "jenkins.xdr.pan.local:8080", "grafana.xdr.pan.local:3443"], "domain_count": 3876, "source_type": "user", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "internet", "platform_type": "all", "top_domains": ["*.logicmonitor.com", "*.paloaltonetworks.com", "*.ser.proofpoint.com:587", "*.whatsapp.net", "0ec6cee2c9c85d8180403d8eccb0c349a05401a88845f7191342f1fc5928b50.us-east-1.prod.service.minerva.devices.a2z.com", "*******", "*******", "*******", "***************", "1abb2a55-5d5c-4c23-987d-3560a70be547.api2-lc-prod-us.gpcloudservice.com:444", "1d.tlu.dl.delivery.mp.microsoft.com", "28bbf0a3decf6d1165032bfd835d6e1844c5d44d.ipv4.cws.conviva.com", "2tosii3aca32sz4i24aa-p7uugk-d83e063ad-clientnsv4-s.akamaihd.net", "***********", "************:5223", "35-161-154-239-pushcl.np.communication.playstation.net", "44-234-157-33-pushcl.np.communication.playstation.net", "4f3edf7b-49b0-4df7-9131-6582b7314577.fei-lc-prod-us.gpcloudservice.com", "5aa25954e40ffb18984989b59487dfe054549e213a2e64a12187f8deb5a4cb5.us-east-1.prod.service.minerva.devices.a2z.com", "7-28-0-app.agent.datadoghq.com"], "domain_count": 1261, "source_type": "iot", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "internet", "platform_type": "ngfw", "top_domains": ["*.logicmonitor.com", "*.paloaltonetworks.com", "*.ser.proofpoint.com:587", "*.whatsapp.net", "0ec6cee2c9c85d8180403d8eccb0c349a05401a88845f7191342f1fc5928b50.us-east-1.prod.service.minerva.devices.a2z.com", "*******", "*******", "*******", "***************", "1abb2a55-5d5c-4c23-987d-3560a70be547.api2-lc-prod-us.gpcloudservice.com:444", "1d.tlu.dl.delivery.mp.microsoft.com", "28bbf0a3decf6d1165032bfd835d6e1844c5d44d.ipv4.cws.conviva.com", "2tosii3aca32sz4i24aa-p7uugk-d83e063ad-clientnsv4-s.akamaihd.net", "***********", "************:5223", "35-161-154-239-pushcl.np.communication.playstation.net", "44-234-157-33-pushcl.np.communication.playstation.net", "4f3edf7b-49b0-4df7-9131-6582b7314577.fei-lc-prod-us.gpcloudservice.com", "5aa25954e40ffb18984989b59487dfe054549e213a2e64a12187f8deb5a4cb5.us-east-1.prod.service.minerva.devices.a2z.com", "7-28-0-app.agent.datadoghq.com"], "domain_count": 1261, "source_type": "iot", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "private", "platform_type": "all", "top_domains": ["ocsp.paloaltonetworks.com:8080", "crl.paloaltonetworks.com", "www.paloaltonetworks.com", "jira-dc.paloaltonetworks.com", "confluence-dc.paloaltonetworks.com", "sjccmgtvw01p.paloaltonetworks.local", "sv3-mfa.paloaltonetworks.com:6082", "int-gw.paloaltonetworks.com", "***********", "sjccmgtvw01p.paloaltonetworks.local:10123", "api.chat.pan.dev", "conversionupdates.paloaltonetworks.com", "chat.pan.dev", "code.pan.run", "code-ai.pan.run", "forgerock.appsvc.pan.local", "gitlab.xdr.pan.local", "pa-gcs.appsvc.pan.local", "quest.paloaltonetworks.local", "bitbucket.paloaltonetworks.local"], "domain_count": 8662, "source_type": "user", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "private", "platform_type": "all", "top_domains": ["ocsp.paloaltonetworks.com:8080", "crl.paloaltonetworks.com", "www.paloaltonetworks.com", "jira-dc.paloaltonetworks.com", "confluence-dc.paloaltonetworks.com", "sjccmgtvw01p.paloaltonetworks.local", "sv3-mfa.paloaltonetworks.com:6082", "int-gw.paloaltonetworks.com", "***********", "sjccmgtvw01p.paloaltonetworks.local:10123", "api.chat.pan.dev", "conversionupdates.paloaltonetworks.com", "chat.pan.dev", "code.pan.run", "code-ai.pan.run", "forgerock.appsvc.pan.local", "gitlab.xdr.pan.local", "pa-gcs.appsvc.pan.local", "quest.paloaltonetworks.local", "bitbucket.paloaltonetworks.local"], "domain_count": 47831, "source_type": "all", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "private", "platform_type": "ngfw", "top_domains": ["ocsp.paloaltonetworks.com:8080", "crl.paloaltonetworks.com", "www.paloaltonetworks.com", "jira-dc.paloaltonetworks.com", "confluence-dc.paloaltonetworks.com", "sjccmgtvw01p.paloaltonetworks.local", "sv3-mfa.paloaltonetworks.com:6082", "int-gw.paloaltonetworks.com", "***********", "sjccmgtvw01p.paloaltonetworks.local:10123", "conversionupdates.paloaltonetworks.com", "api.chat.pan.dev", "code.pan.run", "code-ai.pan.run", "chat.pan.dev", "gitlab.xdr.pan.local", "forgerock.appsvc.pan.local", "quest.paloaltonetworks.local", "bitbucket.paloaltonetworks.local", "gcs-velocity.paloaltonetworks.local"], "domain_count": 8076, "source_type": "user", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "private", "platform_type": "ngfw", "top_domains": ["ocsp.paloaltonetworks.com:8080", "crl.paloaltonetworks.com", "www.paloaltonetworks.com", "jira-dc.paloaltonetworks.com", "confluence-dc.paloaltonetworks.com", "sjccmgtvw01p.paloaltonetworks.local", "sv3-mfa.paloaltonetworks.com:6082", "int-gw.paloaltonetworks.com", "***********", "sjccmgtvw01p.paloaltonetworks.local:10123", "conversionupdates.paloaltonetworks.com", "api.chat.pan.dev", "code.pan.run", "code-ai.pan.run", "chat.pan.dev", "gitlab.xdr.pan.local", "forgerock.appsvc.pan.local", "quest.paloaltonetworks.local", "bitbucket.paloaltonetworks.local", "gcs-velocity.paloaltonetworks.local"], "domain_count": 47426, "source_type": "all", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "internet", "platform_type": "ngfw", "top_domains": ["paloaltonetworks.enterprise.slack.com", "gateway.talon-sec.com", "edgeapi.slack.com", "ch-panwhq.traps.paloaltonetworks.com", "dc-panwhq.traps.paloaltonetworks.com", "lh3.googleusercontent.com", "slack.com", "www.google.com", "slackb.com", "www.gstatic.com", "mail.google.com", "play.google.com", "waa-pa.clients6.google.com", "classifier-auf.talon-sec.com", "accounts.google.com", "api.segment.io", "bfe078e7921507bb.talon-sec.com", "app.slack.com", "ssl.gstatic.com", "content-autofill.googleapis.com"], "domain_count": 80192, "source_type": "user", "sub_tenant_id": "*********"}, {"tenant_id": "*********", "aggregation_type": "last_30_days", "domain_type": "internet", "platform_type": "ngfw", "top_domains": ["paloaltonetworks.enterprise.slack.com", "gateway.talon-sec.com", "edgeapi.slack.com", "ch-panwhq.traps.paloaltonetworks.com", "dc-panwhq.traps.paloaltonetworks.com", "lh3.googleusercontent.com", "slack.com", "www.google.com", "slackb.com", "www.gstatic.com", "mail.google.com", "play.google.com", "waa-pa.clients6.google.com", "classifier-auf.talon-sec.com", "accounts.google.com", "api.segment.io", "bfe078e7921507bb.talon-sec.com", "app.slack.com", "ssl.gstatic.com", "content-autofill.googleapis.com"], "domain_count": 131687, "source_type": "all", "sub_tenant_id": "*********"}]}, "status": 200, "headers": {"content-length": "2444", "content-type": "application/json"}}