{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/netsec/v2/traffic_application_count", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418846370, 1738010846370]}]}}, "response": {"header": {"createdAt": "2025-01-27T20:47:26Z", "dataCount": 1, "requestId": "af93cf6d-6f1f-4d59-bfa8-b06293195295", "clientRequestId": "90da5245-1911-4a16-93eb-954be9552ec5", "isResourceDataOverridden": false, "status": {"subCode": 200}, "name": "netsec/v2/traffic_application_count", "cache_operation": "IGNORED"}, "data": [{"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "aggregation_type": "last_30_days", "application_count": 696, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 270261948111}, {"application_name": "ssh", "bytes_total_volume": 57488469318}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 47316302408}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 40381420942}, {"application_name": "web-browsing", "bytes_total_volume": 34515213738}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32456652692}, {"application_name": "mysql", "bytes_total_volume": 24292991959}, {"application_name": "unknown-udp", "bytes_total_volume": 13694069241}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 10785598413}, {"application_name": "traps-management-service", "bytes_total_volume": 7726974738}, {"application_name": "zoom-meeting", "bytes_total_volume": 6362040323}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569695723}, {"application_name": "paloalto-updates", "bytes_total_volume": 5519222355}, {"application_name": "cortex-xdr", "bytes_total_volume": 4343699089}, {"application_name": "datadog", "bytes_total_volume": 4300906307}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "ms-update", "bytes_total_volume": 4186388312}, {"application_name": "rtp-base", "bytes_total_volume": 3928765892}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}, {"application_name": "ocsp", "bytes_total_volume": 3752035745}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "aggregation_type": "last_30_days", "application_count": 707, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 270261948111}, {"application_name": "google-base", "bytes_total_volume": 105976101201}, {"application_name": "ssh", "bytes_total_volume": 57488469318}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 47316302408}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 40381420942}, {"application_name": "web-browsing", "bytes_total_volume": 34515213738}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32456652692}, {"application_name": "mysql", "bytes_total_volume": 24292991959}, {"application_name": "unknown-udp", "bytes_total_volume": 13694069241}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 10785598413}, {"application_name": "traps-management-service", "bytes_total_volume": 7726974738}, {"application_name": "zoom-meeting", "bytes_total_volume": 6362040323}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569695723}, {"application_name": "paloalto-updates", "bytes_total_volume": 5519222355}, {"application_name": "cortex-xdr", "bytes_total_volume": 4343699089}, {"application_name": "datadog", "bytes_total_volume": 4300906307}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "ms-update", "bytes_total_volume": 4186388312}, {"application_name": "rtp-base", "bytes_total_volume": 3928765892}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "private_apps", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 185, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 184750566526}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 47316302408}, {"application_name": "ssh", "bytes_total_volume": 46166560317}, {"application_name": "web-browsing", "bytes_total_volume": 27680616475}, {"application_name": "mysql", "bytes_total_volume": 24292991959}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569695723}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "rtp-base", "bytes_total_volume": 3625781916}, {"application_name": "vmware", "bytes_total_volume": 3163321573}, {"application_name": "ocsp", "bytes_total_volume": 3047817057}, {"application_name": "paloalto-userid-agent", "bytes_total_volume": 2568082443}, {"application_name": "perforce", "bytes_total_volume": 2100408457}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 1581887239}, {"application_name": "ldap", "bytes_total_volume": 1186034407}, {"application_name": "snmpv3", "bytes_total_volume": 863422418}, {"application_name": "lpd", "bytes_total_volume": 609481776}, {"application_name": "panorama", "bytes_total_volume": 582927551}, {"application_name": "tftp", "bytes_total_volume": 505805507}, {"application_name": "ms-rdp", "bytes_total_volume": 500876616}, {"application_name": "ms-update-optimization-p2p", "bytes_total_volume": 492900223}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "private_apps", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 187, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 184750566526}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 47316302408}, {"application_name": "ssh", "bytes_total_volume": 46166560317}, {"application_name": "web-browsing", "bytes_total_volume": 27680616475}, {"application_name": "mysql", "bytes_total_volume": 24292991959}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569695723}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "rtp-base", "bytes_total_volume": 3625781916}, {"application_name": "vmware", "bytes_total_volume": 3163321573}, {"application_name": "ocsp", "bytes_total_volume": 3047817057}, {"application_name": "paloalto-userid-agent", "bytes_total_volume": 2568082443}, {"application_name": "perforce", "bytes_total_volume": 2100408457}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 1581887239}, {"application_name": "ldap", "bytes_total_volume": 1186034407}, {"application_name": "snmpv3", "bytes_total_volume": 863422418}, {"application_name": "lpd", "bytes_total_volume": 609481776}, {"application_name": "panorama", "bytes_total_volume": 582927551}, {"application_name": "tftp", "bytes_total_volume": 505805507}, {"application_name": "ms-rdp", "bytes_total_volume": 500876616}, {"application_name": "ms-update-optimization-p2p", "bytes_total_volume": 492900223}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "private_apps", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 187, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 195217697460}, {"application_name": "insufficient-data", "bytes_total_volume": 66622580400}, {"application_name": "ssh", "bytes_total_volume": 47478326725}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 47376707533}, {"application_name": "web-browsing", "bytes_total_volume": 27824093053}, {"application_name": "mysql", "bytes_total_volume": 24293548362}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569695723}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "rtp-base", "bytes_total_volume": 3625803308}, {"application_name": "vmware", "bytes_total_volume": 3200569562}, {"application_name": "ocsp", "bytes_total_volume": 3047899672}, {"application_name": "paloalto-userid-agent", "bytes_total_volume": 2568101266}, {"application_name": "express-mode", "bytes_total_volume": 2417561957}, {"application_name": "perforce", "bytes_total_volume": 2100408457}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 1581887239}, {"application_name": "ldap", "bytes_total_volume": 1269243211}, {"application_name": "ms-rdp", "bytes_total_volume": 1267187116}, {"application_name": "snmpv3", "bytes_total_volume": 863422418}, {"application_name": "lpd", "bytes_total_volume": 609481998}, {"application_name": "panorama", "bytes_total_volume": 582927551}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "private_apps", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 189, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 195217697460}, {"application_name": "insufficient-data", "bytes_total_volume": 66622580400}, {"application_name": "ssh", "bytes_total_volume": 47478326725}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 47376707533}, {"application_name": "web-browsing", "bytes_total_volume": 27824093053}, {"application_name": "mysql", "bytes_total_volume": 24293548362}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569695723}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "rtp-base", "bytes_total_volume": 3625803308}, {"application_name": "vmware", "bytes_total_volume": 3200569562}, {"application_name": "ocsp", "bytes_total_volume": 3047899672}, {"application_name": "paloalto-userid-agent", "bytes_total_volume": 2568101266}, {"application_name": "express-mode", "bytes_total_volume": 2417561957}, {"application_name": "perforce", "bytes_total_volume": 2100408457}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 1581887239}, {"application_name": "ldap", "bytes_total_volume": 1269243211}, {"application_name": "ms-rdp", "bytes_total_volume": 1267187116}, {"application_name": "snmpv3", "bytes_total_volume": 863422418}, {"application_name": "lpd", "bytes_total_volume": 609481998}, {"application_name": "panorama", "bytes_total_volume": 582927551}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "aggregation_type": "last_30_days", "application_count": 767, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 290514013298}, {"application_name": "insufficient-data", "bytes_total_volume": 66631629220}, {"application_name": "ssh", "bytes_total_volume": 58805730628}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 48983335392}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 47376707533}, {"application_name": "web-browsing", "bytes_total_volume": 45910437614}, {"application_name": "express-mode", "bytes_total_volume": 39325276858}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32514804268}, {"application_name": "mysql", "bytes_total_volume": 24293548362}, {"application_name": "unknown-udp", "bytes_total_volume": 13874606657}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 10789218884}, {"application_name": "traps-management-service", "bytes_total_volume": 10289934000}, {"application_name": "http-video", "bytes_total_volume": 7768325936}, {"application_name": "zoom-meeting", "bytes_total_volume": 6418355883}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569695723}, {"application_name": "paloalto-updates", "bytes_total_volume": 5533514530}, {"application_name": "ms-update", "bytes_total_volume": 5113475200}, {"application_name": "cortex-xdr", "bytes_total_volume": 4500920686}, {"application_name": "datadog", "bytes_total_volume": 4314635626}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "internet", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 379, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 95296315838}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 48550896562}, {"application_name": "express-mode", "bytes_total_volume": 36907714901}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32514686830}, {"application_name": "web-browsing", "bytes_total_volume": 18086344561}, {"application_name": "unknown-udp", "bytes_total_volume": 13799484861}, {"application_name": "ssh", "bytes_total_volume": 11327403903}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 10789218884}, {"application_name": "traps-management-service", "bytes_total_volume": 10289724804}, {"application_name": "http-video", "bytes_total_volume": 7766987107}, {"application_name": "paloalto-updates", "bytes_total_volume": 5493293251}, {"application_name": "ms-update", "bytes_total_volume": 5109392045}, {"application_name": "cortex-xdr", "bytes_total_volume": 4500920686}, {"application_name": "rtcp", "bytes_total_volume": 3550249068}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2742483731}, {"application_name": "apt-get", "bytes_total_volume": 2129197324}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 1949482329}, {"application_name": "paloalto-cloud-identity", "bytes_total_volume": 1453839246}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 1252697221}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208534181}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "internet", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 342, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 85511381585}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 40003680708}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32456535254}, {"application_name": "unknown-udp", "bytes_total_volume": 13629964355}, {"application_name": "ssh", "bytes_total_volume": 11321909001}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 10785598413}, {"application_name": "traps-management-service", "bytes_total_volume": 7726765542}, {"application_name": "web-browsing", "bytes_total_volume": 6834597263}, {"application_name": "paloalto-updates", "bytes_total_volume": 5479001076}, {"application_name": "cortex-xdr", "bytes_total_volume": 4343699089}, {"application_name": "ms-update", "bytes_total_volume": 4182562363}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2740364129}, {"application_name": "apt-get", "bytes_total_volume": 2129171029}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 1933018958}, {"application_name": "paloalto-cloud-identity", "bytes_total_volume": 1453775426}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 1252261301}, {"application_name": "stun", "bytes_total_volume": 942794650}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 897049621}, {"application_name": "ocsp", "bytes_total_volume": 704218688}, {"application_name": "paloalto-wildfire-cloud", "bytes_total_volume": 676989651}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "yes", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 137627397}, {"application_name": "notion-base", "bytes_total_volume": 43965669}, {"application_name": "tabnine", "bytes_total_volume": 6428392}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5572244}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "bing-ai-base", "bytes_total_volume": 85141}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 137627397}, {"application_name": "notion-base", "bytes_total_volume": 43965669}, {"application_name": "tabnine", "bytes_total_volume": 6428392}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5572244}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "bing-ai-base", "bytes_total_volume": 85141}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "yes", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 137627397}, {"application_name": "notion-base", "bytes_total_volume": 43965669}, {"application_name": "tabnine", "bytes_total_volume": 6428392}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5572244}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "bing-ai-base", "bytes_total_volume": 85141}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "yes", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 115090018}, {"application_name": "notion-base", "bytes_total_volume": 17109961}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5551400}, {"application_name": "tabnine", "bytes_total_volume": 495719}, {"application_name": "bing-ai-base", "bytes_total_volume": 53665}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "yes", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 115090018}, {"application_name": "notion-base", "bytes_total_volume": 17109961}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5551400}, {"application_name": "tabnine", "bytes_total_volume": 495719}, {"application_name": "bing-ai-base", "bytes_total_volume": 53665}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "all", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 115090018}, {"application_name": "notion-base", "bytes_total_volume": 17109961}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5551400}, {"application_name": "tabnine", "bytes_total_volume": 495719}, {"application_name": "bing-ai-base", "bytes_total_volume": 53665}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "all", "platform_type": "all", "is_genai": "yes", "aggregation_type": "last_30_days", "application_count": 47, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 137627397}, {"application_name": "openai-chatgpt", "bytes_total_volume": 52152309}, {"application_name": "notion-base", "bytes_total_volume": 43965669}, {"application_name": "openai-base", "bytes_total_volume": 7305083}, {"application_name": "tabnine", "bytes_total_volume": 6428392}, {"application_name": "deepl-base", "bytes_total_volume": 5654897}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5572244}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 4564737}, {"application_name": "synthesia-base", "bytes_total_volume": 4403183}, {"application_name": "github-copilot-business", "bytes_total_volume": 4343713}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "google-gemini", "bytes_total_volume": 2114509}, {"application_name": "adobe-express-base", "bytes_total_volume": 1115260}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "huggingface-base", "bytes_total_volume": 1007263}, {"application_name": "claude-base", "bytes_total_volume": 966654}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 882168}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "all", "platform_type": "all", "is_genai": "yes", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 47, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 137627397}, {"application_name": "openai-chatgpt", "bytes_total_volume": 52152309}, {"application_name": "notion-base", "bytes_total_volume": 43965669}, {"application_name": "openai-base", "bytes_total_volume": 7305083}, {"application_name": "tabnine", "bytes_total_volume": 6428392}, {"application_name": "deepl-base", "bytes_total_volume": 5654897}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5572244}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 4564737}, {"application_name": "synthesia-base", "bytes_total_volume": 4403183}, {"application_name": "github-copilot-business", "bytes_total_volume": 4343713}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "google-gemini", "bytes_total_volume": 2114509}, {"application_name": "adobe-express-base", "bytes_total_volume": 1115260}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "huggingface-base", "bytes_total_volume": 1007263}, {"application_name": "claude-base", "bytes_total_volume": 966654}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 882168}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "yes", "aggregation_type": "last_30_days", "application_count": 32, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 115090018}, {"application_name": "openai-chatgpt", "bytes_total_volume": 39288940}, {"application_name": "notion-base", "bytes_total_volume": 17109961}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5551400}, {"application_name": "deepl-base", "bytes_total_volume": 5412573}, {"application_name": "openai-base", "bytes_total_volume": 5037921}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 3897763}, {"application_name": "github-copilot-business", "bytes_total_volume": 3806173}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "adobe-express-base", "bytes_total_volume": 965482}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 604532}, {"application_name": "tabnine", "bytes_total_volume": 495719}, {"application_name": "google-gemini", "bytes_total_volume": 425584}, {"application_name": "huggingface-base", "bytes_total_volume": 405918}, {"application_name": "deepl-write", "bytes_total_volume": 344615}, {"application_name": "claude-base", "bytes_total_volume": 343270}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 297367}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "yes", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 32, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 115090018}, {"application_name": "openai-chatgpt", "bytes_total_volume": 39288940}, {"application_name": "notion-base", "bytes_total_volume": 17109961}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5551400}, {"application_name": "deepl-base", "bytes_total_volume": 5412573}, {"application_name": "openai-base", "bytes_total_volume": 5037921}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 3897763}, {"application_name": "github-copilot-business", "bytes_total_volume": 3806173}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "adobe-express-base", "bytes_total_volume": 965482}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 604532}, {"application_name": "tabnine", "bytes_total_volume": 495719}, {"application_name": "google-gemini", "bytes_total_volume": 425584}, {"application_name": "huggingface-base", "bytes_total_volume": 405918}, {"application_name": "deepl-write", "bytes_total_volume": 344615}, {"application_name": "claude-base", "bytes_total_volume": 343270}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 297367}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "yes", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 23, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "deepl-base", "bytes_total_volume": 5412573}, {"application_name": "openai-base", "bytes_total_volume": 5037921}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 3897763}, {"application_name": "github-copilot-business", "bytes_total_volume": 3806173}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "adobe-express-base", "bytes_total_volume": 965482}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 604532}, {"application_name": "google-gemini", "bytes_total_volume": 425584}, {"application_name": "huggingface-base", "bytes_total_volume": 405918}, {"application_name": "deepl-write", "bytes_total_volume": 344615}, {"application_name": "claude-base", "bytes_total_volume": 343270}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 219877}, {"application_name": "ms-copilot-studio", "bytes_total_volume": 164406}, {"application_name": "openai-chatgpt-download", "bytes_total_volume": 137514}, {"application_name": "claude-upload", "bytes_total_volume": 79328}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 58670}, {"application_name": "github-copilot", "bytes_total_volume": 58162}, {"application_name": "github-copilot-chat", "bytes_total_volume": 49211}, {"application_name": "synthesia-base", "bytes_total_volume": 30207}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "yes", "aggregation_type": "last_30_days", "application_count": 23, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "deepl-base", "bytes_total_volume": 5412573}, {"application_name": "openai-base", "bytes_total_volume": 5037921}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 3897763}, {"application_name": "github-copilot-business", "bytes_total_volume": 3806173}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "adobe-express-base", "bytes_total_volume": 965482}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 604532}, {"application_name": "google-gemini", "bytes_total_volume": 425584}, {"application_name": "huggingface-base", "bytes_total_volume": 405918}, {"application_name": "deepl-write", "bytes_total_volume": 344615}, {"application_name": "claude-base", "bytes_total_volume": 343270}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 219877}, {"application_name": "ms-copilot-studio", "bytes_total_volume": 164406}, {"application_name": "openai-chatgpt-download", "bytes_total_volume": 137514}, {"application_name": "claude-upload", "bytes_total_volume": 79328}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 58670}, {"application_name": "github-copilot", "bytes_total_volume": 58162}, {"application_name": "github-copilot-chat", "bytes_total_volume": 49211}, {"application_name": "synthesia-base", "bytes_total_volume": 30207}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "all", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 178, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tenable.io", "bytes_total_volume": 2479940998}, {"application_name": "slack-base", "bytes_total_volume": 2024732157}, {"application_name": "google-docs-base", "bytes_total_volume": 1316391759}, {"application_name": "gmail-base", "bytes_total_volume": 1144833291}, {"application_name": "google-drive-web-base", "bytes_total_volume": 490617795}, {"application_name": "vidyo", "bytes_total_volume": 400736999}, {"application_name": "okta", "bytes_total_volume": 274781798}, {"application_name": "youtube-base", "bytes_total_volume": 261844048}, {"application_name": "outlook-web-online", "bytes_total_volume": 210006848}, {"application_name": "ms-teams-audio-video", "bytes_total_volume": 200844148}, {"application_name": "icloud-base", "bytes_total_volume": 190426144}, {"application_name": "slack-uploading", "bytes_total_volume": 170171490}, {"application_name": "facebook-base", "bytes_total_volume": 167861948}, {"application_name": "github-base", "bytes_total_volume": 166882736}, {"application_name": "figma-base", "bytes_total_volume": 159890819}, {"application_name": "ms-teams", "bytes_total_volume": 149263809}, {"application_name": "slack-downloading", "bytes_total_volume": 140657125}, {"application_name": "google-chat-base", "bytes_total_volume": 136811887}, {"application_name": "facebook-video", "bytes_total_volume": 130906430}, {"application_name": "ms-onedrive-downloading", "bytes_total_volume": 127241182}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "all", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 187, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tenable.io", "bytes_total_volume": 2479940998}, {"application_name": "slack-base", "bytes_total_volume": 2024732157}, {"application_name": "google-docs-base", "bytes_total_volume": 1316391759}, {"application_name": "gmail-base", "bytes_total_volume": 1144833291}, {"application_name": "google-drive-web-base", "bytes_total_volume": 490617795}, {"application_name": "vidyo", "bytes_total_volume": 400736999}, {"application_name": "okta", "bytes_total_volume": 274781798}, {"application_name": "youtube-base", "bytes_total_volume": 261844048}, {"application_name": "outlook-web-online", "bytes_total_volume": 210006848}, {"application_name": "ms-teams-audio-video", "bytes_total_volume": 200844148}, {"application_name": "icloud-base", "bytes_total_volume": 190426144}, {"application_name": "slack-uploading", "bytes_total_volume": 170171490}, {"application_name": "facebook-base", "bytes_total_volume": 167861948}, {"application_name": "github-base", "bytes_total_volume": 166882736}, {"application_name": "figma-base", "bytes_total_volume": 159890819}, {"application_name": "ms-teams", "bytes_total_volume": 149263809}, {"application_name": "slack-downloading", "bytes_total_volume": 140657125}, {"application_name": "google-chat-base", "bytes_total_volume": 136811887}, {"application_name": "facebook-video", "bytes_total_volume": 130906430}, {"application_name": "ms-onedrive-downloading", "bytes_total_volume": 127241182}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "private_apps", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "all", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 164845951}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "private_apps", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 401288916}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "internet", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "all", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 10544962729}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "internet", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 105574812285}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "private_apps", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 566134867}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "internet", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 116119775014}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "internet", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 381, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 116119775014}, {"application_name": "ssl", "bytes_total_volume": 95296315838}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 48550896562}, {"application_name": "express-mode", "bytes_total_volume": 36907714901}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32514686830}, {"application_name": "web-browsing", "bytes_total_volume": 18086344561}, {"application_name": "unknown-udp", "bytes_total_volume": 13799484861}, {"application_name": "ssh", "bytes_total_volume": 11327403903}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 10789218884}, {"application_name": "traps-management-service", "bytes_total_volume": 10289724804}, {"application_name": "http-video", "bytes_total_volume": 7766987107}, {"application_name": "paloalto-updates", "bytes_total_volume": 5493293251}, {"application_name": "ms-update", "bytes_total_volume": 5109392045}, {"application_name": "cortex-xdr", "bytes_total_volume": 4500920686}, {"application_name": "google-play", "bytes_total_volume": 3649052872}, {"application_name": "rtcp", "bytes_total_volume": 3550249068}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2742483731}, {"application_name": "apt-get", "bytes_total_volume": 2129197324}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 1949482329}, {"application_name": "paloalto-cloud-identity", "bytes_total_volume": 1453839246}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "internet", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 344, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 105574812285}, {"application_name": "ssl", "bytes_total_volume": 85511381585}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 40003680708}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32456535254}, {"application_name": "unknown-udp", "bytes_total_volume": 13629964355}, {"application_name": "ssh", "bytes_total_volume": 11321909001}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 10785598413}, {"application_name": "traps-management-service", "bytes_total_volume": 7726765542}, {"application_name": "web-browsing", "bytes_total_volume": 6834597263}, {"application_name": "paloalto-updates", "bytes_total_volume": 5479001076}, {"application_name": "cortex-xdr", "bytes_total_volume": 4343699089}, {"application_name": "ms-update", "bytes_total_volume": 4182562363}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2740364129}, {"application_name": "apt-get", "bytes_total_volume": 2129171029}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 1933018958}, {"application_name": "paloalto-cloud-identity", "bytes_total_volume": 1453775426}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 1252261301}, {"application_name": "stun", "bytes_total_volume": 942794650}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 897049621}, {"application_name": "google-play", "bytes_total_volume": 831345380}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "aggregation_type": "last_30_days", "application_count": 7, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 116685909881}, {"application_name": "grammarly", "bytes_total_volume": 137627397}, {"application_name": "notion-base", "bytes_total_volume": 43965669}, {"application_name": "tabnine", "bytes_total_volume": 6428392}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5572244}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "bing-ai-base", "bytes_total_volume": 85141}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "all", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": ***********}, {"application_name": "grammarly", "bytes_total_volume": 115090018}, {"application_name": "notion-base", "bytes_total_volume": 17109961}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5551400}, {"application_name": "tabnine", "bytes_total_volume": 495719}, {"application_name": "bing-ai-base", "bytes_total_volume": 53665}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "aggregation_type": "last_30_days", "application_count": 7, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 105976101201}, {"application_name": "notion-base", "bytes_total_volume": 26855708}, {"application_name": "grammarly", "bytes_total_volume": 22537379}, {"application_name": "tabnine", "bytes_total_volume": 5932673}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}, {"application_name": "azure-openai-studio", "bytes_total_volume": 20844}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "internet", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "all", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 2817707492}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "private_apps", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 37674881}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "internet", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 831345380}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "internet", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 3649052872}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "private_apps", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "all", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 37507671}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "private_apps", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 167210}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 831512590}, {"application_name": "openai-chatgpt", "bytes_total_volume": 12863369}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 331334}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 3686727753}, {"application_name": "openai-chatgpt", "bytes_total_volume": 52152309}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 628701}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "all", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 2855215163}, {"application_name": "openai-chatgpt", "bytes_total_volume": 39288940}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 297367}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "notion-base", "bytes_total_volume": 26855708}, {"application_name": "grammarly", "bytes_total_volume": 22537379}, {"application_name": "tabnine", "bytes_total_volume": 5932673}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}, {"application_name": "azure-openai-studio", "bytes_total_volume": 20844}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "yes", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "notion-base", "bytes_total_volume": 26855708}, {"application_name": "grammarly", "bytes_total_volume": 22537379}, {"application_name": "tabnine", "bytes_total_volume": 5932673}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}, {"application_name": "azure-openai-studio", "bytes_total_volume": 20844}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "yes", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "notion-base", "bytes_total_volume": 26855708}, {"application_name": "grammarly", "bytes_total_volume": 22537379}, {"application_name": "tabnine", "bytes_total_volume": 5932673}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}, {"application_name": "azure-openai-studio", "bytes_total_volume": 20844}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "yes", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 39, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "notion-base", "bytes_total_volume": 26855708}, {"application_name": "grammarly", "bytes_total_volume": 22537379}, {"application_name": "openai-chatgpt", "bytes_total_volume": 12863369}, {"application_name": "tabnine", "bytes_total_volume": 5932673}, {"application_name": "synthesia-base", "bytes_total_volume": 4372976}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "openai-base", "bytes_total_volume": 2267162}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "google-gemini", "bytes_total_volume": 1688925}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 666974}, {"application_name": "github-copilot", "bytes_total_volume": 666470}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 662291}, {"application_name": "claude-base", "bytes_total_volume": 623384}, {"application_name": "huggingface-base", "bytes_total_volume": 601345}, {"application_name": "github-copilot-business", "bytes_total_volume": 537540}, {"application_name": "runway-app-base", "bytes_total_volume": 470447}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 331334}, {"application_name": "deepl-base", "bytes_total_volume": 242324}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "yes", "aggregation_type": "last_30_days", "application_count": 39, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "notion-base", "bytes_total_volume": 26855708}, {"application_name": "grammarly", "bytes_total_volume": 22537379}, {"application_name": "openai-chatgpt", "bytes_total_volume": 12863369}, {"application_name": "tabnine", "bytes_total_volume": 5932673}, {"application_name": "synthesia-base", "bytes_total_volume": 4372976}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "openai-base", "bytes_total_volume": 2267162}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "google-gemini", "bytes_total_volume": 1688925}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 666974}, {"application_name": "github-copilot", "bytes_total_volume": 666470}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 662291}, {"application_name": "claude-base", "bytes_total_volume": 623384}, {"application_name": "huggingface-base", "bytes_total_volume": 601345}, {"application_name": "github-copilot-business", "bytes_total_volume": 537540}, {"application_name": "runway-app-base", "bytes_total_volume": 470447}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 331334}, {"application_name": "deepl-base", "bytes_total_volume": 242324}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "yes", "aggregation_type": "last_30_days", "application_count": 36, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-base", "bytes_total_volume": 7305083}, {"application_name": "deepl-base", "bytes_total_volume": 5654897}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 4564737}, {"application_name": "synthesia-base", "bytes_total_volume": 4403183}, {"application_name": "github-copilot-business", "bytes_total_volume": 4343713}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "google-gemini", "bytes_total_volume": 2114509}, {"application_name": "adobe-express-base", "bytes_total_volume": 1115260}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "huggingface-base", "bytes_total_volume": 1007263}, {"application_name": "claude-base", "bytes_total_volume": 966654}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 882168}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 724759}, {"application_name": "github-copilot", "bytes_total_volume": 724632}, {"application_name": "runway-app-base", "bytes_total_volume": 470447}, {"application_name": "deepl-write", "bytes_total_volume": 359689}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}, {"application_name": "ms-copilot-studio", "bytes_total_volume": 164406}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "yes", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 36, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-base", "bytes_total_volume": 7305083}, {"application_name": "deepl-base", "bytes_total_volume": 5654897}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 4564737}, {"application_name": "synthesia-base", "bytes_total_volume": 4403183}, {"application_name": "github-copilot-business", "bytes_total_volume": 4343713}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "google-gemini", "bytes_total_volume": 2114509}, {"application_name": "adobe-express-base", "bytes_total_volume": 1115260}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "huggingface-base", "bytes_total_volume": 1007263}, {"application_name": "claude-base", "bytes_total_volume": 966654}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 882168}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 724759}, {"application_name": "github-copilot", "bytes_total_volume": 724632}, {"application_name": "runway-app-base", "bytes_total_volume": 470447}, {"application_name": "deepl-write", "bytes_total_volume": 359689}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}, {"application_name": "ms-copilot-studio", "bytes_total_volume": 164406}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "internet", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "all", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 176, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "express-mode", "bytes_total_volume": 36907714901}, {"application_name": "web-browsing", "bytes_total_volume": 11251747298}, {"application_name": "ssl", "bytes_total_volume": 9784934253}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 8547215854}, {"application_name": "http-video", "bytes_total_volume": 7412538727}, {"application_name": "rtcp", "bytes_total_volume": 3121645416}, {"application_name": "traps-management-service", "bytes_total_volume": 2562959262}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208463657}, {"application_name": "ms-update", "bytes_total_volume": 926829682}, {"application_name": "linkedin-base", "bytes_total_volume": 487408902}, {"application_name": "websocket", "bytes_total_volume": 373629986}, {"application_name": "twitter-base", "bytes_total_volume": 350017982}, {"application_name": "ping", "bytes_total_volume": 294756540}, {"application_name": "wechat-file-transfer", "bytes_total_volume": 272208634}, {"application_name": "apple-update", "bytes_total_volume": 183489843}, {"application_name": "traceroute", "bytes_total_volume": 169800444}, {"application_name": "unknown-udp", "bytes_total_volume": 169520506}, {"application_name": "cortex-xdr", "bytes_total_volume": 157221597}, {"application_name": "itunes-base", "bytes_total_volume": 138118412}, {"application_name": "quic-base", "bytes_total_volume": 134430008}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "internet", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "all", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 178, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "express-mode", "bytes_total_volume": 36907714901}, {"application_name": "web-browsing", "bytes_total_volume": 11251747298}, {"application_name": "google-base", "bytes_total_volume": 10544962729}, {"application_name": "ssl", "bytes_total_volume": 9784934253}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 8547215854}, {"application_name": "http-video", "bytes_total_volume": 7412538727}, {"application_name": "rtcp", "bytes_total_volume": 3121645416}, {"application_name": "google-play", "bytes_total_volume": 2817707492}, {"application_name": "traps-management-service", "bytes_total_volume": 2562959262}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208463657}, {"application_name": "ms-update", "bytes_total_volume": 926829682}, {"application_name": "linkedin-base", "bytes_total_volume": 487408902}, {"application_name": "websocket", "bytes_total_volume": 373629986}, {"application_name": "twitter-base", "bytes_total_volume": 350017982}, {"application_name": "ping", "bytes_total_volume": 294756540}, {"application_name": "wechat-file-transfer", "bytes_total_volume": 272208634}, {"application_name": "apple-update", "bytes_total_volume": 183489843}, {"application_name": "traceroute", "bytes_total_volume": 169800444}, {"application_name": "unknown-udp", "bytes_total_volume": 169520506}, {"application_name": "cortex-xdr", "bytes_total_volume": 157221597}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 296, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-meeting", "bytes_total_volume": 6418355883}, {"application_name": "datadog", "bytes_total_volume": 4314635626}, {"application_name": "tenable.io", "bytes_total_volume": 4181339645}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}, {"application_name": "slack-base", "bytes_total_volume": 2930628922}, {"application_name": "google-docs-base", "bytes_total_volume": 1726814602}, {"application_name": "gmail-base", "bytes_total_volume": 1550078269}, {"application_name": "salesforce-base", "bytes_total_volume": 1505467143}, {"application_name": "youtube-base", "bytes_total_volume": 1073509786}, {"application_name": "youtube-streaming", "bytes_total_volume": 1036113299}, {"application_name": "github-base", "bytes_total_volume": 906084237}, {"application_name": "facebook-base", "bytes_total_volume": 761099489}, {"application_name": "google-drive-web-base", "bytes_total_volume": 584716246}, {"application_name": "github-downloading", "bytes_total_volume": 476691961}, {"application_name": "vidyo", "bytes_total_volume": 401333710}, {"application_name": "okta", "bytes_total_volume": 389711455}, {"application_name": "zoom-base", "bytes_total_volume": 378748771}, {"application_name": "icloud-base", "bytes_total_volume": 337899915}, {"application_name": "figma-base", "bytes_total_volume": 279177883}, {"application_name": "jamf", "bytes_total_volume": 259946738}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 307, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-meeting", "bytes_total_volume": 6418355883}, {"application_name": "datadog", "bytes_total_volume": 4314635626}, {"application_name": "tenable.io", "bytes_total_volume": 4181339645}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}, {"application_name": "slack-base", "bytes_total_volume": 2930628922}, {"application_name": "google-docs-base", "bytes_total_volume": 1726814602}, {"application_name": "gmail-base", "bytes_total_volume": 1550078269}, {"application_name": "salesforce-base", "bytes_total_volume": 1505467143}, {"application_name": "youtube-base", "bytes_total_volume": 1073509786}, {"application_name": "youtube-streaming", "bytes_total_volume": 1036113299}, {"application_name": "github-base", "bytes_total_volume": 906084237}, {"application_name": "facebook-base", "bytes_total_volume": 761099489}, {"application_name": "google-drive-web-base", "bytes_total_volume": 584716246}, {"application_name": "github-downloading", "bytes_total_volume": 476691961}, {"application_name": "vidyo", "bytes_total_volume": 401333710}, {"application_name": "okta", "bytes_total_volume": 389711455}, {"application_name": "zoom-base", "bytes_total_volume": 378748771}, {"application_name": "icloud-base", "bytes_total_volume": 337899915}, {"application_name": "figma-base", "bytes_total_volume": 279177883}, {"application_name": "jamf", "bytes_total_volume": 259946738}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 253, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-meeting", "bytes_total_volume": 6362040323}, {"application_name": "datadog", "bytes_total_volume": 4300906307}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}, {"application_name": "tenable.io", "bytes_total_volume": 1701398647}, {"application_name": "salesforce-base", "bytes_total_volume": 1468190469}, {"application_name": "youtube-streaming", "bytes_total_volume": 1029597162}, {"application_name": "slack-base", "bytes_total_volume": 905896765}, {"application_name": "youtube-base", "bytes_total_volume": 811665738}, {"application_name": "github-base", "bytes_total_volume": 739201501}, {"application_name": "facebook-base", "bytes_total_volume": 593237541}, {"application_name": "github-downloading", "bytes_total_volume": 465356918}, {"application_name": "google-docs-base", "bytes_total_volume": 410422843}, {"application_name": "gmail-base", "bytes_total_volume": 405244978}, {"application_name": "zoom-base", "bytes_total_volume": 355947046}, {"application_name": "jamf", "bytes_total_volume": 259871339}, {"application_name": "jfrog-artifactory", "bytes_total_volume": 248105710}, {"application_name": "vimeo-base", "bytes_total_volume": 165204901}, {"application_name": "icloud-base", "bytes_total_volume": 147473771}, {"application_name": "figma-base", "bytes_total_volume": 119287064}, {"application_name": "okta", "bytes_total_volume": 114929657}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 262, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-meeting", "bytes_total_volume": 6362040323}, {"application_name": "datadog", "bytes_total_volume": 4300906307}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}, {"application_name": "tenable.io", "bytes_total_volume": 1701398647}, {"application_name": "salesforce-base", "bytes_total_volume": 1468190469}, {"application_name": "youtube-streaming", "bytes_total_volume": 1029597162}, {"application_name": "slack-base", "bytes_total_volume": 905896765}, {"application_name": "youtube-base", "bytes_total_volume": 811665738}, {"application_name": "github-base", "bytes_total_volume": 739201501}, {"application_name": "facebook-base", "bytes_total_volume": 593237541}, {"application_name": "github-downloading", "bytes_total_volume": 465356918}, {"application_name": "google-docs-base", "bytes_total_volume": 410422843}, {"application_name": "gmail-base", "bytes_total_volume": 405244978}, {"application_name": "zoom-base", "bytes_total_volume": 355947046}, {"application_name": "jamf", "bytes_total_volume": 259871339}, {"application_name": "jfrog-artifactory", "bytes_total_volume": 248105710}, {"application_name": "vimeo-base", "bytes_total_volume": 165204901}, {"application_name": "icloud-base", "bytes_total_volume": 147473771}, {"application_name": "figma-base", "bytes_total_volume": 119287064}, {"application_name": "okta", "bytes_total_volume": 114929657}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 3, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 12863369}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 331334}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "yes", "aggregation_type": "last_30_days", "application_count": 3, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 12863369}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 331334}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "yes", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 3, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 12863369}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 331334}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 52152309}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 628701}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "yes", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 52152309}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 628701}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "yes", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 52152309}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 628701}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "yes", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 39288940}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 297367}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "all", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 39288940}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 297367}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "yes", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 39288940}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 297367}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "yes", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 30, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "synthesia-base", "bytes_total_volume": 4372976}, {"application_name": "openai-base", "bytes_total_volume": 2267162}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "google-gemini", "bytes_total_volume": 1688925}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 666974}, {"application_name": "github-copilot", "bytes_total_volume": 666470}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 662291}, {"application_name": "claude-base", "bytes_total_volume": 623384}, {"application_name": "huggingface-base", "bytes_total_volume": 601345}, {"application_name": "github-copilot-business", "bytes_total_volume": 537540}, {"application_name": "runway-app-base", "bytes_total_volume": 470447}, {"application_name": "deepl-base", "bytes_total_volume": 242324}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}, {"application_name": "adobe-express-base", "bytes_total_volume": 149778}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 120227}, {"application_name": "writesonic-base", "bytes_total_volume": 79743}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 60272}, {"application_name": "github-copilot-chat", "bytes_total_volume": 24553}, {"application_name": "deepl-write", "bytes_total_volume": 15074}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "yes", "aggregation_type": "last_30_days", "application_count": 30, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "synthesia-base", "bytes_total_volume": 4372976}, {"application_name": "openai-base", "bytes_total_volume": 2267162}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "google-gemini", "bytes_total_volume": 1688925}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 666974}, {"application_name": "github-copilot", "bytes_total_volume": 666470}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 662291}, {"application_name": "claude-base", "bytes_total_volume": 623384}, {"application_name": "huggingface-base", "bytes_total_volume": 601345}, {"application_name": "github-copilot-business", "bytes_total_volume": 537540}, {"application_name": "runway-app-base", "bytes_total_volume": 470447}, {"application_name": "deepl-base", "bytes_total_volume": 242324}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}, {"application_name": "adobe-express-base", "bytes_total_volume": 149778}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 120227}, {"application_name": "writesonic-base", "bytes_total_volume": 79743}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 60272}, {"application_name": "github-copilot-chat", "bytes_total_volume": 24553}, {"application_name": "deepl-write", "bytes_total_volume": 15074}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "private_apps", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "all", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 70, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "insufficient-data", "bytes_total_volume": 66502395646}, {"application_name": "ssl", "bytes_total_volume": 10467130934}, {"application_name": "express-mode", "bytes_total_volume": 2417561957}, {"application_name": "ssh", "bytes_total_volume": 1311766408}, {"application_name": "ms-rdp", "bytes_total_volume": 766310500}, {"application_name": "google-base", "bytes_total_volume": 164845951}, {"application_name": "web-browsing", "bytes_total_volume": 143476578}, {"application_name": "ldap", "bytes_total_volume": 83208804}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 60405125}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 54698596}, {"application_name": "google-play", "bytes_total_volume": 37507671}, {"application_name": "vmware", "bytes_total_volume": 37247989}, {"application_name": "msrpc-base", "bytes_total_volume": 30432293}, {"application_name": "ping", "bytes_total_volume": 18440211}, {"application_name": "active-directory-base", "bytes_total_volume": 15178307}, {"application_name": "unknown-tcp", "bytes_total_volume": 12889689}, {"application_name": "ms-sms", "bytes_total_volume": 11148930}, {"application_name": "unknown-udp", "bytes_total_volume": 11016910}, {"application_name": "kerber<PERSON>", "bytes_total_volume": 10326787}, {"application_name": "paloalto-prisma-sdwan-base", "bytes_total_volume": 7787983}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "private_apps", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "all", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 68, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "insufficient-data", "bytes_total_volume": 66502395646}, {"application_name": "ssl", "bytes_total_volume": 10467130934}, {"application_name": "express-mode", "bytes_total_volume": 2417561957}, {"application_name": "ssh", "bytes_total_volume": 1311766408}, {"application_name": "ms-rdp", "bytes_total_volume": 766310500}, {"application_name": "web-browsing", "bytes_total_volume": 143476578}, {"application_name": "ldap", "bytes_total_volume": 83208804}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 60405125}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 54698596}, {"application_name": "vmware", "bytes_total_volume": 37247989}, {"application_name": "msrpc-base", "bytes_total_volume": 30432293}, {"application_name": "ping", "bytes_total_volume": 18440211}, {"application_name": "active-directory-base", "bytes_total_volume": 15178307}, {"application_name": "unknown-tcp", "bytes_total_volume": 12889689}, {"application_name": "ms-sms", "bytes_total_volume": 11148930}, {"application_name": "unknown-udp", "bytes_total_volume": 11016910}, {"application_name": "kerber<PERSON>", "bytes_total_volume": 10326787}, {"application_name": "paloalto-prisma-sdwan-base", "bytes_total_volume": 7787983}, {"application_name": "sap", "bytes_total_volume": 6539162}, {"application_name": "rtcp", "bytes_total_volume": 4824052}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "all", "aggregation_type": "last_30_days", "application_count": 394, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "insufficient-data", "bytes_total_volume": 66502926959}, {"application_name": "express-mode", "bytes_total_volume": 39325276858}, {"application_name": "ssl", "bytes_total_volume": 20252065187}, {"application_name": "web-browsing", "bytes_total_volume": 11395223876}, {"application_name": "google-base", "bytes_total_volume": ***********}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 8601914450}, {"application_name": "http-video", "bytes_total_volume": 7412538727}, {"application_name": "rtcp", "bytes_total_volume": 3126469468}, {"application_name": "google-play", "bytes_total_volume": 2855215163}, {"application_name": "traps-management-service", "bytes_total_volume": 2562959262}, {"application_name": "tenable.io", "bytes_total_volume": 2479940998}, {"application_name": "slack-base", "bytes_total_volume": 2024732157}, {"application_name": "ssh", "bytes_total_volume": 1317261310}, {"application_name": "google-docs-base", "bytes_total_volume": 1316391759}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208463657}, {"application_name": "gmail-base", "bytes_total_volume": 1144833291}, {"application_name": "ms-update", "bytes_total_volume": 927086888}, {"application_name": "ms-rdp", "bytes_total_volume": 766312512}, {"application_name": "google-drive-web-base", "bytes_total_volume": 490617795}, {"application_name": "linkedin-base", "bytes_total_volume": 487408902}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "all", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "all", "aggregation_type": "last_30_days", "application_count": 383, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "insufficient-data", "bytes_total_volume": 66502926959}, {"application_name": "express-mode", "bytes_total_volume": 39325276858}, {"application_name": "ssl", "bytes_total_volume": 20252065187}, {"application_name": "web-browsing", "bytes_total_volume": 11395223876}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 8601914450}, {"application_name": "http-video", "bytes_total_volume": 7412538727}, {"application_name": "rtcp", "bytes_total_volume": 3126469468}, {"application_name": "traps-management-service", "bytes_total_volume": 2562959262}, {"application_name": "tenable.io", "bytes_total_volume": 2479940998}, {"application_name": "slack-base", "bytes_total_volume": 2024732157}, {"application_name": "ssh", "bytes_total_volume": 1317261310}, {"application_name": "google-docs-base", "bytes_total_volume": 1316391759}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208463657}, {"application_name": "gmail-base", "bytes_total_volume": 1144833291}, {"application_name": "ms-update", "bytes_total_volume": 927086888}, {"application_name": "ms-rdp", "bytes_total_volume": 766312512}, {"application_name": "google-drive-web-base", "bytes_total_volume": 490617795}, {"application_name": "linkedin-base", "bytes_total_volume": 487408902}, {"application_name": "vidyo", "bytes_total_volume": 400736999}, {"application_name": "websocket", "bytes_total_volume": 374226470}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 607, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 228474034548}, {"application_name": "ssh", "bytes_total_volume": 52351129632}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 46959407715}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 35552136728}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32446376936}, {"application_name": "web-browsing", "bytes_total_volume": 29496608189}, {"application_name": "mysql", "bytes_total_volume": 23865158659}, {"application_name": "unknown-udp", "bytes_total_volume": 13693733852}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 9674871020}, {"application_name": "traps-management-service", "bytes_total_volume": 6384247662}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569487144}, {"application_name": "paloalto-updates", "bytes_total_volume": 5417235479}, {"application_name": "cortex-xdr", "bytes_total_volume": 4317252098}, {"application_name": "datadog", "bytes_total_volume": 4286176171}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "rtp-base", "bytes_total_volume": 3928738460}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}, {"application_name": "ocsp", "bytes_total_volume": 3741568178}, {"application_name": "ms-update", "bytes_total_volume": 3253084088}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2719987465}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 615, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 228474034548}, {"application_name": "google-base", "bytes_total_volume": 102444374312}, {"application_name": "ssh", "bytes_total_volume": 52351129632}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 46959407715}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 35552136728}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32446376936}, {"application_name": "web-browsing", "bytes_total_volume": 29496608189}, {"application_name": "mysql", "bytes_total_volume": 23865158659}, {"application_name": "unknown-udp", "bytes_total_volume": 13693733852}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 9674871020}, {"application_name": "traps-management-service", "bytes_total_volume": 6384247662}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569487144}, {"application_name": "paloalto-updates", "bytes_total_volume": 5417235479}, {"application_name": "cortex-xdr", "bytes_total_volume": 4317252098}, {"application_name": "datadog", "bytes_total_volume": 4286176171}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "rtp-base", "bytes_total_volume": 3928738460}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}, {"application_name": "ocsp", "bytes_total_volume": 3741568178}, {"application_name": "ms-update", "bytes_total_volume": 3253084088}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 620, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 228820750231}, {"application_name": "google-base", "bytes_total_volume": 102461035175}, {"application_name": "insufficient-data", "bytes_total_volume": 66629093089}, {"application_name": "ssh", "bytes_total_volume": 52351129632}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 46959836854}, {"application_name": "express-mode", "bytes_total_volume": 38831944754}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 35707683039}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32446376936}, {"application_name": "web-browsing", "bytes_total_volume": 29509774992}, {"application_name": "mysql", "bytes_total_volume": 23865158659}, {"application_name": "unknown-udp", "bytes_total_volume": 13693733852}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 9678491491}, {"application_name": "traps-management-service", "bytes_total_volume": 6395316574}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569487144}, {"application_name": "paloalto-updates", "bytes_total_volume": 5429062553}, {"application_name": "cortex-xdr", "bytes_total_volume": 4336492677}, {"application_name": "datadog", "bytes_total_volume": 4287184315}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "rtp-base", "bytes_total_volume": 3928759852}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "private_apps", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 177, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 157652349143}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 46959407715}, {"application_name": "ssh", "bytes_total_volume": 41038871078}, {"application_name": "web-browsing", "bytes_total_volume": 26202192903}, {"application_name": "mysql", "bytes_total_volume": 23865158659}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569487144}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "rtp-base", "bytes_total_volume": 3625781916}, {"application_name": "ocsp", "bytes_total_volume": 3045394559}, {"application_name": "perforce", "bytes_total_volume": 2100408457}, {"application_name": "vmware", "bytes_total_volume": 1961235451}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 1581887239}, {"application_name": "ldap", "bytes_total_volume": 757864989}, {"application_name": "snmpv3", "bytes_total_volume": 671843427}, {"application_name": "tftp", "bytes_total_volume": 505805507}, {"application_name": "panorama", "bytes_total_volume": 453688912}, {"application_name": "ms-update-optimization-p2p", "bytes_total_volume": 439138205}, {"application_name": "not-applicable", "bytes_total_volume": 386192349}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 375177819}, {"application_name": "nfs", "bytes_total_volume": 358109840}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "private_apps", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 175, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 157652349143}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 46959407715}, {"application_name": "ssh", "bytes_total_volume": 41038871078}, {"application_name": "web-browsing", "bytes_total_volume": 26202192903}, {"application_name": "mysql", "bytes_total_volume": 23865158659}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569487144}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "rtp-base", "bytes_total_volume": 3625781916}, {"application_name": "ocsp", "bytes_total_volume": 3045394559}, {"application_name": "perforce", "bytes_total_volume": 2100408457}, {"application_name": "vmware", "bytes_total_volume": 1961235451}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 1581887239}, {"application_name": "ldap", "bytes_total_volume": 757864989}, {"application_name": "snmpv3", "bytes_total_volume": 671843427}, {"application_name": "tftp", "bytes_total_volume": 505805507}, {"application_name": "panorama", "bytes_total_volume": 453688912}, {"application_name": "ms-update-optimization-p2p", "bytes_total_volume": 439138205}, {"application_name": "not-applicable", "bytes_total_volume": 386192349}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 375177819}, {"application_name": "nfs", "bytes_total_volume": 358109840}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "internet", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 316, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 70907980047}, {"application_name": "express-mode", "bytes_total_volume": 36907714901}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 35332499120}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32446260032}, {"application_name": "unknown-udp", "bytes_total_volume": 13629765256}, {"application_name": "ssh", "bytes_total_volume": 11312258554}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 9678491491}, {"application_name": "traps-management-service", "bytes_total_volume": 6395107378}, {"application_name": "paloalto-updates", "bytes_total_volume": 5408867408}, {"application_name": "cortex-xdr", "bytes_total_volume": 4336492677}, {"application_name": "web-browsing", "bytes_total_volume": 3307582089}, {"application_name": "ms-update", "bytes_total_volume": 3250704346}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2722005457}, {"application_name": "apt-get", "bytes_total_volume": 2121739050}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 1902961406}, {"application_name": "paloalto-cloud-identity", "bytes_total_volume": 1453711052}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 1199978839}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 909366962}, {"application_name": "stun", "bytes_total_volume": 833901504}, {"application_name": "ocsp", "bytes_total_volume": 696639121}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 612, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 228820750231}, {"application_name": "insufficient-data", "bytes_total_volume": 66629093089}, {"application_name": "ssh", "bytes_total_volume": 52351129632}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 46959836854}, {"application_name": "express-mode", "bytes_total_volume": 38831944754}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 35707683039}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32446376936}, {"application_name": "web-browsing", "bytes_total_volume": 29509774992}, {"application_name": "mysql", "bytes_total_volume": 23865158659}, {"application_name": "unknown-udp", "bytes_total_volume": 13693733852}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 9678491491}, {"application_name": "traps-management-service", "bytes_total_volume": 6395316574}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569487144}, {"application_name": "paloalto-updates", "bytes_total_volume": 5429062553}, {"application_name": "cortex-xdr", "bytes_total_volume": 4336492677}, {"application_name": "datadog", "bytes_total_volume": 4287184315}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "rtp-base", "bytes_total_volume": 3928759852}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}, {"application_name": "ocsp", "bytes_total_volume": 3742033680}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "private_apps", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 176, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 157912770184}, {"application_name": "insufficient-data", "bytes_total_volume": 66620695930}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 46959836854}, {"application_name": "ssh", "bytes_total_volume": 41038871078}, {"application_name": "web-browsing", "bytes_total_volume": 26202192903}, {"application_name": "mysql", "bytes_total_volume": 23865158659}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569487144}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "rtp-base", "bytes_total_volume": 3625803308}, {"application_name": "ocsp", "bytes_total_volume": 3045394559}, {"application_name": "perforce", "bytes_total_volume": 2100408457}, {"application_name": "vmware", "bytes_total_volume": 1961305463}, {"application_name": "express-mode", "bytes_total_volume": 1924229853}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 1581887239}, {"application_name": "ldap", "bytes_total_volume": 759483281}, {"application_name": "snmpv3", "bytes_total_volume": 671843427}, {"application_name": "tftp", "bytes_total_volume": 505805507}, {"application_name": "panorama", "bytes_total_volume": 453688912}, {"application_name": "ms-update-optimization-p2p", "bytes_total_volume": 439138205}, {"application_name": "not-applicable", "bytes_total_volume": 386192349}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "private_apps", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 178, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 157912770184}, {"application_name": "insufficient-data", "bytes_total_volume": 66620695930}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 46959836854}, {"application_name": "ssh", "bytes_total_volume": 41038871078}, {"application_name": "web-browsing", "bytes_total_volume": 26202192903}, {"application_name": "mysql", "bytes_total_volume": 23865158659}, {"application_name": "mssql-db-unencrypted", "bytes_total_volume": 5569487144}, {"application_name": "engweb-rabbitmq", "bytes_total_volume": 4233674628}, {"application_name": "rtp-base", "bytes_total_volume": 3625803308}, {"application_name": "ocsp", "bytes_total_volume": 3045394559}, {"application_name": "perforce", "bytes_total_volume": 2100408457}, {"application_name": "vmware", "bytes_total_volume": 1961305463}, {"application_name": "express-mode", "bytes_total_volume": 1924229853}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 1581887239}, {"application_name": "ldap", "bytes_total_volume": 759483281}, {"application_name": "snmpv3", "bytes_total_volume": 671843427}, {"application_name": "tftp", "bytes_total_volume": 505805507}, {"application_name": "panorama", "bytes_total_volume": 453688912}, {"application_name": "ms-update-optimization-p2p", "bytes_total_volume": 439138205}, {"application_name": "not-applicable", "bytes_total_volume": 386192349}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "internet", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 308, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 70821685405}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 35176958909}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32446260032}, {"application_name": "unknown-udp", "bytes_total_volume": 13629765256}, {"application_name": "ssh", "bytes_total_volume": 11312258554}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 9674871020}, {"application_name": "traps-management-service", "bytes_total_volume": 6384038466}, {"application_name": "paloalto-updates", "bytes_total_volume": 5397040334}, {"application_name": "cortex-xdr", "bytes_total_volume": 4317252098}, {"application_name": "web-browsing", "bytes_total_volume": 3294415286}, {"application_name": "ms-update", "bytes_total_volume": 3249555797}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2719987465}, {"application_name": "apt-get", "bytes_total_volume": 2121739050}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 1886541597}, {"application_name": "paloalto-cloud-identity", "bytes_total_volume": 1453711052}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 1199542919}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 896900654}, {"application_name": "stun", "bytes_total_volume": 833896806}, {"application_name": "ocsp", "bytes_total_volume": 696173619}, {"application_name": "paloalto-wildfire-cloud", "bytes_total_volume": 673351430}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 204, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "datadog", "bytes_total_volume": 4287184315}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}, {"application_name": "zoom-meeting", "bytes_total_volume": 2615973042}, {"application_name": "salesforce-base", "bytes_total_volume": 1426745971}, {"application_name": "youtube-base", "bytes_total_volume": 726232760}, {"application_name": "github-base", "bytes_total_volume": 719759297}, {"application_name": "tenable.io", "bytes_total_volume": 607759533}, {"application_name": "facebook-base", "bytes_total_volume": 546083447}, {"application_name": "jfrog-artifactory", "bytes_total_volume": 246692874}, {"application_name": "zoom-base", "bytes_total_volume": 242793892}, {"application_name": "slack-base", "bytes_total_volume": 180604928}, {"application_name": "icloud-base", "bytes_total_volume": 85126805}, {"application_name": "bluexp", "bytes_total_volume": 73918075}, {"application_name": "gmail-base", "bytes_total_volume": 66512459}, {"application_name": "okta", "bytes_total_volume": 62469593}, {"application_name": "github-pages", "bytes_total_volume": 62037040}, {"application_name": "google-docs-base", "bytes_total_volume": 46098327}, {"application_name": "instagram-base", "bytes_total_volume": 35392606}, {"application_name": "jamf", "bytes_total_volume": 28795041}, {"application_name": "dropbox-base", "bytes_total_volume": 26661977}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 210, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "datadog", "bytes_total_volume": 4287184315}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}, {"application_name": "zoom-meeting", "bytes_total_volume": 2615973042}, {"application_name": "salesforce-base", "bytes_total_volume": 1426745971}, {"application_name": "youtube-base", "bytes_total_volume": 726232760}, {"application_name": "github-base", "bytes_total_volume": 719759297}, {"application_name": "tenable.io", "bytes_total_volume": 607759533}, {"application_name": "facebook-base", "bytes_total_volume": 546083447}, {"application_name": "jfrog-artifactory", "bytes_total_volume": 246692874}, {"application_name": "zoom-base", "bytes_total_volume": 242793892}, {"application_name": "slack-base", "bytes_total_volume": 180604928}, {"application_name": "icloud-base", "bytes_total_volume": 85126805}, {"application_name": "bluexp", "bytes_total_volume": 73918075}, {"application_name": "gmail-base", "bytes_total_volume": 66512459}, {"application_name": "okta", "bytes_total_volume": 62469593}, {"application_name": "github-pages", "bytes_total_volume": 62037040}, {"application_name": "google-docs-base", "bytes_total_volume": 46098327}, {"application_name": "instagram-base", "bytes_total_volume": 35392606}, {"application_name": "jamf", "bytes_total_volume": 28795041}, {"application_name": "dropbox-base", "bytes_total_volume": 26661977}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 204, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "datadog", "bytes_total_volume": 4286176171}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}, {"application_name": "zoom-meeting", "bytes_total_volume": 2615970268}, {"application_name": "salesforce-base", "bytes_total_volume": 1426723188}, {"application_name": "youtube-base", "bytes_total_volume": 726117065}, {"application_name": "github-base", "bytes_total_volume": 719736085}, {"application_name": "tenable.io", "bytes_total_volume": 607745025}, {"application_name": "facebook-base", "bytes_total_volume": 545969309}, {"application_name": "jfrog-artifactory", "bytes_total_volume": 246692874}, {"application_name": "zoom-base", "bytes_total_volume": 236609127}, {"application_name": "slack-base", "bytes_total_volume": 177526105}, {"application_name": "icloud-base", "bytes_total_volume": 83957386}, {"application_name": "bluexp", "bytes_total_volume": 73918075}, {"application_name": "gmail-base", "bytes_total_volume": 66284910}, {"application_name": "okta", "bytes_total_volume": 62305769}, {"application_name": "github-pages", "bytes_total_volume": 62037040}, {"application_name": "google-docs-base", "bytes_total_volume": 46019800}, {"application_name": "instagram-base", "bytes_total_volume": 35392606}, {"application_name": "jamf", "bytes_total_volume": 28795041}, {"application_name": "dropbox-base", "bytes_total_volume": 26661977}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 210, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "datadog", "bytes_total_volume": 4286176171}, {"application_name": "tableau-base", "bytes_total_volume": 3922708094}, {"application_name": "zoom-meeting", "bytes_total_volume": 2615970268}, {"application_name": "salesforce-base", "bytes_total_volume": 1426723188}, {"application_name": "youtube-base", "bytes_total_volume": 726117065}, {"application_name": "github-base", "bytes_total_volume": 719736085}, {"application_name": "tenable.io", "bytes_total_volume": 607745025}, {"application_name": "facebook-base", "bytes_total_volume": 545969309}, {"application_name": "jfrog-artifactory", "bytes_total_volume": 246692874}, {"application_name": "zoom-base", "bytes_total_volume": 236609127}, {"application_name": "slack-base", "bytes_total_volume": 177526105}, {"application_name": "icloud-base", "bytes_total_volume": 83957386}, {"application_name": "bluexp", "bytes_total_volume": 73918075}, {"application_name": "gmail-base", "bytes_total_volume": 66284910}, {"application_name": "okta", "bytes_total_volume": 62305769}, {"application_name": "github-pages", "bytes_total_volume": 62037040}, {"application_name": "google-docs-base", "bytes_total_volume": 46019800}, {"application_name": "instagram-base", "bytes_total_volume": 35392606}, {"application_name": "jamf", "bytes_total_volume": 28795041}, {"application_name": "dropbox-base", "bytes_total_volume": 26661977}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "yes", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tabnine", "bytes_total_volume": 5315908}, {"application_name": "notion-base", "bytes_total_volume": 4764085}, {"application_name": "grammarly", "bytes_total_volume": 3079294}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tabnine", "bytes_total_volume": 5315908}, {"application_name": "notion-base", "bytes_total_volume": 4764085}, {"application_name": "grammarly", "bytes_total_volume": 3079294}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tabnine", "bytes_total_volume": 5315908}, {"application_name": "notion-base", "bytes_total_volume": 4764085}, {"application_name": "grammarly", "bytes_total_volume": 3050703}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "yes", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tabnine", "bytes_total_volume": 5315908}, {"application_name": "notion-base", "bytes_total_volume": 4764085}, {"application_name": "grammarly", "bytes_total_volume": 3079294}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tabnine", "bytes_total_volume": 5315908}, {"application_name": "notion-base", "bytes_total_volume": 4764085}, {"application_name": "grammarly", "bytes_total_volume": 3050703}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tabnine", "bytes_total_volume": 5315908}, {"application_name": "notion-base", "bytes_total_volume": 4764085}, {"application_name": "grammarly", "bytes_total_volume": 3050703}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "all", "platform_type": "all", "is_genai": "yes", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 25, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tabnine", "bytes_total_volume": 5315908}, {"application_name": "notion-base", "bytes_total_volume": 4764085}, {"application_name": "synthesia-base", "bytes_total_volume": 4372976}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "grammarly", "bytes_total_volume": 3079294}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "openai-base", "bytes_total_volume": 1875077}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "openai-chatgpt", "bytes_total_volume": 879865}, {"application_name": "huggingface-base", "bytes_total_volume": 601345}, {"application_name": "runway-app-base", "bytes_total_volume": 468781}, {"application_name": "github-copilot", "bytes_total_volume": 247653}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 120227}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 80025}, {"application_name": "writesonic-base", "bytes_total_volume": 79743}, {"application_name": "deepl-base", "bytes_total_volume": 47834}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 16041}, {"application_name": "adobe-firefly-base", "bytes_total_volume": 12876}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 25, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tabnine", "bytes_total_volume": 5315908}, {"application_name": "notion-base", "bytes_total_volume": 4764085}, {"application_name": "synthesia-base", "bytes_total_volume": 4372976}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "grammarly", "bytes_total_volume": 3050703}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "openai-base", "bytes_total_volume": 1875077}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "openai-chatgpt", "bytes_total_volume": 879865}, {"application_name": "huggingface-base", "bytes_total_volume": 601345}, {"application_name": "runway-app-base", "bytes_total_volume": 468781}, {"application_name": "github-copilot", "bytes_total_volume": 247653}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 120227}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 80025}, {"application_name": "writesonic-base", "bytes_total_volume": 79743}, {"application_name": "deepl-base", "bytes_total_volume": 47834}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 16041}, {"application_name": "adobe-firefly-base", "bytes_total_volume": 12876}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 25, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tabnine", "bytes_total_volume": 5315908}, {"application_name": "notion-base", "bytes_total_volume": 4764085}, {"application_name": "synthesia-base", "bytes_total_volume": 4372976}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "grammarly", "bytes_total_volume": 3050703}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "openai-base", "bytes_total_volume": 1875077}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "openai-chatgpt", "bytes_total_volume": 879865}, {"application_name": "huggingface-base", "bytes_total_volume": 601345}, {"application_name": "runway-app-base", "bytes_total_volume": 468781}, {"application_name": "github-copilot", "bytes_total_volume": 247653}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 120227}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 80025}, {"application_name": "writesonic-base", "bytes_total_volume": 79743}, {"application_name": "deepl-base", "bytes_total_volume": 47834}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 16041}, {"application_name": "adobe-firefly-base", "bytes_total_volume": 12876}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "all", "platform_type": "all", "is_genai": "yes", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 25, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tabnine", "bytes_total_volume": 5315908}, {"application_name": "notion-base", "bytes_total_volume": 4764085}, {"application_name": "synthesia-base", "bytes_total_volume": 4372976}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "grammarly", "bytes_total_volume": 3079294}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "openai-base", "bytes_total_volume": 1875077}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "openai-chatgpt", "bytes_total_volume": 879865}, {"application_name": "huggingface-base", "bytes_total_volume": 601345}, {"application_name": "runway-app-base", "bytes_total_volume": 468781}, {"application_name": "github-copilot", "bytes_total_volume": 247653}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 120227}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 80025}, {"application_name": "writesonic-base", "bytes_total_volume": 79743}, {"application_name": "deepl-base", "bytes_total_volume": 47834}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 16041}, {"application_name": "adobe-firefly-base", "bytes_total_volume": 12876}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 28591}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "yes", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 28591}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "yes", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 28591}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "yes", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 28591}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "yes", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 28591}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 30, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-base", "bytes_total_volume": 6184765}, {"application_name": "slack-base", "bytes_total_volume": 3078823}, {"application_name": "icloud-base", "bytes_total_volume": 1169419}, {"application_name": "datadog", "bytes_total_volume": 1008144}, {"application_name": "google-chat-base", "bytes_total_volume": 428012}, {"application_name": "mailchimp", "bytes_total_volume": 302914}, {"application_name": "gmail-base", "bytes_total_volume": 227549}, {"application_name": "google-drive-web-base", "bytes_total_volume": 199485}, {"application_name": "okta", "bytes_total_volume": 163824}, {"application_name": "ms-office365-base", "bytes_total_volume": 132332}, {"application_name": "youtube-base", "bytes_total_volume": 115695}, {"application_name": "facebook-base", "bytes_total_volume": 114138}, {"application_name": "naver-line", "bytes_total_volume": 91993}, {"application_name": "zoom-phone", "bytes_total_volume": 86154}, {"application_name": "google-docs-base", "bytes_total_volume": 78527}, {"application_name": "microsoft-intune", "bytes_total_volume": 65917}, {"application_name": "smartsheet-base", "bytes_total_volume": 60210}, {"application_name": "pagerduty-base", "bytes_total_volume": 54265}, {"application_name": "lastpass", "bytes_total_volume": 32800}, {"application_name": "asana-base", "bytes_total_volume": 29277}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 29, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-base", "bytes_total_volume": 6184765}, {"application_name": "slack-base", "bytes_total_volume": 3078823}, {"application_name": "icloud-base", "bytes_total_volume": 1169419}, {"application_name": "datadog", "bytes_total_volume": 1008144}, {"application_name": "google-chat-base", "bytes_total_volume": 428012}, {"application_name": "mailchimp", "bytes_total_volume": 302914}, {"application_name": "gmail-base", "bytes_total_volume": 227549}, {"application_name": "google-drive-web-base", "bytes_total_volume": 199485}, {"application_name": "okta", "bytes_total_volume": 163824}, {"application_name": "ms-office365-base", "bytes_total_volume": 132332}, {"application_name": "youtube-base", "bytes_total_volume": 115695}, {"application_name": "facebook-base", "bytes_total_volume": 114138}, {"application_name": "naver-line", "bytes_total_volume": 91993}, {"application_name": "zoom-phone", "bytes_total_volume": 86154}, {"application_name": "google-docs-base", "bytes_total_volume": 78527}, {"application_name": "microsoft-intune", "bytes_total_volume": 65917}, {"application_name": "smartsheet-base", "bytes_total_volume": 60210}, {"application_name": "pagerduty-base", "bytes_total_volume": 54265}, {"application_name": "lastpass", "bytes_total_volume": 32800}, {"application_name": "asana-base", "bytes_total_volume": 29277}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "internet", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 102122851443}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "private_apps", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 338081253}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "internet", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 102106293059}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "private_apps", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 338183732}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "private_apps", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 102479}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "internet", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 16558384}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "internet", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 318, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 102122851443}, {"application_name": "ssl", "bytes_total_volume": 70907980047}, {"application_name": "express-mode", "bytes_total_volume": 36907714901}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 35332499120}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32446260032}, {"application_name": "unknown-udp", "bytes_total_volume": 13629765256}, {"application_name": "ssh", "bytes_total_volume": 11312258554}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 9678491491}, {"application_name": "traps-management-service", "bytes_total_volume": 6395107378}, {"application_name": "paloalto-updates", "bytes_total_volume": 5408867408}, {"application_name": "cortex-xdr", "bytes_total_volume": 4336492677}, {"application_name": "web-browsing", "bytes_total_volume": 3307582089}, {"application_name": "ms-update", "bytes_total_volume": 3250704346}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2722005457}, {"application_name": "apt-get", "bytes_total_volume": 2121739050}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 1902961406}, {"application_name": "paloalto-cloud-identity", "bytes_total_volume": 1453711052}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 1199978839}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 909366962}, {"application_name": "stun", "bytes_total_volume": 833901504}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "internet", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 310, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 102106293059}, {"application_name": "ssl", "bytes_total_volume": 70821685405}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 35176958909}, {"application_name": "ipsec-esp-udp", "bytes_total_volume": 32446260032}, {"application_name": "unknown-udp", "bytes_total_volume": 13629765256}, {"application_name": "ssh", "bytes_total_volume": 11312258554}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 9674871020}, {"application_name": "traps-management-service", "bytes_total_volume": 6384038466}, {"application_name": "paloalto-updates", "bytes_total_volume": 5397040334}, {"application_name": "cortex-xdr", "bytes_total_volume": 4317252098}, {"application_name": "web-browsing", "bytes_total_volume": 3294415286}, {"application_name": "ms-update", "bytes_total_volume": 3249555797}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2719987465}, {"application_name": "apt-get", "bytes_total_volume": 2121739050}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 1886541597}, {"application_name": "paloalto-cloud-identity", "bytes_total_volume": 1453711052}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 1199542919}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 896900654}, {"application_name": "stun", "bytes_total_volume": 833896806}, {"application_name": "ocsp", "bytes_total_volume": 696173619}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 102444374312}, {"application_name": "tabnine", "bytes_total_volume": 5315908}, {"application_name": "notion-base", "bytes_total_volume": 4764085}, {"application_name": "grammarly", "bytes_total_volume": 3050703}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 102461035175}, {"application_name": "tabnine", "bytes_total_volume": 5315908}, {"application_name": "notion-base", "bytes_total_volume": 4764085}, {"application_name": "grammarly", "bytes_total_volume": 3079294}, {"application_name": "bing-ai-base", "bytes_total_volume": 31476}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 2, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 16660863}, {"application_name": "grammarly", "bytes_total_volume": 28591}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "internet", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 430965}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "internet", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 45313072}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "private_apps", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 137504}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 430965}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "internet", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 45744037}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "private_apps", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 137504}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 3, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 45881541}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "openai-chatgpt", "bytes_total_volume": 879865}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 3, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 45450576}, {"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "openai-chatgpt", "bytes_total_volume": 879865}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "internet", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 54, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "express-mode", "bytes_total_volume": 36907714901}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 155540211}, {"application_name": "ping", "bytes_total_volume": 102967678}, {"application_name": "ssl", "bytes_total_volume": 86294642}, {"application_name": "traceroute", "bytes_total_volume": 40204142}, {"application_name": "cortex-xdr", "bytes_total_volume": 19240579}, {"application_name": "google-base", "bytes_total_volume": 16558384}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 16419809}, {"application_name": "web-browsing", "bytes_total_volume": 13166803}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 12466308}, {"application_name": "paloalto-updates", "bytes_total_volume": 11827074}, {"application_name": "traps-management-service", "bytes_total_volume": 11068912}, {"application_name": "paloalto-iot-security", "bytes_total_volume": 7210256}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 3620471}, {"application_name": "paloalto-device-telemetry", "bytes_total_volume": 2724850}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2017992}, {"application_name": "ms-update", "bytes_total_volume": 1148549}, {"application_name": "incomplete", "bytes_total_volume": 1056610}, {"application_name": "apple-update", "bytes_total_volume": 550878}, {"application_name": "ocsp", "bytes_total_volume": 465502}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "internet", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 52, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "express-mode", "bytes_total_volume": 36907714901}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 155540211}, {"application_name": "ping", "bytes_total_volume": 102967678}, {"application_name": "ssl", "bytes_total_volume": 86294642}, {"application_name": "traceroute", "bytes_total_volume": 40204142}, {"application_name": "cortex-xdr", "bytes_total_volume": 19240579}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 16419809}, {"application_name": "web-browsing", "bytes_total_volume": 13166803}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 12466308}, {"application_name": "paloalto-updates", "bytes_total_volume": 11827074}, {"application_name": "traps-management-service", "bytes_total_volume": 11068912}, {"application_name": "paloalto-iot-security", "bytes_total_volume": 7210256}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 3620471}, {"application_name": "paloalto-device-telemetry", "bytes_total_volume": 2724850}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2017992}, {"application_name": "ms-update", "bytes_total_volume": 1148549}, {"application_name": "incomplete", "bytes_total_volume": 1056610}, {"application_name": "apple-update", "bytes_total_volume": 550878}, {"application_name": "ocsp", "bytes_total_volume": 465502}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 435920}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 2, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "openai-chatgpt", "bytes_total_volume": 879865}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 2, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "openai-chatgpt", "bytes_total_volume": 879865}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "yes", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 2, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "openai-chatgpt", "bytes_total_volume": 879865}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "yes", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 2, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "openai-chatgpt", "bytes_total_volume": 879865}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 2, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "openai-chatgpt", "bytes_total_volume": 879865}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 2, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "jasper-ai-base", "bytes_total_volume": 3644725}, {"application_name": "openai-chatgpt", "bytes_total_volume": 879865}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "yes", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 19, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "synthesia-base", "bytes_total_volume": 4372976}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "openai-base", "bytes_total_volume": 1875077}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "huggingface-base", "bytes_total_volume": 601345}, {"application_name": "runway-app-base", "bytes_total_volume": 468781}, {"application_name": "github-copilot", "bytes_total_volume": 247653}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 120227}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 80025}, {"application_name": "writesonic-base", "bytes_total_volume": 79743}, {"application_name": "deepl-base", "bytes_total_volume": 47834}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 16041}, {"application_name": "adobe-firefly-base", "bytes_total_volume": 12876}, {"application_name": "elevenlabs-base", "bytes_total_volume": 9692}, {"application_name": "sembly-base", "bytes_total_volume": 8689}, {"application_name": "chatbot-base", "bytes_total_volume": 7818}, {"application_name": "elaiapp-base", "bytes_total_volume": 5585}, {"application_name": "regie.ai-base", "bytes_total_volume": 2005}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "yes", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 19, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "synthesia-base", "bytes_total_volume": 4372976}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "openai-base", "bytes_total_volume": 1875077}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "huggingface-base", "bytes_total_volume": 601345}, {"application_name": "runway-app-base", "bytes_total_volume": 468781}, {"application_name": "github-copilot", "bytes_total_volume": 247653}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 120227}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 80025}, {"application_name": "writesonic-base", "bytes_total_volume": 79743}, {"application_name": "deepl-base", "bytes_total_volume": 47834}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 16041}, {"application_name": "adobe-firefly-base", "bytes_total_volume": 12876}, {"application_name": "elevenlabs-base", "bytes_total_volume": 9692}, {"application_name": "sembly-base", "bytes_total_volume": 8689}, {"application_name": "chatbot-base", "bytes_total_volume": 7818}, {"application_name": "elaiapp-base", "bytes_total_volume": 5585}, {"application_name": "regie.ai-base", "bytes_total_volume": 2005}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "OTHER", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 19, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "synthesia-base", "bytes_total_volume": 4372976}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "openai-base", "bytes_total_volume": 1875077}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "huggingface-base", "bytes_total_volume": 601345}, {"application_name": "runway-app-base", "bytes_total_volume": 468781}, {"application_name": "github-copilot", "bytes_total_volume": 247653}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 120227}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 80025}, {"application_name": "writesonic-base", "bytes_total_volume": 79743}, {"application_name": "deepl-base", "bytes_total_volume": 47834}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 16041}, {"application_name": "adobe-firefly-base", "bytes_total_volume": 12876}, {"application_name": "elevenlabs-base", "bytes_total_volume": 9692}, {"application_name": "sembly-base", "bytes_total_volume": 8689}, {"application_name": "chatbot-base", "bytes_total_volume": 7818}, {"application_name": "elaiapp-base", "bytes_total_volume": 5585}, {"application_name": "regie.ai-base", "bytes_total_volume": 2005}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 19, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "synthesia-base", "bytes_total_volume": 4372976}, {"application_name": "copy.ai-base", "bytes_total_volume": 2197152}, {"application_name": "openai-base", "bytes_total_volume": 1875077}, {"application_name": "anyword-base", "bytes_total_volume": 1076931}, {"application_name": "huggingface-base", "bytes_total_volume": 601345}, {"application_name": "runway-app-base", "bytes_total_volume": 468781}, {"application_name": "github-copilot", "bytes_total_volume": 247653}, {"application_name": "meetgeek-base", "bytes_total_volume": 222916}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 120227}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 80025}, {"application_name": "writesonic-base", "bytes_total_volume": 79743}, {"application_name": "deepl-base", "bytes_total_volume": 47834}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 16041}, {"application_name": "adobe-firefly-base", "bytes_total_volume": 12876}, {"application_name": "elevenlabs-base", "bytes_total_volume": 9692}, {"application_name": "sembly-base", "bytes_total_volume": 8689}, {"application_name": "chatbot-base", "bytes_total_volume": 7818}, {"application_name": "elaiapp-base", "bytes_total_volume": 5585}, {"application_name": "regie.ai-base", "bytes_total_volume": 2005}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "private_apps", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 30, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "insufficient-data", "bytes_total_volume": 66502187246}, {"application_name": "express-mode", "bytes_total_volume": 1924229853}, {"application_name": "ssl", "bytes_total_volume": 260421041}, {"application_name": "ping", "bytes_total_volume": 4738103}, {"application_name": "ldap", "bytes_total_volume": 1618292}, {"application_name": "icmp", "bytes_total_volume": 1111897}, {"application_name": "syslog", "bytes_total_volume": 690028}, {"application_name": "snmp-base", "bytes_total_volume": 618832}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 429139}, {"application_name": "incomplete", "bytes_total_volume": 426228}, {"application_name": "traceroute", "bytes_total_volume": 416166}, {"application_name": "radius", "bytes_total_volume": 363876}, {"application_name": "kerber<PERSON>", "bytes_total_volume": 329443}, {"application_name": "snmp-trap", "bytes_total_volume": 267126}, {"application_name": "tacacs-plus", "bytes_total_volume": 188228}, {"application_name": "dhcp", "bytes_total_volume": 138485}, {"application_name": "vmware", "bytes_total_volume": 70012}, {"application_name": "unknown-tcp", "bytes_total_volume": 54942}, {"application_name": "msrpc-base", "bytes_total_volume": 50044}, {"application_name": "active-directory-base", "bytes_total_volume": 38470}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "private_apps", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 31, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "insufficient-data", "bytes_total_volume": 66502187246}, {"application_name": "express-mode", "bytes_total_volume": 1924229853}, {"application_name": "ssl", "bytes_total_volume": 260421041}, {"application_name": "ping", "bytes_total_volume": 4738103}, {"application_name": "ldap", "bytes_total_volume": 1618292}, {"application_name": "icmp", "bytes_total_volume": 1111897}, {"application_name": "syslog", "bytes_total_volume": 690028}, {"application_name": "snmp-base", "bytes_total_volume": 618832}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 429139}, {"application_name": "incomplete", "bytes_total_volume": 426228}, {"application_name": "traceroute", "bytes_total_volume": 416166}, {"application_name": "radius", "bytes_total_volume": 363876}, {"application_name": "kerber<PERSON>", "bytes_total_volume": 329443}, {"application_name": "snmp-trap", "bytes_total_volume": 267126}, {"application_name": "tacacs-plus", "bytes_total_volume": 188228}, {"application_name": "dhcp", "bytes_total_volume": 138485}, {"application_name": "google-base", "bytes_total_volume": 102479}, {"application_name": "vmware", "bytes_total_volume": 70012}, {"application_name": "unknown-tcp", "bytes_total_volume": 54942}, {"application_name": "msrpc-base", "bytes_total_volume": 50044}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 103, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "insufficient-data", "bytes_total_volume": 66502187246}, {"application_name": "express-mode", "bytes_total_volume": 38831944754}, {"application_name": "ssl", "bytes_total_volume": 346715683}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 155546311}, {"application_name": "ping", "bytes_total_volume": 107705781}, {"application_name": "traceroute", "bytes_total_volume": 40620308}, {"application_name": "cortex-xdr", "bytes_total_volume": 19240579}, {"application_name": "google-base", "bytes_total_volume": 16660863}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 16419809}, {"application_name": "web-browsing", "bytes_total_volume": 13166803}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 12466308}, {"application_name": "paloalto-updates", "bytes_total_volume": 11827074}, {"application_name": "traps-management-service", "bytes_total_volume": 11068912}, {"application_name": "paloalto-iot-security", "bytes_total_volume": 7210256}, {"application_name": "zoom-base", "bytes_total_volume": 6184765}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 3620471}, {"application_name": "slack-base", "bytes_total_volume": 3078823}, {"application_name": "paloalto-device-telemetry", "bytes_total_volume": 2724850}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2017992}, {"application_name": "ldap", "bytes_total_volume": 1618292}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "other", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "OTHER", "aggregation_type": "last_30_days", "application_count": 100, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "insufficient-data", "bytes_total_volume": 66502187246}, {"application_name": "express-mode", "bytes_total_volume": 38831944754}, {"application_name": "ssl", "bytes_total_volume": 346715683}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 155546311}, {"application_name": "ping", "bytes_total_volume": 107705781}, {"application_name": "traceroute", "bytes_total_volume": 40620308}, {"application_name": "cortex-xdr", "bytes_total_volume": 19240579}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 16419809}, {"application_name": "web-browsing", "bytes_total_volume": 13166803}, {"application_name": "paloalto-prisma-sdwan-control", "bytes_total_volume": 12466308}, {"application_name": "paloalto-updates", "bytes_total_volume": 11827074}, {"application_name": "traps-management-service", "bytes_total_volume": 11068912}, {"application_name": "paloalto-iot-security", "bytes_total_volume": 7210256}, {"application_name": "zoom-base", "bytes_total_volume": 6184765}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 3620471}, {"application_name": "slack-base", "bytes_total_volume": 3078823}, {"application_name": "paloalto-device-telemetry", "bytes_total_volume": 2724850}, {"application_name": "pan-db-cloud", "bytes_total_volume": 2017992}, {"application_name": "ldap", "bytes_total_volume": 1618292}, {"application_name": "incomplete", "bytes_total_volume": 1482838}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "private_apps", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 62, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 10206709893}, {"application_name": "ssh", "bytes_total_volume": 1311766408}, {"application_name": "ms-rdp", "bytes_total_volume": 766310500}, {"application_name": "express-mode", "bytes_total_volume": 493332104}, {"application_name": "google-base", "bytes_total_volume": 164743472}, {"application_name": "web-browsing", "bytes_total_volume": 143476578}, {"application_name": "ldap", "bytes_total_volume": 81590512}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 59975986}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 54692496}, {"application_name": "google-play", "bytes_total_volume": 37507671}, {"application_name": "vmware", "bytes_total_volume": 37177977}, {"application_name": "msrpc-base", "bytes_total_volume": 30382249}, {"application_name": "active-directory-base", "bytes_total_volume": 15139837}, {"application_name": "ping", "bytes_total_volume": 13702108}, {"application_name": "unknown-tcp", "bytes_total_volume": 12834747}, {"application_name": "ms-sms", "bytes_total_volume": 11119593}, {"application_name": "unknown-udp", "bytes_total_volume": 11016910}, {"application_name": "kerber<PERSON>", "bytes_total_volume": 9997344}, {"application_name": "paloalto-prisma-sdwan-base", "bytes_total_volume": 7787983}, {"application_name": "sap", "bytes_total_volume": 6539162}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "private_apps", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 60, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 10206709893}, {"application_name": "ssh", "bytes_total_volume": 1311766408}, {"application_name": "ms-rdp", "bytes_total_volume": 766310500}, {"application_name": "express-mode", "bytes_total_volume": 493332104}, {"application_name": "web-browsing", "bytes_total_volume": 143476578}, {"application_name": "ldap", "bytes_total_volume": 81590512}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 59975986}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 54692496}, {"application_name": "vmware", "bytes_total_volume": 37177977}, {"application_name": "msrpc-base", "bytes_total_volume": 30382249}, {"application_name": "active-directory-base", "bytes_total_volume": 15139837}, {"application_name": "ping", "bytes_total_volume": 13702108}, {"application_name": "unknown-tcp", "bytes_total_volume": 12834747}, {"application_name": "ms-sms", "bytes_total_volume": 11119593}, {"application_name": "unknown-udp", "bytes_total_volume": 11016910}, {"application_name": "kerber<PERSON>", "bytes_total_volume": 9997344}, {"application_name": "paloalto-prisma-sdwan-base", "bytes_total_volume": 7787983}, {"application_name": "sap", "bytes_total_volume": 6539162}, {"application_name": "rtcp", "bytes_total_volume": 4824052}, {"application_name": "ms-netlogon", "bytes_total_volume": 4801104}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "private_apps", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 80, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 32777165224}, {"application_name": "ssh", "bytes_total_volume": 6398717591}, {"application_name": "web-browsing", "bytes_total_volume": 1371915306}, {"application_name": "ms-rdp", "bytes_total_volume": 1262237079}, {"application_name": "vmware", "bytes_total_volume": 1239124103}, {"application_name": "lpd", "bytes_total_volume": 609100722}, {"application_name": "express-mode", "bytes_total_volume": 493332104}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 416595131}, {"application_name": "ldap", "bytes_total_volume": 320965349}, {"application_name": "vnc-base", "bytes_total_volume": 189969544}, {"application_name": "vnc-encrypted", "bytes_total_volume": 175075544}, {"application_name": "msrpc-base", "bytes_total_volume": 134318132}, {"application_name": "sap", "bytes_total_volume": 83738907}, {"application_name": "vmware-view", "bytes_total_volume": 68383970}, {"application_name": "paloalto-prisma-sdwan-base", "bytes_total_volume": 68371644}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 56904597}, {"application_name": "active-directory-base", "bytes_total_volume": 54868560}, {"application_name": "ms-update-optimization-p2p", "bytes_total_volume": 53809475}, {"application_name": "ms-sms", "bytes_total_volume": 39047931}, {"application_name": "kerber<PERSON>", "bytes_total_volume": 35176138}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "private_apps", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 82, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 32777165224}, {"application_name": "ssh", "bytes_total_volume": 6398717591}, {"application_name": "web-browsing", "bytes_total_volume": 1371915306}, {"application_name": "ms-rdp", "bytes_total_volume": 1262237079}, {"application_name": "vmware", "bytes_total_volume": 1239124103}, {"application_name": "lpd", "bytes_total_volume": 609100722}, {"application_name": "express-mode", "bytes_total_volume": 493332104}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 416595131}, {"application_name": "ldap", "bytes_total_volume": 320965349}, {"application_name": "google-base", "bytes_total_volume": 226717183}, {"application_name": "vnc-base", "bytes_total_volume": 189969544}, {"application_name": "vnc-encrypted", "bytes_total_volume": 175075544}, {"application_name": "msrpc-base", "bytes_total_volume": 134318132}, {"application_name": "sap", "bytes_total_volume": 83738907}, {"application_name": "vmware-view", "bytes_total_volume": 68383970}, {"application_name": "paloalto-prisma-sdwan-base", "bytes_total_volume": 68371644}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 56904597}, {"application_name": "active-directory-base", "bytes_total_volume": 54868560}, {"application_name": "ms-update-optimization-p2p", "bytes_total_volume": 53809475}, {"application_name": "ms-sms", "bytes_total_volume": 39047931}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "private_apps", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 76, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 22570455331}, {"application_name": "ssh", "bytes_total_volume": 5086951183}, {"application_name": "web-browsing", "bytes_total_volume": 1228438728}, {"application_name": "vmware", "bytes_total_volume": 1201946126}, {"application_name": "lpd", "bytes_total_volume": 609100722}, {"application_name": "ms-rdp", "bytes_total_volume": 495926579}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 356619145}, {"application_name": "ldap", "bytes_total_volume": 239374837}, {"application_name": "vnc-base", "bytes_total_volume": 189960850}, {"application_name": "vnc-encrypted", "bytes_total_volume": 175075544}, {"application_name": "msrpc-base", "bytes_total_volume": 103935883}, {"application_name": "sap", "bytes_total_volume": 77199745}, {"application_name": "vmware-view", "bytes_total_volume": 68383970}, {"application_name": "google-base", "bytes_total_volume": 61973711}, {"application_name": "paloalto-prisma-sdwan-base", "bytes_total_volume": 60583661}, {"application_name": "ms-update-optimization-p2p", "bytes_total_volume": 53762018}, {"application_name": "active-directory-base", "bytes_total_volume": 39728723}, {"application_name": "zabbix", "bytes_total_volume": 31747653}, {"application_name": "ms-sms", "bytes_total_volume": 27928338}, {"application_name": "kerber<PERSON>", "bytes_total_volume": 25178794}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "private_apps", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 74, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 22570455331}, {"application_name": "ssh", "bytes_total_volume": 5086951183}, {"application_name": "web-browsing", "bytes_total_volume": 1228438728}, {"application_name": "vmware", "bytes_total_volume": 1201946126}, {"application_name": "lpd", "bytes_total_volume": 609100722}, {"application_name": "ms-rdp", "bytes_total_volume": 495926579}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 356619145}, {"application_name": "ldap", "bytes_total_volume": 239374837}, {"application_name": "vnc-base", "bytes_total_volume": 189960850}, {"application_name": "vnc-encrypted", "bytes_total_volume": 175075544}, {"application_name": "msrpc-base", "bytes_total_volume": 103935883}, {"application_name": "sap", "bytes_total_volume": 77199745}, {"application_name": "vmware-view", "bytes_total_volume": 68383970}, {"application_name": "paloalto-prisma-sdwan-base", "bytes_total_volume": 60583661}, {"application_name": "ms-update-optimization-p2p", "bytes_total_volume": 53762018}, {"application_name": "active-directory-base", "bytes_total_volume": 39728723}, {"application_name": "zabbix", "bytes_total_volume": 31747653}, {"application_name": "ms-sms", "bytes_total_volume": 27928338}, {"application_name": "kerber<PERSON>", "bytes_total_volume": 25178794}, {"application_name": "websocket", "bytes_total_volume": 18196207}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 333, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 36939355595}, {"application_name": "ssh", "bytes_total_volume": 5096601630}, {"application_name": "web-browsing", "bytes_total_volume": 4619564644}, {"application_name": "zoom-meeting", "bytes_total_volume": 3469378497}, {"application_name": "google-base", "bytes_total_volume": 3054131711}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 2261535191}, {"application_name": "traps-management-service", "bytes_total_volume": 1337899085}, {"application_name": "vmware", "bytes_total_volume": 1201946126}, {"application_name": "youtube-streaming", "bytes_total_volume": 1029597162}, {"application_name": "ms-update", "bytes_total_volume": 932169004}, {"application_name": "tenable.io", "bytes_total_volume": 879395192}, {"application_name": "google-play", "bytes_total_volume": 786024972}, {"application_name": "slack-base", "bytes_total_volume": 727888297}, {"application_name": "lpd", "bytes_total_volume": 609100722}, {"application_name": "ms-rdp", "bytes_total_volume": 495926579}, {"application_name": "github-downloading", "bytes_total_volume": 465283571}, {"application_name": "google-docs-base", "bytes_total_volume": 364403043}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 356619145}, {"application_name": "http-video", "bytes_total_volume": 355645362}, {"application_name": "gmail-base", "bytes_total_volume": 338933385}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 324, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 36939355595}, {"application_name": "ssh", "bytes_total_volume": 5096601630}, {"application_name": "web-browsing", "bytes_total_volume": 4619564644}, {"application_name": "zoom-meeting", "bytes_total_volume": 3469378497}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 2261535191}, {"application_name": "traps-management-service", "bytes_total_volume": 1337899085}, {"application_name": "vmware", "bytes_total_volume": 1201946126}, {"application_name": "youtube-streaming", "bytes_total_volume": 1029597162}, {"application_name": "ms-update", "bytes_total_volume": 932169004}, {"application_name": "tenable.io", "bytes_total_volume": 879395192}, {"application_name": "slack-base", "bytes_total_volume": 727888297}, {"application_name": "lpd", "bytes_total_volume": 609100722}, {"application_name": "ms-rdp", "bytes_total_volume": 495926579}, {"application_name": "github-downloading", "bytes_total_volume": 465283571}, {"application_name": "google-docs-base", "bytes_total_volume": 364403043}, {"application_name": "ms-ds-smbv3", "bytes_total_volume": 356619145}, {"application_name": "http-video", "bytes_total_volume": 355645362}, {"application_name": "gmail-base", "bytes_total_volume": 338933385}, {"application_name": "ldap", "bytes_total_volume": 239374837}, {"application_name": "jamf", "bytes_total_volume": 231076298}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 446, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 56844705099}, {"application_name": "web-browsing", "bytes_total_volume": 16001621717}, {"application_name": "google-base", "bytes_total_volume": ***********}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 10707903330}, {"application_name": "http-video", "bytes_total_volume": 7768184089}, {"application_name": "ssh", "bytes_total_volume": 6413862940}, {"application_name": "traps-management-service", "bytes_total_volume": 3889789435}, {"application_name": "google-play", "bytes_total_volume": 3640809170}, {"application_name": "zoom-meeting", "bytes_total_volume": 3525691283}, {"application_name": "tenable.io", "bytes_total_volume": 3359321682}, {"application_name": "rtcp", "bytes_total_volume": 3136905000}, {"application_name": "slack-base", "bytes_total_volume": 2749541631}, {"application_name": "ms-update", "bytes_total_volume": 1858107343}, {"application_name": "google-docs-base", "bytes_total_volume": 1680716275}, {"application_name": "gmail-base", "bytes_total_volume": 1483539127}, {"application_name": "ms-rdp", "bytes_total_volume": 1262237933}, {"application_name": "vmware", "bytes_total_volume": 1239124103}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208526551}, {"application_name": "youtube-streaming", "bytes_total_volume": 1036113299}, {"application_name": "lpd", "bytes_total_volume": 609100722}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 377, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 19905349504}, {"application_name": "web-browsing", "bytes_total_volume": 11382057073}, {"application_name": "google-base", "bytes_total_volume": ***********}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 8446368139}, {"application_name": "http-video", "bytes_total_volume": 7412538727}, {"application_name": "rtcp", "bytes_total_volume": 3126469468}, {"application_name": "google-play", "bytes_total_volume": 2854784198}, {"application_name": "traps-management-service", "bytes_total_volume": 2551890350}, {"application_name": "tenable.io", "bytes_total_volume": 2479926490}, {"application_name": "slack-base", "bytes_total_volume": 2021653334}, {"application_name": "ssh", "bytes_total_volume": 1317261310}, {"application_name": "google-docs-base", "bytes_total_volume": 1316313232}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208463657}, {"application_name": "gmail-base", "bytes_total_volume": 1144605742}, {"application_name": "ms-update", "bytes_total_volume": 925938339}, {"application_name": "ms-rdp", "bytes_total_volume": 766311354}, {"application_name": "express-mode", "bytes_total_volume": 493332104}, {"application_name": "google-drive-web-base", "bytes_total_volume": 490418310}, {"application_name": "linkedin-base", "bytes_total_volume": 487308136}, {"application_name": "vidyo", "bytes_total_volume": 400736999}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "internet", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 197, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 24067539875}, {"application_name": "web-browsing", "bytes_total_volume": 14629706411}, {"application_name": "google-base", "bytes_total_volume": 13520562345}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 10650998733}, {"application_name": "http-video", "bytes_total_volume": 7766845260}, {"application_name": "traps-management-service", "bytes_total_volume": 3889789435}, {"application_name": "google-play", "bytes_total_volume": 3603271793}, {"application_name": "rtcp", "bytes_total_volume": 3121944727}, {"application_name": "ms-update", "bytes_total_volume": 1857552479}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208526551}, {"application_name": "linkedin-base", "bytes_total_volume": 576719435}, {"application_name": "twitter-base", "bytes_total_volume": 426895339}, {"application_name": "websocket", "bytes_total_volume": 412427173}, {"application_name": "apple-update", "bytes_total_volume": 318244088}, {"application_name": "wechat-file-transfer", "bytes_total_volume": 272242593}, {"application_name": "ping", "bytes_total_volume": 215925494}, {"application_name": "quic-base", "bytes_total_volume": 205641632}, {"application_name": "unknown-udp", "bytes_total_volume": 169719605}, {"application_name": "traceroute", "bytes_total_volume": 161383156}, {"application_name": "cortex-xdr", "bytes_total_volume": 157358385}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "internet", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 143, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 14368900264}, {"application_name": "web-browsing", "bytes_total_volume": 3391125916}, {"application_name": "google-base", "bytes_total_volume": 2992158000}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 2259323090}, {"application_name": "traps-management-service", "bytes_total_volume": 1337899085}, {"application_name": "ms-update", "bytes_total_volume": 931871346}, {"application_name": "google-play", "bytes_total_volume": 785995266}, {"application_name": "http-video", "bytes_total_volume": 354306533}, {"application_name": "apple-update", "bytes_total_volume": 135305123}, {"application_name": "stun", "bytes_total_volume": 108897844}, {"application_name": "linkedin-base", "bytes_total_volume": 89411299}, {"application_name": "twitter-base", "bytes_total_volume": 76890433}, {"application_name": "quic-base", "bytes_total_volume": 71436744}, {"application_name": "imap", "bytes_total_volume": 44453816}, {"application_name": "apple-maps", "bytes_total_volume": 39614886}, {"application_name": "websocket", "bytes_total_volume": 38797187}, {"application_name": "soap", "bytes_total_volume": 35779616}, {"application_name": "traceroute", "bytes_total_volume": 31786854}, {"application_name": "whatsapp-file-transfer", "bytes_total_volume": 29497539}, {"application_name": "incomplete", "bytes_total_volume": 25472315}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 434, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 56844705099}, {"application_name": "web-browsing", "bytes_total_volume": 16001621717}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 10707903330}, {"application_name": "http-video", "bytes_total_volume": 7768184089}, {"application_name": "ssh", "bytes_total_volume": 6413862940}, {"application_name": "traps-management-service", "bytes_total_volume": 3889789435}, {"application_name": "zoom-meeting", "bytes_total_volume": 3525691283}, {"application_name": "tenable.io", "bytes_total_volume": 3359321682}, {"application_name": "rtcp", "bytes_total_volume": 3136905000}, {"application_name": "slack-base", "bytes_total_volume": 2749541631}, {"application_name": "ms-update", "bytes_total_volume": 1858107343}, {"application_name": "google-docs-base", "bytes_total_volume": 1680716275}, {"application_name": "gmail-base", "bytes_total_volume": 1483539127}, {"application_name": "ms-rdp", "bytes_total_volume": 1262237933}, {"application_name": "vmware", "bytes_total_volume": 1239124103}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208526551}, {"application_name": "youtube-streaming", "bytes_total_volume": 1036113299}, {"application_name": "lpd", "bytes_total_volume": 609100722}, {"application_name": "google-drive-web-base", "bytes_total_volume": 578372707}, {"application_name": "linkedin-base", "bytes_total_volume": 576719435}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 366, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 19905349504}, {"application_name": "web-browsing", "bytes_total_volume": 11382057073}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 8446368139}, {"application_name": "http-video", "bytes_total_volume": 7412538727}, {"application_name": "rtcp", "bytes_total_volume": 3126469468}, {"application_name": "traps-management-service", "bytes_total_volume": 2551890350}, {"application_name": "tenable.io", "bytes_total_volume": 2479926490}, {"application_name": "slack-base", "bytes_total_volume": 2021653334}, {"application_name": "ssh", "bytes_total_volume": 1317261310}, {"application_name": "google-docs-base", "bytes_total_volume": 1316313232}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208463657}, {"application_name": "gmail-base", "bytes_total_volume": 1144605742}, {"application_name": "ms-update", "bytes_total_volume": 925938339}, {"application_name": "ms-rdp", "bytes_total_volume": 766311354}, {"application_name": "express-mode", "bytes_total_volume": 493332104}, {"application_name": "google-drive-web-base", "bytes_total_volume": 490418310}, {"application_name": "linkedin-base", "bytes_total_volume": 487308136}, {"application_name": "vidyo", "bytes_total_volume": 400736999}, {"application_name": "websocket", "bytes_total_volume": 374226470}, {"application_name": "twitter-base", "bytes_total_volume": 350004906}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "internet", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 195, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 24067539875}, {"application_name": "web-browsing", "bytes_total_volume": 14629706411}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 10650998733}, {"application_name": "http-video", "bytes_total_volume": 7766845260}, {"application_name": "traps-management-service", "bytes_total_volume": 3889789435}, {"application_name": "rtcp", "bytes_total_volume": 3121944727}, {"application_name": "ms-update", "bytes_total_volume": 1857552479}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208526551}, {"application_name": "linkedin-base", "bytes_total_volume": 576719435}, {"application_name": "twitter-base", "bytes_total_volume": 426895339}, {"application_name": "websocket", "bytes_total_volume": 412427173}, {"application_name": "apple-update", "bytes_total_volume": 318244088}, {"application_name": "wechat-file-transfer", "bytes_total_volume": 272242593}, {"application_name": "ping", "bytes_total_volume": 215925494}, {"application_name": "quic-base", "bytes_total_volume": 205641632}, {"application_name": "unknown-udp", "bytes_total_volume": 169719605}, {"application_name": "traceroute", "bytes_total_volume": 161383156}, {"application_name": "cortex-xdr", "bytes_total_volume": 157358385}, {"application_name": "itunes-base", "bytes_total_volume": 149904624}, {"application_name": "incomplete", "bytes_total_volume": 142814768}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "internet", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 141, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 14368900264}, {"application_name": "web-browsing", "bytes_total_volume": 3391125916}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 2259323090}, {"application_name": "traps-management-service", "bytes_total_volume": 1337899085}, {"application_name": "ms-update", "bytes_total_volume": 931871346}, {"application_name": "http-video", "bytes_total_volume": 354306533}, {"application_name": "apple-update", "bytes_total_volume": 135305123}, {"application_name": "stun", "bytes_total_volume": 108897844}, {"application_name": "linkedin-base", "bytes_total_volume": 89411299}, {"application_name": "twitter-base", "bytes_total_volume": 76890433}, {"application_name": "quic-base", "bytes_total_volume": 71436744}, {"application_name": "imap", "bytes_total_volume": 44453816}, {"application_name": "apple-maps", "bytes_total_volume": 39614886}, {"application_name": "websocket", "bytes_total_volume": 38797187}, {"application_name": "soap", "bytes_total_volume": 35779616}, {"application_name": "traceroute", "bytes_total_volume": 31786854}, {"application_name": "whatsapp-file-transfer", "bytes_total_volume": 29497539}, {"application_name": "incomplete", "bytes_total_volume": 25472315}, {"application_name": "amazon-aws-console", "bytes_total_volume": 25286881}, {"application_name": "ping", "bytes_total_volume": 24136632}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "yes", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 115061427}, {"application_name": "notion-base", "bytes_total_volume": 17109961}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5551400}, {"application_name": "tabnine", "bytes_total_volume": 495719}, {"application_name": "bing-ai-base", "bytes_total_volume": 53665}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 115061427}, {"application_name": "notion-base", "bytes_total_volume": 17109961}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5551400}, {"application_name": "tabnine", "bytes_total_volume": 495719}, {"application_name": "bing-ai-base", "bytes_total_volume": 53665}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "yes", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 115061427}, {"application_name": "notion-base", "bytes_total_volume": 17109961}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5551400}, {"application_name": "tabnine", "bytes_total_volume": 495719}, {"application_name": "bing-ai-base", "bytes_total_volume": 53665}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 134548103}, {"application_name": "notion-base", "bytes_total_volume": 39201584}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5572244}, {"application_name": "tabnine", "bytes_total_volume": 1112484}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "bing-ai-base", "bytes_total_volume": 53665}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "yes", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 134548103}, {"application_name": "notion-base", "bytes_total_volume": 39201584}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5572244}, {"application_name": "tabnine", "bytes_total_volume": 1112484}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "bing-ai-base", "bytes_total_volume": 53665}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "yes", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 134548103}, {"application_name": "notion-base", "bytes_total_volume": 39201584}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5572244}, {"application_name": "tabnine", "bytes_total_volume": 1112484}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "bing-ai-base", "bytes_total_volume": 53665}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "all", "platform_type": "all", "is_genai": "yes", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 36, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 134548103}, {"application_name": "openai-chatgpt", "bytes_total_volume": 51272444}, {"application_name": "notion-base", "bytes_total_volume": 39201584}, {"application_name": "deepl-base", "bytes_total_volume": 5607063}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5572244}, {"application_name": "openai-base", "bytes_total_volume": 5430006}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 4484712}, {"application_name": "github-copilot-business", "bytes_total_volume": 4343713}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "google-gemini", "bytes_total_volume": 2114509}, {"application_name": "adobe-express-base", "bytes_total_volume": 1115260}, {"application_name": "tabnine", "bytes_total_volume": 1112484}, {"application_name": "claude-base", "bytes_total_volume": 966654}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 882168}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 628701}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 604532}, {"application_name": "github-copilot", "bytes_total_volume": 476979}, {"application_name": "huggingface-base", "bytes_total_volume": 405918}, {"application_name": "deepl-write", "bytes_total_volume": 359689}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "all", "platform_type": "all", "is_genai": "yes", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 36, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 134548103}, {"application_name": "openai-chatgpt", "bytes_total_volume": 51272444}, {"application_name": "notion-base", "bytes_total_volume": 39201584}, {"application_name": "deepl-base", "bytes_total_volume": 5607063}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5572244}, {"application_name": "openai-base", "bytes_total_volume": 5430006}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 4484712}, {"application_name": "github-copilot-business", "bytes_total_volume": 4343713}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "google-gemini", "bytes_total_volume": 2114509}, {"application_name": "adobe-express-base", "bytes_total_volume": 1115260}, {"application_name": "tabnine", "bytes_total_volume": 1112484}, {"application_name": "claude-base", "bytes_total_volume": 966654}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 882168}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 628701}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 604532}, {"application_name": "github-copilot", "bytes_total_volume": 476979}, {"application_name": "huggingface-base", "bytes_total_volume": 405918}, {"application_name": "deepl-write", "bytes_total_volume": 359689}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "yes", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 32, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 115061427}, {"application_name": "openai-chatgpt", "bytes_total_volume": 39288940}, {"application_name": "notion-base", "bytes_total_volume": 17109961}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5551400}, {"application_name": "deepl-base", "bytes_total_volume": 5412573}, {"application_name": "openai-base", "bytes_total_volume": 5037921}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 3897763}, {"application_name": "github-copilot-business", "bytes_total_volume": 3806173}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "adobe-express-base", "bytes_total_volume": 965482}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 604532}, {"application_name": "tabnine", "bytes_total_volume": 495719}, {"application_name": "google-gemini", "bytes_total_volume": 425584}, {"application_name": "huggingface-base", "bytes_total_volume": 405918}, {"application_name": "deepl-write", "bytes_total_volume": 344615}, {"application_name": "claude-base", "bytes_total_volume": 343270}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 297367}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "yes", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 32, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "grammarly", "bytes_total_volume": 115061427}, {"application_name": "openai-chatgpt", "bytes_total_volume": 39288940}, {"application_name": "notion-base", "bytes_total_volume": 17109961}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5551400}, {"application_name": "deepl-base", "bytes_total_volume": 5412573}, {"application_name": "openai-base", "bytes_total_volume": 5037921}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 3897763}, {"application_name": "github-copilot-business", "bytes_total_volume": 3806173}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "adobe-express-base", "bytes_total_volume": 965482}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 604532}, {"application_name": "tabnine", "bytes_total_volume": 495719}, {"application_name": "google-gemini", "bytes_total_volume": 425584}, {"application_name": "huggingface-base", "bytes_total_volume": 405918}, {"application_name": "deepl-write", "bytes_total_volume": 344615}, {"application_name": "claude-base", "bytes_total_volume": 343270}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 297367}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "yes", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 26, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "deepl-base", "bytes_total_volume": 5607063}, {"application_name": "openai-base", "bytes_total_volume": 5430006}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 4484712}, {"application_name": "github-copilot-business", "bytes_total_volume": 4343713}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "google-gemini", "bytes_total_volume": 2114509}, {"application_name": "adobe-express-base", "bytes_total_volume": 1115260}, {"application_name": "claude-base", "bytes_total_volume": 966654}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 882168}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 604532}, {"application_name": "github-copilot", "bytes_total_volume": 476979}, {"application_name": "huggingface-base", "bytes_total_volume": 405918}, {"application_name": "deepl-write", "bytes_total_volume": 359689}, {"application_name": "ms-copilot-studio", "bytes_total_volume": 164406}, {"application_name": "openai-chatgpt-download", "bytes_total_volume": 137514}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 102901}, {"application_name": "claude-upload", "bytes_total_volume": 79328}, {"application_name": "github-copilot-chat", "bytes_total_volume": 73764}, {"application_name": "synthesia-base", "bytes_total_volume": 30207}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "yes", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 26, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "deepl-base", "bytes_total_volume": 5607063}, {"application_name": "openai-base", "bytes_total_volume": 5430006}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 4484712}, {"application_name": "github-copilot-business", "bytes_total_volume": 4343713}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "google-gemini", "bytes_total_volume": 2114509}, {"application_name": "adobe-express-base", "bytes_total_volume": 1115260}, {"application_name": "claude-base", "bytes_total_volume": 966654}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 882168}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 604532}, {"application_name": "github-copilot", "bytes_total_volume": 476979}, {"application_name": "huggingface-base", "bytes_total_volume": 405918}, {"application_name": "deepl-write", "bytes_total_volume": 359689}, {"application_name": "ms-copilot-studio", "bytes_total_volume": 164406}, {"application_name": "openai-chatgpt-download", "bytes_total_volume": 137514}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 102901}, {"application_name": "claude-upload", "bytes_total_volume": 79328}, {"application_name": "github-copilot-chat", "bytes_total_volume": 73764}, {"application_name": "synthesia-base", "bytes_total_volume": 30207}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "yes", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 23, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "deepl-base", "bytes_total_volume": 5412573}, {"application_name": "openai-base", "bytes_total_volume": 5037921}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 3897763}, {"application_name": "github-copilot-business", "bytes_total_volume": 3806173}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "adobe-express-base", "bytes_total_volume": 965482}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 604532}, {"application_name": "google-gemini", "bytes_total_volume": 425584}, {"application_name": "huggingface-base", "bytes_total_volume": 405918}, {"application_name": "deepl-write", "bytes_total_volume": 344615}, {"application_name": "claude-base", "bytes_total_volume": 343270}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 219877}, {"application_name": "ms-copilot-studio", "bytes_total_volume": 164406}, {"application_name": "openai-chatgpt-download", "bytes_total_volume": 137514}, {"application_name": "claude-upload", "bytes_total_volume": 79328}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 58670}, {"application_name": "github-copilot", "bytes_total_volume": 58162}, {"application_name": "github-copilot-chat", "bytes_total_volume": 49211}, {"application_name": "synthesia-base", "bytes_total_volume": 30207}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "yes", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 23, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "deepl-base", "bytes_total_volume": 5412573}, {"application_name": "openai-base", "bytes_total_volume": 5037921}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 3897763}, {"application_name": "github-copilot-business", "bytes_total_volume": 3806173}, {"application_name": "adobe-firefly-upload", "bytes_total_volume": 2295142}, {"application_name": "adobe-express-base", "bytes_total_volume": 965482}, {"application_name": "otter.ai-base", "bytes_total_volume": 819853}, {"application_name": "azure-openai-encrypted", "bytes_total_volume": 604532}, {"application_name": "google-gemini", "bytes_total_volume": 425584}, {"application_name": "huggingface-base", "bytes_total_volume": 405918}, {"application_name": "deepl-write", "bytes_total_volume": 344615}, {"application_name": "claude-base", "bytes_total_volume": 343270}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 219877}, {"application_name": "ms-copilot-studio", "bytes_total_volume": 164406}, {"application_name": "openai-chatgpt-download", "bytes_total_volume": 137514}, {"application_name": "claude-upload", "bytes_total_volume": 79328}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 58670}, {"application_name": "github-copilot", "bytes_total_volume": 58162}, {"application_name": "github-copilot-chat", "bytes_total_volume": 49211}, {"application_name": "synthesia-base", "bytes_total_volume": 30207}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 187, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tenable.io", "bytes_total_volume": 2479926490}, {"application_name": "slack-base", "bytes_total_volume": 2021653334}, {"application_name": "google-docs-base", "bytes_total_volume": 1316313232}, {"application_name": "gmail-base", "bytes_total_volume": 1144605742}, {"application_name": "google-drive-web-base", "bytes_total_volume": 490418310}, {"application_name": "vidyo", "bytes_total_volume": 400736999}, {"application_name": "okta", "bytes_total_volume": 274617974}, {"application_name": "youtube-base", "bytes_total_volume": 261728353}, {"application_name": "outlook-web-online", "bytes_total_volume": 210006848}, {"application_name": "ms-teams-audio-video", "bytes_total_volume": 200844148}, {"application_name": "icloud-base", "bytes_total_volume": 189256725}, {"application_name": "slack-uploading", "bytes_total_volume": 170171490}, {"application_name": "facebook-base", "bytes_total_volume": 167747810}, {"application_name": "github-base", "bytes_total_volume": 166859524}, {"application_name": "figma-base", "bytes_total_volume": 159890819}, {"application_name": "ms-teams", "bytes_total_volume": 149239434}, {"application_name": "slack-downloading", "bytes_total_volume": 140657125}, {"application_name": "google-chat-base", "bytes_total_volume": 136383875}, {"application_name": "facebook-video", "bytes_total_volume": 130906430}, {"application_name": "ms-onedrive-downloading", "bytes_total_volume": 127241182}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 178, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "tenable.io", "bytes_total_volume": 2479926490}, {"application_name": "slack-base", "bytes_total_volume": 2021653334}, {"application_name": "google-docs-base", "bytes_total_volume": 1316313232}, {"application_name": "gmail-base", "bytes_total_volume": 1144605742}, {"application_name": "google-drive-web-base", "bytes_total_volume": 490418310}, {"application_name": "vidyo", "bytes_total_volume": 400736999}, {"application_name": "okta", "bytes_total_volume": 274617974}, {"application_name": "youtube-base", "bytes_total_volume": 261728353}, {"application_name": "outlook-web-online", "bytes_total_volume": 210006848}, {"application_name": "ms-teams-audio-video", "bytes_total_volume": 200844148}, {"application_name": "icloud-base", "bytes_total_volume": 189256725}, {"application_name": "slack-uploading", "bytes_total_volume": 170171490}, {"application_name": "facebook-base", "bytes_total_volume": 167747810}, {"application_name": "github-base", "bytes_total_volume": 166859524}, {"application_name": "figma-base", "bytes_total_volume": 159890819}, {"application_name": "ms-teams", "bytes_total_volume": 149239434}, {"application_name": "slack-downloading", "bytes_total_volume": 140657125}, {"application_name": "google-chat-base", "bytes_total_volume": 136383875}, {"application_name": "facebook-video", "bytes_total_volume": 130906430}, {"application_name": "ms-onedrive-downloading", "bytes_total_volume": 127241182}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "internet", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 10528404345}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "internet", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 13520562345}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "internet", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 2992158000}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "private_apps", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 164743472}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "private_apps", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 226717183}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "private_apps", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 61973711}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": ***********}, {"application_name": "grammarly", "bytes_total_volume": 115061427}, {"application_name": "notion-base", "bytes_total_volume": 17109961}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5551400}, {"application_name": "tabnine", "bytes_total_volume": 495719}, {"application_name": "bing-ai-base", "bytes_total_volume": 53665}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 7, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": ***********}, {"application_name": "grammarly", "bytes_total_volume": 134548103}, {"application_name": "notion-base", "bytes_total_volume": 39201584}, {"application_name": "azure-openai-studio", "bytes_total_volume": 5572244}, {"application_name": "tabnine", "bytes_total_volume": 1112484}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "bing-ai-base", "bytes_total_volume": 53665}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 6, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 3054131711}, {"application_name": "notion-base", "bytes_total_volume": 22091623}, {"application_name": "grammarly", "bytes_total_volume": 19486676}, {"application_name": "tabnine", "bytes_total_volume": 616765}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "azure-openai-studio", "bytes_total_volume": 20844}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "internet", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 2817276527}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "private_apps", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 37507671}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "private_apps", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 29706}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "internet", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 3603271793}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "internet", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 785995266}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "private_apps", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 37537377}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 3, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 786024972}, {"application_name": "openai-chatgpt", "bytes_total_volume": 11983504}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 331334}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 2854784198}, {"application_name": "openai-chatgpt", "bytes_total_volume": 39288940}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 297367}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 3640809170}, {"application_name": "openai-chatgpt", "bytes_total_volume": 51272444}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 628701}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "notion-base", "bytes_total_volume": 22091623}, {"application_name": "grammarly", "bytes_total_volume": 19486676}, {"application_name": "tabnine", "bytes_total_volume": 616765}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "azure-openai-studio", "bytes_total_volume": 20844}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "notion-base", "bytes_total_volume": 22091623}, {"application_name": "grammarly", "bytes_total_volume": 19486676}, {"application_name": "tabnine", "bytes_total_volume": 616765}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "azure-openai-studio", "bytes_total_volume": 20844}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 5, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "notion-base", "bytes_total_volume": 22091623}, {"application_name": "grammarly", "bytes_total_volume": 19486676}, {"application_name": "tabnine", "bytes_total_volume": 616765}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "azure-openai-studio", "bytes_total_volume": 20844}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 24, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "notion-base", "bytes_total_volume": 22091623}, {"application_name": "grammarly", "bytes_total_volume": 19486676}, {"application_name": "openai-chatgpt", "bytes_total_volume": 11983504}, {"application_name": "google-gemini", "bytes_total_volume": 1688925}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 662291}, {"application_name": "claude-base", "bytes_total_volume": 623384}, {"application_name": "tabnine", "bytes_total_volume": 616765}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 586949}, {"application_name": "github-copilot-business", "bytes_total_volume": 537540}, {"application_name": "github-copilot", "bytes_total_volume": 418817}, {"application_name": "openai-base", "bytes_total_volume": 392085}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 331334}, {"application_name": "deepl-base", "bytes_total_volume": 194490}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "adobe-express-base", "bytes_total_volume": 149778}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 44231}, {"application_name": "github-copilot-chat", "bytes_total_volume": 24553}, {"application_name": "azure-openai-studio", "bytes_total_volume": 20844}, {"application_name": "deepl-write", "bytes_total_volume": 15074}, {"application_name": "krisp-base", "bytes_total_volume": 4780}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 24, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "notion-base", "bytes_total_volume": 22091623}, {"application_name": "grammarly", "bytes_total_volume": 19486676}, {"application_name": "openai-chatgpt", "bytes_total_volume": 11983504}, {"application_name": "google-gemini", "bytes_total_volume": 1688925}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 662291}, {"application_name": "claude-base", "bytes_total_volume": 623384}, {"application_name": "tabnine", "bytes_total_volume": 616765}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 586949}, {"application_name": "github-copilot-business", "bytes_total_volume": 537540}, {"application_name": "github-copilot", "bytes_total_volume": 418817}, {"application_name": "openai-base", "bytes_total_volume": 392085}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 331334}, {"application_name": "deepl-base", "bytes_total_volume": 194490}, {"application_name": "notion-delete", "bytes_total_volume": 191611}, {"application_name": "adobe-express-base", "bytes_total_volume": 149778}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 44231}, {"application_name": "github-copilot-chat", "bytes_total_volume": 24553}, {"application_name": "azure-openai-studio", "bytes_total_volume": 20844}, {"application_name": "deepl-write", "bytes_total_volume": 15074}, {"application_name": "krisp-base", "bytes_total_volume": 4780}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "internet", "application_sub_type": "unsanctioned", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 163, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "web-browsing", "bytes_total_volume": 11238580495}, {"application_name": "ssl", "bytes_total_volume": 9698639611}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 8391675643}, {"application_name": "http-video", "bytes_total_volume": 7412538727}, {"application_name": "rtcp", "bytes_total_volume": 3121645416}, {"application_name": "traps-management-service", "bytes_total_volume": 2551890350}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208463657}, {"application_name": "ms-update", "bytes_total_volume": 925681133}, {"application_name": "linkedin-base", "bytes_total_volume": 487308136}, {"application_name": "websocket", "bytes_total_volume": 373629986}, {"application_name": "twitter-base", "bytes_total_volume": 350004906}, {"application_name": "wechat-file-transfer", "bytes_total_volume": 272208634}, {"application_name": "ping", "bytes_total_volume": 191788862}, {"application_name": "apple-update", "bytes_total_volume": 182938965}, {"application_name": "unknown-udp", "bytes_total_volume": 169520506}, {"application_name": "cortex-xdr", "bytes_total_volume": 137981018}, {"application_name": "itunes-base", "bytes_total_volume": 137866648}, {"application_name": "quic-base", "bytes_total_volume": 134204888}, {"application_name": "http-audio", "bytes_total_volume": 133722553}, {"application_name": "traceroute", "bytes_total_volume": 129596302}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "internet", "application_sub_type": "all", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 165, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "web-browsing", "bytes_total_volume": 11238580495}, {"application_name": "google-base", "bytes_total_volume": 10528404345}, {"application_name": "ssl", "bytes_total_volume": 9698639611}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 8391675643}, {"application_name": "http-video", "bytes_total_volume": 7412538727}, {"application_name": "rtcp", "bytes_total_volume": 3121645416}, {"application_name": "google-play", "bytes_total_volume": 2817276527}, {"application_name": "traps-management-service", "bytes_total_volume": 2551890350}, {"application_name": "cisco-spark-audio-video", "bytes_total_volume": 1208463657}, {"application_name": "ms-update", "bytes_total_volume": 925681133}, {"application_name": "linkedin-base", "bytes_total_volume": 487308136}, {"application_name": "websocket", "bytes_total_volume": 373629986}, {"application_name": "twitter-base", "bytes_total_volume": 350004906}, {"application_name": "wechat-file-transfer", "bytes_total_volume": 272208634}, {"application_name": "ping", "bytes_total_volume": 191788862}, {"application_name": "apple-update", "bytes_total_volume": 182938965}, {"application_name": "unknown-udp", "bytes_total_volume": 169520506}, {"application_name": "cortex-xdr", "bytes_total_volume": 137981018}, {"application_name": "itunes-base", "bytes_total_volume": 137866648}, {"application_name": "quic-base", "bytes_total_volume": 134204888}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 199, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-meeting", "bytes_total_volume": 3525691283}, {"application_name": "tenable.io", "bytes_total_volume": 3359321682}, {"application_name": "slack-base", "bytes_total_volume": 2749541631}, {"application_name": "google-docs-base", "bytes_total_volume": 1680716275}, {"application_name": "gmail-base", "bytes_total_volume": 1483539127}, {"application_name": "youtube-streaming", "bytes_total_volume": 1036113299}, {"application_name": "google-drive-web-base", "bytes_total_volume": 578372707}, {"application_name": "github-downloading", "bytes_total_volume": 476618614}, {"application_name": "vidyo", "bytes_total_volume": 400736999}, {"application_name": "youtube-base", "bytes_total_volume": 347277026}, {"application_name": "okta", "bytes_total_volume": 327108612}, {"application_name": "figma-base", "bytes_total_volume": 276618455}, {"application_name": "icloud-base", "bytes_total_volume": 252610112}, {"application_name": "jamf", "bytes_total_volume": 231151697}, {"application_name": "facebook-base", "bytes_total_volume": 215016042}, {"application_name": "outlook-web-online", "bytes_total_volume": 211455412}, {"application_name": "ms-teams-audio-video", "bytes_total_volume": 200975226}, {"application_name": "github-base", "bytes_total_volume": 186305812}, {"application_name": "facebook-video", "bytes_total_volume": 182134474}, {"application_name": "slack-downloading", "bytes_total_volume": 175268834}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 209, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-meeting", "bytes_total_volume": 3525691283}, {"application_name": "tenable.io", "bytes_total_volume": 3359321682}, {"application_name": "slack-base", "bytes_total_volume": 2749541631}, {"application_name": "google-docs-base", "bytes_total_volume": 1680716275}, {"application_name": "gmail-base", "bytes_total_volume": 1483539127}, {"application_name": "youtube-streaming", "bytes_total_volume": 1036113299}, {"application_name": "google-drive-web-base", "bytes_total_volume": 578372707}, {"application_name": "github-downloading", "bytes_total_volume": 476618614}, {"application_name": "vidyo", "bytes_total_volume": 400736999}, {"application_name": "youtube-base", "bytes_total_volume": 347277026}, {"application_name": "okta", "bytes_total_volume": 327108612}, {"application_name": "figma-base", "bytes_total_volume": 276618455}, {"application_name": "icloud-base", "bytes_total_volume": 252610112}, {"application_name": "jamf", "bytes_total_volume": 231151697}, {"application_name": "facebook-base", "bytes_total_volume": 215016042}, {"application_name": "outlook-web-online", "bytes_total_volume": 211455412}, {"application_name": "ms-teams-audio-video", "bytes_total_volume": 200975226}, {"application_name": "github-base", "bytes_total_volume": 186305812}, {"application_name": "facebook-video", "bytes_total_volume": 182134474}, {"application_name": "slack-downloading", "bytes_total_volume": 175268834}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 138, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-meeting", "bytes_total_volume": 3469378497}, {"application_name": "youtube-streaming", "bytes_total_volume": 1029597162}, {"application_name": "tenable.io", "bytes_total_volume": 879395192}, {"application_name": "slack-base", "bytes_total_volume": 727888297}, {"application_name": "github-downloading", "bytes_total_volume": 465283571}, {"application_name": "google-docs-base", "bytes_total_volume": 364403043}, {"application_name": "gmail-base", "bytes_total_volume": 338933385}, {"application_name": "jamf", "bytes_total_volume": 231076298}, {"application_name": "vimeo-base", "bytes_total_volume": 164374854}, {"application_name": "figma-base", "bytes_total_volume": 116727636}, {"application_name": "zoom-base", "bytes_total_volume": 104900697}, {"application_name": "google-drive-web-base", "bytes_total_volume": 87954397}, {"application_name": "youtube-base", "bytes_total_volume": 85548673}, {"application_name": "icloud-base", "bytes_total_volume": 63353387}, {"application_name": "adobe-creative-cloud-base", "bytes_total_volume": 57671004}, {"application_name": "okta", "bytes_total_volume": 52490638}, {"application_name": "facebook-video", "bytes_total_volume": 51228044}, {"application_name": "facebook-base", "bytes_total_volume": 47268232}, {"application_name": "salesforce-base", "bytes_total_volume": 41459413}, {"application_name": "new-relic", "bytes_total_volume": 36550846}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 145, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-meeting", "bytes_total_volume": 3469378497}, {"application_name": "youtube-streaming", "bytes_total_volume": 1029597162}, {"application_name": "tenable.io", "bytes_total_volume": 879395192}, {"application_name": "slack-base", "bytes_total_volume": 727888297}, {"application_name": "github-downloading", "bytes_total_volume": 465283571}, {"application_name": "google-docs-base", "bytes_total_volume": 364403043}, {"application_name": "gmail-base", "bytes_total_volume": 338933385}, {"application_name": "jamf", "bytes_total_volume": 231076298}, {"application_name": "vimeo-base", "bytes_total_volume": 164374854}, {"application_name": "figma-base", "bytes_total_volume": 116727636}, {"application_name": "zoom-base", "bytes_total_volume": 104900697}, {"application_name": "google-drive-web-base", "bytes_total_volume": 87954397}, {"application_name": "youtube-base", "bytes_total_volume": 85548673}, {"application_name": "icloud-base", "bytes_total_volume": 63353387}, {"application_name": "adobe-creative-cloud-base", "bytes_total_volume": 57671004}, {"application_name": "okta", "bytes_total_volume": 52490638}, {"application_name": "facebook-video", "bytes_total_volume": 51228044}, {"application_name": "facebook-base", "bytes_total_volume": 47268232}, {"application_name": "salesforce-base", "bytes_total_volume": 41459413}, {"application_name": "new-relic", "bytes_total_volume": 36550846}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 17, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-gemini", "bytes_total_volume": 1688925}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 662291}, {"application_name": "claude-base", "bytes_total_volume": 623384}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 586949}, {"application_name": "github-copilot-business", "bytes_total_volume": 537540}, {"application_name": "github-copilot", "bytes_total_volume": 418817}, {"application_name": "openai-base", "bytes_total_volume": 392085}, {"application_name": "deepl-base", "bytes_total_volume": 194490}, {"application_name": "adobe-express-base", "bytes_total_volume": 149778}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 44231}, {"application_name": "github-copilot-chat", "bytes_total_volume": 24553}, {"application_name": "deepl-write", "bytes_total_volume": 15074}, {"application_name": "krisp-base", "bytes_total_volume": 4780}, {"application_name": "murf-base", "bytes_total_volume": 4470}, {"application_name": "pinecone-base", "bytes_total_volume": 2125}, {"application_name": "runway-app-base", "bytes_total_volume": 1666}, {"application_name": "amazon-codewhisperer", "bytes_total_volume": 1399}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 17, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-gemini", "bytes_total_volume": 1688925}, {"application_name": "amazon-sagemaker-base", "bytes_total_volume": 662291}, {"application_name": "claude-base", "bytes_total_volume": 623384}, {"application_name": "perplexity-ai-base", "bytes_total_volume": 586949}, {"application_name": "github-copilot-business", "bytes_total_volume": 537540}, {"application_name": "github-copilot", "bytes_total_volume": 418817}, {"application_name": "openai-base", "bytes_total_volume": 392085}, {"application_name": "deepl-base", "bytes_total_volume": 194490}, {"application_name": "adobe-express-base", "bytes_total_volume": 149778}, {"application_name": "sourcegraph-cody", "bytes_total_volume": 44231}, {"application_name": "github-copilot-chat", "bytes_total_volume": 24553}, {"application_name": "deepl-write", "bytes_total_volume": 15074}, {"application_name": "krisp-base", "bytes_total_volume": 4780}, {"application_name": "murf-base", "bytes_total_volume": 4470}, {"application_name": "pinecone-base", "bytes_total_volume": 2125}, {"application_name": "runway-app-base", "bytes_total_volume": 1666}, {"application_name": "amazon-codewhisperer", "bytes_total_volume": 1399}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 2, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 11983504}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 331334}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "yes", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 2, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 11983504}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 331334}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 2, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 11983504}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 331334}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "yes", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 51272444}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 628701}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 51272444}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 628701}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "yes", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 51272444}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 628701}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "yes", "source_type_label": "USERS", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 39288940}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 297367}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "yes", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 39288940}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 297367}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "user", "application_type": "saas", "application_sub_type": "tolerated", "platform_type": "prisma_access", "is_genai": "all", "source_type_label": "USERS", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 4, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "openai-chatgpt", "bytes_total_volume": 39288940}, {"application_name": "reclaim.ai-base", "bytes_total_volume": 297367}, {"application_name": "deepl-translator", "bytes_total_volume": 274579}, {"application_name": "codeium", "bytes_total_volume": 248939}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "private_apps", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 95, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 4527762052}, {"application_name": "paloalto-userid-agent", "bytes_total_volume": 2360390272}, {"application_name": "mysql", "bytes_total_volume": 418364038}, {"application_name": "web-browsing", "bytes_total_volume": 249984844}, {"application_name": "snmpv3", "bytes_total_volume": 191578991}, {"application_name": "ldap", "bytes_total_volume": 188794581}, {"application_name": "panorama", "bytes_total_volume": 129238639}, {"application_name": "unknown-tcp", "bytes_total_volume": 66488286}, {"application_name": "ssh", "bytes_total_volume": 40738056}, {"application_name": "paloalto-updates", "bytes_total_volume": 20026134}, {"application_name": "snmpv3-get-request", "bytes_total_volume": 12228707}, {"application_name": "not-applicable", "bytes_total_volume": 11349366}, {"application_name": "ping", "bytes_total_volume": 7014612}, {"application_name": "incomplete", "bytes_total_volume": 6714532}, {"application_name": "apt-get", "bytes_total_volume": 4006217}, {"application_name": "snmp-base", "bytes_total_volume": 3967961}, {"application_name": "snmp-trap", "bytes_total_volume": 3345326}, {"application_name": "syslog", "bytes_total_volume": 2828684}, {"application_name": "ocsp", "bytes_total_volume": 2422498}, {"application_name": "google-base", "bytes_total_volume": 1233952}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "private_apps", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 95, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 4527762052}, {"application_name": "paloalto-userid-agent", "bytes_total_volume": 2360390272}, {"application_name": "mysql", "bytes_total_volume": 418364038}, {"application_name": "web-browsing", "bytes_total_volume": 249984844}, {"application_name": "snmpv3", "bytes_total_volume": 191578991}, {"application_name": "ldap", "bytes_total_volume": 188794581}, {"application_name": "panorama", "bytes_total_volume": 129238639}, {"application_name": "unknown-tcp", "bytes_total_volume": 66488286}, {"application_name": "ssh", "bytes_total_volume": 40738056}, {"application_name": "paloalto-updates", "bytes_total_volume": 20026134}, {"application_name": "snmpv3-get-request", "bytes_total_volume": 12228707}, {"application_name": "not-applicable", "bytes_total_volume": 11349366}, {"application_name": "ping", "bytes_total_volume": 7014612}, {"application_name": "incomplete", "bytes_total_volume": 6714532}, {"application_name": "apt-get", "bytes_total_volume": 4006217}, {"application_name": "snmp-base", "bytes_total_volume": 3967961}, {"application_name": "snmp-trap", "bytes_total_volume": 3345326}, {"application_name": "syslog", "bytes_total_volume": 2828684}, {"application_name": "ocsp", "bytes_total_volume": 2422498}, {"application_name": "google-base", "bytes_total_volume": 1233952}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "private_apps", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 94, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 4527762052}, {"application_name": "paloalto-userid-agent", "bytes_total_volume": 2360390272}, {"application_name": "mysql", "bytes_total_volume": 418364038}, {"application_name": "web-browsing", "bytes_total_volume": 249984844}, {"application_name": "snmpv3", "bytes_total_volume": 191578991}, {"application_name": "ldap", "bytes_total_volume": 188794581}, {"application_name": "panorama", "bytes_total_volume": 129238639}, {"application_name": "unknown-tcp", "bytes_total_volume": 66488286}, {"application_name": "ssh", "bytes_total_volume": 40738056}, {"application_name": "paloalto-updates", "bytes_total_volume": 20026134}, {"application_name": "snmpv3-get-request", "bytes_total_volume": 12228707}, {"application_name": "not-applicable", "bytes_total_volume": 11349366}, {"application_name": "ping", "bytes_total_volume": 7014612}, {"application_name": "incomplete", "bytes_total_volume": 6714532}, {"application_name": "apt-get", "bytes_total_volume": 4006217}, {"application_name": "snmp-base", "bytes_total_volume": 3967961}, {"application_name": "snmp-trap", "bytes_total_volume": 3345326}, {"application_name": "syslog", "bytes_total_volume": 2828684}, {"application_name": "ocsp", "bytes_total_volume": 2422498}, {"application_name": "insufficient-data", "bytes_total_volume": 1037328}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "private_apps", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 94, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 4527762052}, {"application_name": "paloalto-userid-agent", "bytes_total_volume": 2360390272}, {"application_name": "mysql", "bytes_total_volume": 418364038}, {"application_name": "web-browsing", "bytes_total_volume": 249984844}, {"application_name": "snmpv3", "bytes_total_volume": 191578991}, {"application_name": "ldap", "bytes_total_volume": 188794581}, {"application_name": "panorama", "bytes_total_volume": 129238639}, {"application_name": "unknown-tcp", "bytes_total_volume": 66488286}, {"application_name": "ssh", "bytes_total_volume": 40738056}, {"application_name": "paloalto-updates", "bytes_total_volume": 20026134}, {"application_name": "snmpv3-get-request", "bytes_total_volume": 12228707}, {"application_name": "not-applicable", "bytes_total_volume": 11349366}, {"application_name": "ping", "bytes_total_volume": 7014612}, {"application_name": "incomplete", "bytes_total_volume": 6714532}, {"application_name": "apt-get", "bytes_total_volume": 4006217}, {"application_name": "snmp-base", "bytes_total_volume": 3967961}, {"application_name": "snmp-trap", "bytes_total_volume": 3345326}, {"application_name": "syslog", "bytes_total_volume": 2828684}, {"application_name": "ocsp", "bytes_total_volume": 2422498}, {"application_name": "insufficient-data", "bytes_total_volume": 1037328}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "aggregation_type": "last_30_days", "application_count": 131, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 4848557968}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 2567749023}, {"application_name": "paloalto-userid-agent", "bytes_total_volume": 2360390272}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 1110692695}, {"application_name": "mysql", "bytes_total_volume": 418364038}, {"application_name": "web-browsing", "bytes_total_volume": 399040905}, {"application_name": "zoom-meeting", "bytes_total_volume": 276691558}, {"application_name": "tenable.io", "bytes_total_volume": 214258430}, {"application_name": "snmpv3", "bytes_total_volume": 191578991}, {"application_name": "ldap", "bytes_total_volume": 188794581}, {"application_name": "panorama", "bytes_total_volume": 129238639}, {"application_name": "paloalto-updates", "bytes_total_volume": 101940780}, {"application_name": "unknown-tcp", "bytes_total_volume": 66495558}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 52718382}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 46457096}, {"application_name": "ssh", "bytes_total_volume": 40738056}, {"application_name": "pan-db-cloud", "bytes_total_volume": 19101616}, {"application_name": "zoom-base", "bytes_total_volume": 14437222}, {"application_name": "snmpv3-get-request", "bytes_total_volume": 12228707}, {"application_name": "paloalto-iot-security", "bytes_total_volume": 12074493}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "all", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "aggregation_type": "last_30_days", "application_count": 131, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 4848557968}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 2567749023}, {"application_name": "paloalto-userid-agent", "bytes_total_volume": 2360390272}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 1110692695}, {"application_name": "mysql", "bytes_total_volume": 418364038}, {"application_name": "web-browsing", "bytes_total_volume": 399040905}, {"application_name": "zoom-meeting", "bytes_total_volume": 276691558}, {"application_name": "tenable.io", "bytes_total_volume": 214258430}, {"application_name": "snmpv3", "bytes_total_volume": 191578991}, {"application_name": "ldap", "bytes_total_volume": 188794581}, {"application_name": "panorama", "bytes_total_volume": 129238639}, {"application_name": "paloalto-updates", "bytes_total_volume": 101940780}, {"application_name": "unknown-tcp", "bytes_total_volume": 66495558}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 52718382}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 46457096}, {"application_name": "ssh", "bytes_total_volume": 40738056}, {"application_name": "pan-db-cloud", "bytes_total_volume": 19101616}, {"application_name": "zoom-base", "bytes_total_volume": 14437222}, {"application_name": "snmpv3-get-request", "bytes_total_volume": 12228707}, {"application_name": "paloalto-iot-security", "bytes_total_volume": 12074493}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "all", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "aggregation_type": "last_30_days", "application_count": 133, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 4848557968}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 2567749023}, {"application_name": "paloalto-userid-agent", "bytes_total_volume": 2360390272}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 1110692695}, {"application_name": "google-base", "bytes_total_volume": 477595178}, {"application_name": "mysql", "bytes_total_volume": 418364038}, {"application_name": "web-browsing", "bytes_total_volume": 399040905}, {"application_name": "zoom-meeting", "bytes_total_volume": 276691558}, {"application_name": "tenable.io", "bytes_total_volume": 214258430}, {"application_name": "snmpv3", "bytes_total_volume": 191578991}, {"application_name": "ldap", "bytes_total_volume": 188794581}, {"application_name": "panorama", "bytes_total_volume": 129238639}, {"application_name": "paloalto-updates", "bytes_total_volume": 101940780}, {"application_name": "unknown-tcp", "bytes_total_volume": 66495558}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 52718382}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 46457096}, {"application_name": "ssh", "bytes_total_volume": 40738056}, {"application_name": "pan-db-cloud", "bytes_total_volume": 19101616}, {"application_name": "zoom-base", "bytes_total_volume": 14437222}, {"application_name": "snmpv3-get-request", "bytes_total_volume": 12228707}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "all", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "aggregation_type": "last_30_days", "application_count": 133, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "ssl", "bytes_total_volume": 4848557968}, {"application_name": "paloalto-shared-services", "bytes_total_volume": 2567749023}, {"application_name": "paloalto-userid-agent", "bytes_total_volume": 2360390272}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 1110692695}, {"application_name": "google-base", "bytes_total_volume": 477595178}, {"application_name": "mysql", "bytes_total_volume": 418364038}, {"application_name": "web-browsing", "bytes_total_volume": 399040905}, {"application_name": "zoom-meeting", "bytes_total_volume": 276691558}, {"application_name": "tenable.io", "bytes_total_volume": 214258430}, {"application_name": "snmpv3", "bytes_total_volume": 191578991}, {"application_name": "ldap", "bytes_total_volume": 188794581}, {"application_name": "panorama", "bytes_total_volume": 129238639}, {"application_name": "paloalto-updates", "bytes_total_volume": 101940780}, {"application_name": "unknown-tcp", "bytes_total_volume": 66495558}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 52718382}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 46457096}, {"application_name": "ssh", "bytes_total_volume": 40738056}, {"application_name": "pan-db-cloud", "bytes_total_volume": 19101616}, {"application_name": "zoom-base", "bytes_total_volume": 14437222}, {"application_name": "snmpv3-get-request", "bytes_total_volume": 12228707}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "private_apps", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 1233952}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 477595178}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "all", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 477595178}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "internet", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 476361226}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "internet", "application_sub_type": "sanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 476361226}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "private_apps", "application_sub_type": "sanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "PRIVATE APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-base", "bytes_total_volume": 1233952}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "internet", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 37042}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "internet", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 37042}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 37042}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "all", "application_sub_type": "tolerated", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "aggregation_type": "last_30_days", "application_count": 1, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "google-play", "bytes_total_volume": 37042}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 13, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-meeting", "bytes_total_volume": 276691558}, {"application_name": "tenable.io", "bytes_total_volume": 214258430}, {"application_name": "zoom-base", "bytes_total_volume": 14437222}, {"application_name": "datadog", "bytes_total_volume": 11481517}, {"application_name": "slack-base", "bytes_total_volume": 482363}, {"application_name": "icloud-base", "bytes_total_volume": 162998}, {"application_name": "okta", "bytes_total_volume": 133250}, {"application_name": "zoom-phone", "bytes_total_volume": 31005}, {"application_name": "gmail-base", "bytes_total_volume": 26683}, {"application_name": "github-base", "bytes_total_volume": 19128}, {"application_name": "service-now-base", "bytes_total_volume": 13507}, {"application_name": "salesforce-base", "bytes_total_volume": 7868}, {"application_name": "paloalto-suppt-case-uploading", "bytes_total_volume": 2361}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "saas", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 13, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-meeting", "bytes_total_volume": 276691558}, {"application_name": "tenable.io", "bytes_total_volume": 214258430}, {"application_name": "zoom-base", "bytes_total_volume": 14437222}, {"application_name": "datadog", "bytes_total_volume": 11481517}, {"application_name": "slack-base", "bytes_total_volume": 482363}, {"application_name": "icloud-base", "bytes_total_volume": 162998}, {"application_name": "okta", "bytes_total_volume": 133250}, {"application_name": "zoom-phone", "bytes_total_volume": 31005}, {"application_name": "gmail-base", "bytes_total_volume": 26683}, {"application_name": "github-base", "bytes_total_volume": 19128}, {"application_name": "service-now-base", "bytes_total_volume": 13507}, {"application_name": "salesforce-base", "bytes_total_volume": 7868}, {"application_name": "paloalto-suppt-case-uploading", "bytes_total_volume": 2361}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "saas", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 13, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-meeting", "bytes_total_volume": 276691558}, {"application_name": "tenable.io", "bytes_total_volume": 214258430}, {"application_name": "zoom-base", "bytes_total_volume": 14437222}, {"application_name": "datadog", "bytes_total_volume": 11481517}, {"application_name": "slack-base", "bytes_total_volume": 482363}, {"application_name": "icloud-base", "bytes_total_volume": 162998}, {"application_name": "okta", "bytes_total_volume": 133250}, {"application_name": "zoom-phone", "bytes_total_volume": 31005}, {"application_name": "gmail-base", "bytes_total_volume": 26683}, {"application_name": "github-base", "bytes_total_volume": 19128}, {"application_name": "service-now-base", "bytes_total_volume": 13507}, {"application_name": "salesforce-base", "bytes_total_volume": 7868}, {"application_name": "paloalto-suppt-case-uploading", "bytes_total_volume": 2361}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "saas", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "SAAS APPS", "aggregation_type": "last_30_days", "application_count": 13, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "zoom-meeting", "bytes_total_volume": 276691558}, {"application_name": "tenable.io", "bytes_total_volume": 214258430}, {"application_name": "zoom-base", "bytes_total_volume": 14437222}, {"application_name": "datadog", "bytes_total_volume": 11481517}, {"application_name": "slack-base", "bytes_total_volume": 482363}, {"application_name": "icloud-base", "bytes_total_volume": 162998}, {"application_name": "okta", "bytes_total_volume": 133250}, {"application_name": "zoom-phone", "bytes_total_volume": 31005}, {"application_name": "gmail-base", "bytes_total_volume": 26683}, {"application_name": "github-base", "bytes_total_volume": 19128}, {"application_name": "service-now-base", "bytes_total_volume": 13507}, {"application_name": "salesforce-base", "bytes_total_volume": 7868}, {"application_name": "paloalto-suppt-case-uploading", "bytes_total_volume": 2361}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "internet", "application_sub_type": "unsanctioned", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 41, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "paloalto-shared-services", "bytes_total_volume": 2567398709}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 1110692695}, {"application_name": "ssl", "bytes_total_volume": 320795916}, {"application_name": "web-browsing", "bytes_total_volume": 149056061}, {"application_name": "paloalto-updates", "bytes_total_volume": 81914646}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 52718382}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 46457096}, {"application_name": "pan-db-cloud", "bytes_total_volume": 19101616}, {"application_name": "paloalto-iot-security", "bytes_total_volume": 12074493}, {"application_name": "cortex-xdr", "bytes_total_volume": 7069624}, {"application_name": "paloalto-prisma-sdwan-base", "bytes_total_volume": 6672002}, {"application_name": "ocsp", "bytes_total_volume": 6110248}, {"application_name": "ws-discovery", "bytes_total_volume": 6093621}, {"application_name": "traps-management-service", "bytes_total_volume": 4827991}, {"application_name": "paloalto-device-telemetry", "bytes_total_volume": 4673036}, {"application_name": "prisma-cloud-compute-defender", "bytes_total_volume": 4383199}, {"application_name": "paloalto-wildfire-cloud", "bytes_total_volume": 3472331}, {"application_name": "paloalto-ace-kcs", "bytes_total_volume": 2294119}, {"application_name": "ms-update", "bytes_total_volume": 1135220}, {"application_name": "apple-update", "bytes_total_volume": 1126774}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "internet", "application_sub_type": "unsanctioned", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 41, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "paloalto-shared-services", "bytes_total_volume": 2567398709}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 1110692695}, {"application_name": "ssl", "bytes_total_volume": 320795916}, {"application_name": "web-browsing", "bytes_total_volume": 149056061}, {"application_name": "paloalto-updates", "bytes_total_volume": 81914646}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 52718382}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 46457096}, {"application_name": "pan-db-cloud", "bytes_total_volume": 19101616}, {"application_name": "paloalto-iot-security", "bytes_total_volume": 12074493}, {"application_name": "cortex-xdr", "bytes_total_volume": 7069624}, {"application_name": "paloalto-prisma-sdwan-base", "bytes_total_volume": 6672002}, {"application_name": "ocsp", "bytes_total_volume": 6110248}, {"application_name": "ws-discovery", "bytes_total_volume": 6093621}, {"application_name": "traps-management-service", "bytes_total_volume": 4827991}, {"application_name": "paloalto-device-telemetry", "bytes_total_volume": 4673036}, {"application_name": "prisma-cloud-compute-defender", "bytes_total_volume": 4383199}, {"application_name": "paloalto-wildfire-cloud", "bytes_total_volume": 3472331}, {"application_name": "paloalto-ace-kcs", "bytes_total_volume": 2294119}, {"application_name": "ms-update", "bytes_total_volume": 1135220}, {"application_name": "apple-update", "bytes_total_volume": 1126774}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "internet", "application_sub_type": "all", "platform_type": "ngfw", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 43, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "paloalto-shared-services", "bytes_total_volume": 2567398709}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 1110692695}, {"application_name": "google-base", "bytes_total_volume": 476361226}, {"application_name": "ssl", "bytes_total_volume": 320795916}, {"application_name": "web-browsing", "bytes_total_volume": 149056061}, {"application_name": "paloalto-updates", "bytes_total_volume": 81914646}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 52718382}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 46457096}, {"application_name": "pan-db-cloud", "bytes_total_volume": 19101616}, {"application_name": "paloalto-iot-security", "bytes_total_volume": 12074493}, {"application_name": "cortex-xdr", "bytes_total_volume": 7069624}, {"application_name": "paloalto-prisma-sdwan-base", "bytes_total_volume": 6672002}, {"application_name": "ocsp", "bytes_total_volume": 6110248}, {"application_name": "ws-discovery", "bytes_total_volume": 6093621}, {"application_name": "traps-management-service", "bytes_total_volume": 4827991}, {"application_name": "paloalto-device-telemetry", "bytes_total_volume": 4673036}, {"application_name": "prisma-cloud-compute-defender", "bytes_total_volume": 4383199}, {"application_name": "paloalto-wildfire-cloud", "bytes_total_volume": 3472331}, {"application_name": "paloalto-ace-kcs", "bytes_total_volume": 2294119}, {"application_name": "ms-update", "bytes_total_volume": 1135220}]}, {"tenant_id": "172924062", "sub_tenant_id": "172924062", "source_type": "iot", "application_type": "internet", "application_sub_type": "all", "platform_type": "all", "is_genai": "all", "source_type_label": "IOT DEVICES", "application_type_label": "INTERNET APPS", "aggregation_type": "last_30_days", "application_count": 43, "bytes_total_volume_all": 0, "top_applications": [{"application_name": "paloalto-shared-services", "bytes_total_volume": 2567398709}, {"application_name": "paloalto-logging-service", "bytes_total_volume": 1110692695}, {"application_name": "google-base", "bytes_total_volume": 476361226}, {"application_name": "ssl", "bytes_total_volume": 320795916}, {"application_name": "web-browsing", "bytes_total_volume": 149056061}, {"application_name": "paloalto-updates", "bytes_total_volume": 81914646}, {"application_name": "paloalto-dns-security", "bytes_total_volume": 52718382}, {"application_name": "paloalto-dlp-service", "bytes_total_volume": 46457096}, {"application_name": "pan-db-cloud", "bytes_total_volume": 19101616}, {"application_name": "paloalto-iot-security", "bytes_total_volume": 12074493}, {"application_name": "cortex-xdr", "bytes_total_volume": 7069624}, {"application_name": "paloalto-prisma-sdwan-base", "bytes_total_volume": 6672002}, {"application_name": "ocsp", "bytes_total_volume": 6110248}, {"application_name": "ws-discovery", "bytes_total_volume": 6093621}, {"application_name": "traps-management-service", "bytes_total_volume": 4827991}, {"application_name": "paloalto-device-telemetry", "bytes_total_volume": 4673036}, {"application_name": "prisma-cloud-compute-defender", "bytes_total_volume": 4383199}, {"application_name": "paloalto-wildfire-cloud", "bytes_total_volume": 3472331}, {"application_name": "paloalto-ace-kcs", "bytes_total_volume": 2294119}, {"application_name": "ms-update", "bytes_total_volume": 1135220}]}]}, "status": 200, "headers": {"content-length": "14850", "content-type": "application/json"}}