{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/netsec/v2/threat_source_count", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418845770, 1738010845770]}]}}, "response": {"header": {"createdAt": "2025-01-27T20:47:26Z", "dataCount": 1, "requestId": "227d0f20-6c72-4582-8113-b6d8494a2d4c", "clientRequestId": "34e0b74d-3796-4466-8d20-a95bf2e3cfb3", "isResourceDataOverridden": false, "status": {"subCode": 200}, "name": "netsec/v2/threat_source_count", "cache_operation": "IGNORED"}, "data": [{"tenant_id": "172924062", "entity_count": 17, "application_type": "all", "source_type": "iot", "security_service_type": "threat_protection", "type": "iot", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "aggregation_type": "last_30_days", "entity_count": 23, "platform_type": "all", "application_type": "all", "source_type": "iot", "sub_tenant_id": "172924062", "security_service_type": "all", "type": "iot", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 6, "application_type": "all", "source_type": "iot", "security_service_type": "dns_security", "type": "iot", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 23, "application_type": "all", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "all", "type": "iot", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 23, "application_type": "all", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "all", "type": "iot", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "aggregation_type": "last_30_days", "entity_count": 23, "platform_type": "all", "application_type": "all", "source_type": "iot", "sub_tenant_id": "172924062", "security_service_type": "all", "type": "iot", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 17, "application_type": "all", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "iot", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 6, "application_type": "all", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "iot", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 6, "application_type": "all", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "iot", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 17, "application_type": "all", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "iot", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 17, "application_type": "all", "source_type": "iot", "security_service_type": "threat_protection", "type": "iot", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 6, "application_type": "all", "source_type": "iot", "security_service_type": "dns_security", "type": "iot", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "aggregation_type": "last_30_days", "entity_count": 18315, "platform_type": "all", "application_type": "all", "source_type": "user_all", "sub_tenant_id": "172924062", "security_service_type": "all", "type": "user", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 127, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 7, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 56, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 29, "application_type": "all", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 9, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 127, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 71, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 6691, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 121, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 13035, "application_type": "all", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16405, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "aggregation_type": "last_30_days", "entity_count": 16, "platform_type": "all", "application_type": "all", "source_type": "user_all", "sub_tenant_id": "172924062", "security_service_type": "all", "type": "user", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 8, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 17149, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 56, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 30, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 121, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "aggregation_type": "last_30_days", "entity_count": 29, "platform_type": "all", "application_type": "all", "source_type": "user_all", "sub_tenant_id": "172924062", "security_service_type": "all", "type": "user", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 7, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 8, "application_type": "all", "source_type": "user_all", "security_service_type": "dns_security", "type": "user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 35, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16414, "application_type": "all", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16, "application_type": "all", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 344, "application_type": "all", "source_type": "user_all", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 71, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "aggregation_type": "last_30_days", "entity_count": 13151, "platform_type": "all", "application_type": "all", "source_type": "user_all", "sub_tenant_id": "172924062", "security_service_type": "all", "type": "user", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 253, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 167, "application_type": "all", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 121, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 31, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 6803, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 56, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "aggregation_type": "last_30_days", "entity_count": 16415, "platform_type": "all", "application_type": "all", "source_type": "user_all", "sub_tenant_id": "172924062", "security_service_type": "all", "type": "user", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 11, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 56, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 121, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 8, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16405, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "aggregation_type": "last_30_days", "entity_count": 205, "platform_type": "all", "application_type": "all", "source_type": "user_all", "sub_tenant_id": "172924062", "security_service_type": "all", "type": "user", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 6765, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 6654, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 169, "application_type": "all", "source_type": "user_all", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 168, "application_type": "all", "source_type": "user_all", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 148, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 36, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 22, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 142, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "aggregation_type": "last_30_days", "entity_count": 167, "platform_type": "all", "application_type": "all", "source_type": "user_all", "sub_tenant_id": "172924062", "security_service_type": "all", "type": "user", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 9, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 17153, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 10898, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 8, "application_type": "all", "source_type": "user_all", "security_service_type": "dns_security", "type": "user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 344, "application_type": "all", "source_type": "user_all", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 142, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 205, "application_type": "all", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "all", "source_type": "user_all", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 167, "application_type": "all", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "aggregation_type": "last_30_days", "entity_count": 167, "platform_type": "all", "application_type": "all", "source_type": "user_all", "sub_tenant_id": "172924062", "security_service_type": "all", "type": "user", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 253, "application_type": "all", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 18267, "application_type": "all", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 10960, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 148, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 11, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 22, "application_type": "all", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 38, "application_type": "all", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "other", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "all", "source_type": "other_all", "security_service_type": "wildfire", "type": "other", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 66, "application_type": "all", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 63, "application_type": "all", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 7164, "application_type": "all", "source_type": "other_all", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 7131, "application_type": "all", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 3, "application_type": "all", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "aggregation_type": "last_30_days", "entity_count": 7336, "platform_type": "all", "application_type": "all", "source_type": "other_all", "sub_tenant_id": "172924062", "security_service_type": "all", "type": "other", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 7131, "application_type": "all", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "all", "source_type": "other_all", "security_service_type": "wildfire", "type": "other", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 7300, "application_type": "all", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 38, "application_type": "all", "source_type": "other_all", "security_service_type": "dns_security", "type": "other", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 7164, "application_type": "all", "source_type": "other_all", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "all", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "other", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 3, "application_type": "all", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 38, "application_type": "all", "source_type": "other_all", "security_service_type": "dns_security", "type": "other", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 38, "application_type": "all", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "other", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 165, "application_type": "all", "source_type": "other_all", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "aggregation_type": "last_30_days", "entity_count": 7336, "platform_type": "all", "application_type": "all", "source_type": "other_all", "sub_tenant_id": "172924062", "security_service_type": "all", "type": "other", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 162, "application_type": "all", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 66, "application_type": "all", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 63, "application_type": "all", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 162, "application_type": "all", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 165, "application_type": "all", "source_type": "other_all", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 7300, "application_type": "all", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "all", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "other", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16405, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 9, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 36, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 8, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "branch_user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 13035, "application_type": "all", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 142, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16414, "application_type": "all", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 253, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 167, "application_type": "all", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 148, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 71, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 29, "application_type": "all", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 22, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 205, "application_type": "all", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 121, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 17149, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 71, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16, "application_type": "all", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 11, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 29, "application_type": "all", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 6691, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16, "application_type": "all", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 56, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 18315, "application_type": "all", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 8, "application_type": "all", "source_type": "user_branch", "security_service_type": "dns_security", "type": "branch_user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 1, "application_type": "all", "source_type": "user_branch", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 253, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 6765, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 11, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 169, "application_type": "all", "source_type": "user_branch", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16415, "application_type": "all", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 56, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 6803, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 167, "application_type": "all", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 31, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 35, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 344, "application_type": "all", "source_type": "user_branch", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 127, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 167, "application_type": "all", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 9, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 18267, "application_type": "all", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 6654, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 10960, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 8, "application_type": "all", "source_type": "user_branch", "security_service_type": "dns_security", "type": "branch_user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 142, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 121, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16405, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 10898, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 167, "application_type": "all", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 7, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 344, "application_type": "all", "source_type": "user_branch", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 17153, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 8, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "branch_user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 30, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 7, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 22, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 1, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 121, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 121, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 127, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 205, "application_type": "all", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 148, "application_type": "all", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 56, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 13151, "application_type": "all", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 56, "application_type": "all", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 168, "application_type": "all", "source_type": "user_branch", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 5486, "application_type": "all", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "all", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "external_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 26, "application_type": "all", "source_type": "other_external", "security_service_type": "url_filtering", "type": "external_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 5486, "application_type": "all", "source_type": "other_external", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 26, "application_type": "all", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "external_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 5484, "application_type": "all", "source_type": "other_external", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 5484, "application_type": "all", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "all", "source_type": "other_external", "security_service_type": "dns_security", "type": "external_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 5484, "application_type": "all", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 5486, "application_type": "all", "source_type": "other_external", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 5484, "application_type": "all", "source_type": "other_external", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "all", "source_type": "other_external", "security_service_type": "dns_security", "type": "external_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 26, "application_type": "all", "source_type": "other_external", "security_service_type": "url_filtering", "type": "external_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 5486, "application_type": "all", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "all", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "external_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 26, "application_type": "all", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "external_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 37, "application_type": "all", "source_type": "other_internal", "security_service_type": "dns_security", "type": "internal_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1837, "application_type": "all", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 37, "application_type": "all", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "internal_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 139, "application_type": "all", "source_type": "other_internal", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 3, "application_type": "all", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 66, "application_type": "all", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1670, "application_type": "all", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 37, "application_type": "all", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "internal_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 3, "application_type": "all", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1670, "application_type": "all", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 63, "application_type": "all", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 37, "application_type": "all", "source_type": "other_internal", "security_service_type": "dns_security", "type": "internal_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 136, "application_type": "all", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 63, "application_type": "all", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1879, "application_type": "all", "source_type": "other_internal", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "all", "source_type": "other_internal", "security_service_type": "wildfire", "type": "internal_hosts", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1837, "application_type": "all", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "all", "source_type": "other_internal", "security_service_type": "wildfire", "type": "internal_hosts", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1879, "application_type": "all", "source_type": "other_internal", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1709, "application_type": "all", "source_type": "other_internal", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 136, "application_type": "all", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 139, "application_type": "all", "source_type": "other_internal", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "all", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "internal_hosts", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 66, "application_type": "all", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "all", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "internal_hosts", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1709, "application_type": "all", "source_type": "other_internal", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "entity_count": 60, "application_type": "saas", "source_type": "user_all", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 60, "application_type": "saas", "source_type": "user_all", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3, "application_type": "saas", "source_type": "user_all", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 148, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 22, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 167, "application_type": "saas", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 121, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 7, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 7, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 56, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 18, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1531, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1426, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1398, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 29, "application_type": "saas", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16, "application_type": "saas", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 7, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 11, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16, "application_type": "saas", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 121, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 428, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 9, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 476, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 167, "application_type": "saas", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 167, "application_type": "saas", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16, "application_type": "saas", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 486, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 56, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 11, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 18, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 205, "application_type": "saas", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 71, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 167, "application_type": "saas", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 205, "application_type": "saas", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 56, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 418, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1928, "application_type": "saas", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1503, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 44, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 148, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 71, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 7, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3, "application_type": "saas", "source_type": "user_all", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 56, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 121, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 11, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 9, "application_type": "saas", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1783, "application_type": "saas", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1963, "application_type": "saas", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1819, "application_type": "saas", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 11, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 22, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 121, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 44, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3, "application_type": "saas", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16, "application_type": "saas", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 29, "application_type": "saas", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 810, "application_type": "saas", "source_type": "other_all", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 810, "application_type": "saas", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 820, "application_type": "saas", "source_type": "other_all", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 820, "application_type": "saas", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 820, "application_type": "saas", "source_type": "other_all", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 10, "application_type": "saas", "source_type": "other_all", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 810, "application_type": "saas", "source_type": "other_all", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 820, "application_type": "saas", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 10, "application_type": "saas", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 10, "application_type": "saas", "source_type": "other_all", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 810, "application_type": "saas", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 10, "application_type": "saas", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 1398, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 60, "application_type": "saas", "source_type": "user_branch", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3, "application_type": "saas", "source_type": "user_branch", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 1963, "application_type": "saas", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 56, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 205, "application_type": "saas", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 167, "application_type": "saas", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 11, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 148, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 167, "application_type": "saas", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 44, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16, "application_type": "saas", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 18, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 121, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 205, "application_type": "saas", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 121, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 11, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 1928, "application_type": "saas", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16, "application_type": "saas", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 1531, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 7, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 22, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 22, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 1783, "application_type": "saas", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 476, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 121, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 148, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 9, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 60, "application_type": "saas", "source_type": "user_branch", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 418, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 56, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 1503, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 7, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 29, "application_type": "saas", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 167, "application_type": "saas", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 71, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 11, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 1426, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16, "application_type": "saas", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 7, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 428, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 18, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 121, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 56, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 71, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 1819, "application_type": "saas", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3, "application_type": "saas", "source_type": "user_branch", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 9, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 486, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 7, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 56, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16, "application_type": "saas", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 167, "application_type": "saas", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "tolerated", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 29, "application_type": "saas", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 44, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 11, "application_type": "saas", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "yes", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 799, "application_type": "saas", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 799, "application_type": "saas", "source_type": "other_external", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 799, "application_type": "saas", "source_type": "other_external", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 799, "application_type": "saas", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 799, "application_type": "saas", "source_type": "other_external", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 799, "application_type": "saas", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 799, "application_type": "saas", "source_type": "other_external", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 799, "application_type": "saas", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 11, "application_type": "saas", "source_type": "other_internal", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 21, "application_type": "saas", "source_type": "other_internal", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 21, "application_type": "saas", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 10, "application_type": "saas", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 11, "application_type": "saas", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 11, "application_type": "saas", "source_type": "other_internal", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 10, "application_type": "saas", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 11, "application_type": "saas", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 10, "application_type": "saas", "source_type": "other_internal", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 21, "application_type": "saas", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 10, "application_type": "saas", "source_type": "other_internal", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 21, "application_type": "saas", "source_type": "other_internal", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "SAAS APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "entity_count": 6, "application_type": "private_apps", "source_type": "iot", "security_service_type": "dns_security", "type": "iot", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 23, "application_type": "private_apps", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "all", "type": "iot", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 17, "application_type": "private_apps", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "iot", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 17, "application_type": "private_apps", "source_type": "iot", "security_service_type": "threat_protection", "type": "iot", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 6, "application_type": "private_apps", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "iot", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 6, "application_type": "private_apps", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "iot", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 23, "application_type": "private_apps", "source_type": "iot", "security_service_type": "all", "type": "iot", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 17, "application_type": "private_apps", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "iot", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 23, "application_type": "private_apps", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "all", "type": "iot", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 6, "application_type": "private_apps", "source_type": "iot", "security_service_type": "dns_security", "type": "iot", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 23, "application_type": "private_apps", "source_type": "iot", "security_service_type": "all", "type": "iot", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 17, "application_type": "private_apps", "source_type": "iot", "security_service_type": "threat_protection", "type": "iot", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 3673, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 12, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3673, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 2, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3632, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 6160, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3665, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 2, "application_type": "private_apps", "source_type": "user_all", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 8, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 4056, "application_type": "private_apps", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 8, "application_type": "private_apps", "source_type": "user_all", "security_service_type": "dns_security", "type": "user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 4042, "application_type": "private_apps", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 8, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3665, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3107, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 24, "application_type": "private_apps", "source_type": "user_all", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 12, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3632, "application_type": "private_apps", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 8, "application_type": "private_apps", "source_type": "user_all", "security_service_type": "dns_security", "type": "user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3632, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 6149, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3096, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 24, "application_type": "private_apps", "source_type": "user_all", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3632, "application_type": "private_apps", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 7006, "application_type": "private_apps", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 2, "application_type": "private_apps", "source_type": "user_all", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 2, "application_type": "private_apps", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 7019, "application_type": "private_apps", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "other", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 2, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 2665, "application_type": "private_apps", "source_type": "other_all", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 2663, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 26, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "private_apps", "source_type": "other_all", "security_service_type": "wildfire", "type": "other", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 2663, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 26, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 2639, "application_type": "private_apps", "source_type": "other_all", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 2, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 2637, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "other", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "private_apps", "source_type": "other_all", "security_service_type": "wildfire", "type": "other", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 2665, "application_type": "private_apps", "source_type": "other_all", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 2, "application_type": "private_apps", "source_type": "other_all", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 26, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 25, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "other", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 25, "application_type": "private_apps", "source_type": "other_all", "security_service_type": "dns_security", "type": "other", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 26, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 25, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "other", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 2, "application_type": "private_apps", "source_type": "other_all", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 2639, "application_type": "private_apps", "source_type": "other_all", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 25, "application_type": "private_apps", "source_type": "other_all", "security_service_type": "dns_security", "type": "other", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 2637, "application_type": "private_apps", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 7006, "application_type": "private_apps", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 4042, "application_type": "private_apps", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3107, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3096, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3632, "application_type": "private_apps", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3632, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 7019, "application_type": "private_apps", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 8, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "branch_user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 8, "application_type": "private_apps", "source_type": "user_branch", "security_service_type": "dns_security", "type": "branch_user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 4056, "application_type": "private_apps", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 8, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "branch_user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 12, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3665, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3632, "application_type": "private_apps", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 2, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 24, "application_type": "private_apps", "source_type": "user_branch", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 24, "application_type": "private_apps", "source_type": "user_branch", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3673, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3632, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 12, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 2, "application_type": "private_apps", "source_type": "user_branch", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 2, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 6149, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 8, "application_type": "private_apps", "source_type": "user_branch", "security_service_type": "dns_security", "type": "branch_user", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 2, "application_type": "private_apps", "source_type": "user_branch", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 6160, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3673, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3665, "application_type": "private_apps", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 2, "application_type": "private_apps", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "external_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1121, "application_type": "private_apps", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1121, "application_type": "private_apps", "source_type": "other_external", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1121, "application_type": "private_apps", "source_type": "other_external", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1121, "application_type": "private_apps", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1121, "application_type": "private_apps", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 2, "application_type": "private_apps", "source_type": "other_external", "security_service_type": "url_filtering", "type": "external_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1121, "application_type": "private_apps", "source_type": "other_external", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1121, "application_type": "private_apps", "source_type": "other_external", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1121, "application_type": "private_apps", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 2, "application_type": "private_apps", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "external_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 2, "application_type": "private_apps", "source_type": "other_external", "security_service_type": "url_filtering", "type": "external_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1542, "application_type": "private_apps", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1516, "application_type": "private_apps", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "private_apps", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "internal_hosts", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 25, "application_type": "private_apps", "source_type": "other_internal", "security_service_type": "dns_security", "type": "internal_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "private_apps", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "internal_hosts", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1544, "application_type": "private_apps", "source_type": "other_internal", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1542, "application_type": "private_apps", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 25, "application_type": "private_apps", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "internal_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "private_apps", "source_type": "other_internal", "security_service_type": "wildfire", "type": "internal_hosts", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1518, "application_type": "private_apps", "source_type": "other_internal", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 25, "application_type": "private_apps", "source_type": "other_internal", "security_service_type": "dns_security", "type": "internal_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "private_apps", "source_type": "other_internal", "security_service_type": "wildfire", "type": "internal_hosts", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1544, "application_type": "private_apps", "source_type": "other_internal", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 26, "application_type": "private_apps", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 26, "application_type": "private_apps", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1518, "application_type": "private_apps", "source_type": "other_internal", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1516, "application_type": "private_apps", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 26, "application_type": "private_apps", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 25, "application_type": "private_apps", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "internal_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 26, "application_type": "private_apps", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "internet", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "all", "type": "iot", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "internet", "source_type": "iot", "security_service_type": "threat_protection", "type": "iot", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "internet", "source_type": "iot", "security_service_type": "all", "type": "iot", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "internet", "source_type": "iot", "security_service_type": "all", "type": "iot", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "internet", "source_type": "iot", "security_service_type": "threat_protection", "type": "iot", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "internet", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "iot", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "internet", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "iot", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "internet", "source_type": "iot", "platform_type_label": "NGFW", "security_service_type": "all", "type": "iot", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "IOT DEVICES"}, {"tenant_id": "172924062", "entity_count": 16317, "application_type": "internet", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3507, "application_type": "internet", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 9, "application_type": "internet", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 17657, "application_type": "internet", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 339, "application_type": "internet", "source_type": "user_all", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16796, "application_type": "internet", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 17715, "application_type": "internet", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16791, "application_type": "internet", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 95, "application_type": "internet", "source_type": "user_all", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 339, "application_type": "internet", "source_type": "user_all", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 96, "application_type": "internet", "source_type": "user_all", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 8874, "application_type": "internet", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 124, "application_type": "internet", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "internet", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3493, "application_type": "internet", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 8935, "application_type": "internet", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16323, "application_type": "internet", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3372, "application_type": "internet", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 251, "application_type": "internet", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16317, "application_type": "internet", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 24, "application_type": "internet", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 251, "application_type": "internet", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 10569, "application_type": "internet", "source_type": "user_all", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 3359, "application_type": "internet", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 16324, "application_type": "internet", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 10, "application_type": "internet", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 88, "application_type": "internet", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 23, "application_type": "internet", "source_type": "user_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 124, "application_type": "internet", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 1, "application_type": "internet", "source_type": "user_all", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 88, "application_type": "internet", "source_type": "user_all", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 10709, "application_type": "internet", "source_type": "user_all", "security_service_type": "all", "type": "user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "USERS"}, {"tenant_id": "172924062", "entity_count": 4535, "application_type": "internet", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 37, "application_type": "internet", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 4675, "application_type": "internet", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 158, "application_type": "internet", "source_type": "other_all", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 4709, "application_type": "internet", "source_type": "other_all", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 37, "application_type": "internet", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 14, "application_type": "internet", "source_type": "other_all", "security_service_type": "dns_security", "type": "other", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 4675, "application_type": "internet", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 158, "application_type": "internet", "source_type": "other_all", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 155, "application_type": "internet", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 4566, "application_type": "internet", "source_type": "other_all", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 4566, "application_type": "internet", "source_type": "other_all", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 4535, "application_type": "internet", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "other", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 14, "application_type": "internet", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "other", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 14, "application_type": "internet", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "other", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 14, "application_type": "internet", "source_type": "other_all", "security_service_type": "dns_security", "type": "other", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 3, "application_type": "internet", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 4709, "application_type": "internet", "source_type": "other_all", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 3, "application_type": "internet", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 40, "application_type": "internet", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 155, "application_type": "internet", "source_type": "other_all", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "other", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "entity_count": 40, "application_type": "internet", "source_type": "other_all", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "other", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "OTHER"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 9, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 17715, "application_type": "internet", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 339, "application_type": "internet", "source_type": "user_branch", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3493, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 251, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3359, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16317, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 8874, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 1, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 23, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 10709, "application_type": "internet", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16796, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 96, "application_type": "internet", "source_type": "user_branch", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 251, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 88, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16791, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3372, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 124, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16324, "application_type": "internet", "source_type": "user_branch", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16317, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 3507, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 8935, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 24, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "all", "type": "branch_user", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 17657, "application_type": "internet", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 16323, "application_type": "internet", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 10569, "application_type": "internet", "source_type": "user_branch", "security_service_type": "threat_protection", "type": "branch_user", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 10, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "NGFW", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 1, "application_type": "internet", "source_type": "user_branch", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "sanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 88, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 124, "application_type": "internet", "source_type": "user_branch", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 95, "application_type": "internet", "source_type": "user_branch", "security_service_type": "wildfire", "type": "branch_user", "security_service_type_label": "WildFire", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "user", "entity_count": 339, "application_type": "internet", "source_type": "user_branch", "security_service_type": "url_filtering", "type": "branch_user", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "BRANCH USERS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 4388, "application_type": "internet", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 4386, "application_type": "internet", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 24, "application_type": "internet", "source_type": "other_external", "security_service_type": "url_filtering", "type": "external_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 4388, "application_type": "internet", "source_type": "other_external", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "internet", "source_type": "other_external", "security_service_type": "dns_security", "type": "external_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "internet", "source_type": "other_external", "security_service_type": "dns_security", "type": "external_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 4388, "application_type": "internet", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 4386, "application_type": "internet", "source_type": "other_external", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 4386, "application_type": "internet", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 24, "application_type": "internet", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "external_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 24, "application_type": "internet", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "external_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 4386, "application_type": "internet", "source_type": "other_external", "security_service_type": "threat_protection", "type": "external_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "internet", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "external_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 4388, "application_type": "internet", "source_type": "other_external", "security_service_type": "all", "type": "external_hosts", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 24, "application_type": "internet", "source_type": "other_external", "security_service_type": "url_filtering", "type": "external_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 1, "application_type": "internet", "source_type": "other_external", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "external_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "EXTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 173, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 134, "application_type": "internet", "source_type": "other_internal", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 3, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 210, "application_type": "internet", "source_type": "other_internal", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 311, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 311, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 13, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "internal_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 3, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 131, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 40, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 37, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 40, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 13, "application_type": "internet", "source_type": "other_internal", "security_service_type": "dns_security", "type": "internal_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 134, "application_type": "internet", "source_type": "other_internal", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 351, "application_type": "internet", "source_type": "other_internal", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 13, "application_type": "internet", "source_type": "other_internal", "security_service_type": "dns_security", "type": "internal_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 13, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "dns_security", "type": "internal_hosts", "security_service_type_label": "DNS Security", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 131, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "url_filtering", "type": "internal_hosts", "security_service_type_label": "URL Filtering", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 210, "application_type": "internet", "source_type": "other_internal", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 173, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "NGFW", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "all", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 37, "application_type": "internet", "source_type": "other_internal", "platform_type_label": "Prisma Access", "security_service_type": "threat_protection", "type": "internal_hosts", "security_service_type_label": "Threat Prevention", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}, {"tenant_id": "172924062", "parent": "other", "entity_count": 351, "application_type": "internet", "source_type": "other_internal", "security_service_type": "all", "type": "internal_hosts", "aggregation_type": "last_30_days", "application_type_label": "INTERNET APPS", "platform_type": "all", "sub_tenant_id": "172924062", "is_genai": "all", "application_sub_type": "unsanctioned", "source_type_label": "INTERNAL HOSTS"}]}, "status": 200, "headers": {"content-length": "6600", "content-type": "application/json"}}