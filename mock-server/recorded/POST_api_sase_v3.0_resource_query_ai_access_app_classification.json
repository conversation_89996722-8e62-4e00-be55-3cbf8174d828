{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/ai_access/app_classification", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1738000338688, 1738011138688]}]}}, "response": {"header": {"createdAt": "2025-01-27T20:52:22Z", "dataCount": 1, "requestId": "a351a321-2bce-424f-8c14-5c06d2c6df12", "clientRequestId": "dc4ae82a-eb1b-4789-bda9-54efa8cfa488", "queryInput": {"time_range": "custom", "event_time": {"from": "2025-01-27T17:52:18Z", "to": "2025-01-27T20:52:18Z", "from_epoch": 1738000338000, "to_epoch": 1738011138000}}, "isResourceDataOverridden": false, "fieldList": [{"property": "sanctioned_applications", "alias": "sanctioned_applications", "dataType": "integer", "dataClass": "integer", "sequence": "1", "type": "integer"}, {"property": "tolerated_applications", "alias": "tolerated_applications", "dataType": "integer", "dataClass": "integer", "sequence": "2", "type": "integer"}, {"property": "unsanctioned_applications", "alias": "unsanctioned_applications", "dataType": "integer", "dataClass": "integer", "sequence": "3", "type": "integer"}], "status": {"subCode": 200}, "name": "ai_access/app_classification", "cache_time": 1738011139016, "cache_age_in_seconds": 3, "cache_operator": "inline", "cache_operation": "WRITE"}, "data": [{"sanctioned_applications": 2, "tolerated_applications": 2, "unsanctioned_applications": 6}]}, "status": 200, "headers": {"cache-time": "1738011139016", "content-length": "469", "content-type": "application/json"}}