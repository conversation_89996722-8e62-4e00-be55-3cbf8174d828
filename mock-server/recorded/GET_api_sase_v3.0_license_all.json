{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/license/all", "method": "GET", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418846372, 1738010846372]}]}}, "response": {"services": ["seb", "adem", "mobile_users", "service_connection", "remote_networks"], "service_details": {"remote_networks": {"unit": "mbps", "size": 5000, "expiry": 1858934835000}, "mobile_users": {"unit": "user", "size": 5000, "expiry": 1858934835000}, "service_connections": {"unit": "connections", "size": 5, "add_on": 0, "expiry": 1858934835000}, "adem:mobile_users": {"unit": "user", "size": 5000, "expiry": 1858934835000}, "adem:remote_networks": {"unit": "mbps", "size": 5000, "expiry": 1858934835000}, "seb:mobile_users": {"unit": "user", "size": 200, "expiry": 1858934835000}}, "edition_rn_and_mu": true, "project_based_access": false, "epm_enabled": false}, "status": 200, "headers": {"content-length": "239", "content-type": "application/json"}}