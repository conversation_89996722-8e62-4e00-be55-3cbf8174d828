{"url": "https://sase-saas-api.staging3.cirrotester.com/inline/v2/netsec/genai_recommendations?page=0&size=20", "method": "GET", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1738000338690, 1738011138690]}]}}, "response": {"data": [{"tsg_id": "1443076947", "control_point": "AI_ACCESS", "category": "SSPM_PLUGINS", "rule_id": "6bb344fc-184f-4d9c-9e64-f32f27da611a", "rule_name": "Review GenAI Plugins with High Severity Access Scopes", "recommendation": "We detected GenAI plugins with High severity access scopes. Review these GenAI app plugins and reduce access privileges", "recommendation_hash": "67b19187e6dbaf7762591debd5a46d9ac30040ef80ca775487f6ce5f1a13b074", "severity": 10, "modified_date": "2025-01-27T00:16:00.000Z"}, {"tsg_id": "1443076947", "control_point": "AI_ACCESS", "category": "SSPM_ONBOARD", "rule_id": "03782cc5-2160-4407-a421-7e1804855b1d", "rule_name": "Onboard SaaS Apps to Detect GenAI Plugins Installed Through SaaS Marketplaces", "recommendation": "You haven't onboarded any SaaS apps to detect and monitor GenAI plugin usage installed through SaaS marketplaces", "recommendation_hash": "8e06a3563047622a094a86266415958fc83f224de33fd4f43b97c4bd1dd7e34e", "severity": 7, "modified_date": "2025-01-27T00:16:00.000Z"}, {"tsg_id": "1443076947", "control_point": "AI_ACCESS", "category": "CIE_ONBOARDING", "rule_id": "c3731f3b-fb99-4aeb-bc89-2892ba06f54b", "rule_name": "Configure a CIE Directory to Identify GenAI Apps to Sanction", "recommendation": "You haven’t configured a CIE directory to collect enterprise application information", "recommendation_hash": "2ec73bdc4fec7967f497d13d636931898ae38dbf6ac92df80b1a887fc446e5ce", "severity": 5, "modified_date": "2025-01-27T00:16:00.000Z"}], "pageNumber": 0, "totalPages": 1, "totalRecords": 3}, "status": 200, "headers": {"content-length": "1488", "content-type": "application/json; charset=utf-8"}}