{"url": "https://sase-saas-api.staging3.cirrotester.com/inline/v2/netsec/sensitive_data_activity", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418843051, 1738010843051]}, {"property": "is_gen_ai_only", "operator": "equals", "values": [true]}]}}, "response": {"activities": [{"type": "FILE", "value": 0}, {"type": "NON_FILE", "value": 100}]}, "status": 200, "headers": {"content-length": "74", "content-type": "application/json; charset=utf-8"}}