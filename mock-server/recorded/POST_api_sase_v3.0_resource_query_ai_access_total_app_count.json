{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/ai_access/total_app_count", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418846375, 1738010846375]}]}, "histogram": {"property": "event_time", "range": "minute", "enableEmptyInterval": true, "value": "3"}}, "response": {"header": {"createdAt": "2025-01-27T20:47:29Z", "dataCount": 1, "requestId": "2a5b7d5f-81a8-4536-9738-7abc3dbfe604", "clientRequestId": "f5ecacd4-35fa-49fe-a3fc-7bde70c98d59", "queryInput": {"time_range": "custom", "event_time": {"from": "2024-12-28T20:47:26Z", "to": "2025-01-27T20:47:26Z", "from_epoch": 1735418846000, "to_epoch": 1738010846000}}, "isResourceDataOverridden": false, "fieldList": [{"property": "count", "alias": "count", "dataType": "integer", "dataClass": "integer", "sequence": "1", "type": "integer"}], "status": {"subCode": 200}, "name": "ai_access/total_app_count", "cache_operation": "IGNORED"}, "data": [{"count": 47}]}, "status": 200, "headers": {"content-length": "388", "content-type": "application/json"}}