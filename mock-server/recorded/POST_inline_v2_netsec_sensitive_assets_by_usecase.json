{"url": "https://sase-saas-api.staging3.cirrotester.com/inline/v2/netsec/sensitive_assets_by_usecase", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "last_n_days", "values": [30]}, {"property": "is_gen_ai_only", "operator": "equals", "values": [true]}, {"property": "aiaccess_usecase_name", "operator": "equals", "values": ["Productivity Assistant"]}]}}, "response": [], "status": 200, "headers": {"content-length": "2", "content-type": "application/json; charset=utf-8"}}