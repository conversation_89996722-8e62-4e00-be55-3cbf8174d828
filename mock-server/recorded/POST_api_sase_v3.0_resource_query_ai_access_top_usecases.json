{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/ai_access/top_usecases", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1738000338689, 1738011138689]}]}}, "response": {"header": {"createdAt": "2025-01-27T20:52:24Z", "dataCount": 10, "requestId": "b70489cb-835f-4aba-bbf9-6c8111b1de2b", "clientRequestId": "65f23627-8ef3-4446-b299-c2f2c1cda5c8", "queryInput": {"time_range": "custom", "event_time": {"from": "2025-01-27T17:52:18Z", "to": "2025-01-27T20:52:18Z", "from_epoch": 1738000338000, "to_epoch": 1738011138000}}, "isResourceDataOverridden": false, "fieldList": [{"property": "use_case", "alias": "use_case", "dataType": "string", "dataClass": "string", "sequence": "1", "type": "string"}, {"property": "use_case_id", "alias": "use_case_id", "dataType": "string", "dataClass": "string", "sequence": "2", "type": "string"}, {"property": "total_applications", "alias": "total_applications", "dataType": "integer", "dataClass": "integer", "sequence": "3", "type": "integer"}, {"property": "unsanctioned_applications", "alias": "unsanctioned_applications", "dataType": "integer", "dataClass": "integer", "sequence": "4", "type": "integer"}, {"property": "tolerated_applications", "alias": "tolerated_applications", "dataType": "integer", "dataClass": "integer", "sequence": "5", "type": "integer"}, {"property": "sanctioned_applications", "alias": "sanctioned_applications", "dataType": "integer", "dataClass": "integer", "sequence": "6", "type": "integer"}, {"property": "unsanctioned_users", "alias": "unsanctioned_users", "dataType": "integer", "dataClass": "integer", "sequence": "7", "type": "integer"}, {"property": "tolerated_users", "alias": "tolerated_users", "dataType": "integer", "dataClass": "integer", "sequence": "8", "type": "integer"}, {"property": "sanctioned_users", "alias": "sanctioned_users", "dataType": "integer", "dataClass": "integer", "sequence": "9", "type": "integer"}, {"property": "total_users", "alias": "total_users", "dataType": "integer", "dataClass": "integer", "sequence": "10", "type": "integer"}, {"property": "total_bytes", "alias": "total_bytes", "dataType": "integer", "dataClass": "integer", "sequence": "11", "type": "integer"}, {"property": "most_used_application", "alias": "most_used_application", "dataType": "json", "dataClass": "json", "sequence": "12", "type": "json"}], "status": {"subCode": 200}, "name": "ai_access/top_usecases", "cache_time": 1738011139009, "cache_age_in_seconds": 5, "cache_operator": "inline", "cache_operation": "WRITE"}, "data": [{"use_case": "Writing Assistant", "use_case_id": "writing-assistant", "total_applications": 5, "unsanctioned_applications": 3, "tolerated_applications": 0, "sanctioned_applications": 2, "unsanctioned_users": 2, "tolerated_users": 0, "sanctioned_users": 26, "total_users": 28, "total_bytes": 642146, "most_used_application": {"application_name": "grammarly", "application_sub_type": "sanctioned", "users": 25, "data_transferred": 364992, "data_used_in_models": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAZlBMVEUrtnP///8AsWcas2zs+PKi2rwhtG8IsWgVsmve8uic2Lhox5am3L/N69vw+vVCvICK0qzl9e33/PqQ1LDH6ddKvoSH0apwyZvW7+JZwo275M6x4McxuHfB59Pm9e56zKFexJC04cqW+KhPAAAB5UlEQVR4nM2X2XKDMAxF7UDkNAstzKSkQyb5/78siwFtDga30+ohC1gH6doWsrGJZv41oAGXAWQOmg2A0hlirlwDOINRDM6RgFx17xF5BKAIeQ9WLAGa1/7GcEEZwC35t3K+Aiy7dxYGxPlTgtngTwhmiz8mzD8j9JvNSYCcP7iO92q5tBoOEOvni87Wkd8vGIA/3Qo76DL475zevEp/EUROADRJn2Dldd1Z7SmAAWdya9i2T3zp0l8qlWFGBnBXYvJR1DIEIyXURB0vgxwoQst1/2E8CaGcAHgRAr8wWcbn0k0A/pyL5t/pQMU2QYDuL20E4G3g1gAaD8DSdsKcYgHgAVgybdUHzXlAxgCx/v288BRWAeCnAMkpcBF3sQAXmEZRvkI2prC4kCwynO+4kJaWMoQAwb3AS/QiwPHRTMZj71pxwLydowpKJiKYC0pMSRuFyPjA/pNWUKvsqLvUABdVWmk+rAjCToNnAC7rLISnf9rt/dA2mveb/wslBpAXi33QgA9WMWdICg8CEKKJ7vbTJzIBLAWI17ursHvRa1qhZPnrXWsQoe6Stm+nSaF5yYgGI73FSW+y0tu89EYzvdVNb7bT2/30A4dNPvJ0ttcPXXtt7O8c+7y1B09obdvBM9r+HvANg0UP7Siq140AAAAASUVORK5CYII=", "enterprise_plan_offered": true, "threats": 0}}, {"use_case": "Conversational Agent", "use_case_id": "conversational-agent", "total_applications": 8, "unsanctioned_applications": 6, "tolerated_applications": 1, "sanctioned_applications": 1, "unsanctioned_users": 5, "tolerated_users": 8, "sanctioned_users": 1, "total_users": 13, "total_bytes": 904338, "most_used_application": {"application_name": "openai-chatgpt", "application_sub_type": "tolerated", "users": 8, "data_transferred": 548736, "data_used_in_models": true, "icon": "data:image/png;base64,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", "enterprise_plan_offered": true, "threats": 93}}, {"use_case": "Code Assistant & Generator", "use_case_id": "code-assistant-generator", "total_applications": 3, "unsanctioned_applications": 3, "tolerated_applications": 0, "sanctioned_applications": 0, "unsanctioned_users": 3, "tolerated_users": 0, "sanctioned_users": 0, "total_users": 3, "total_bytes": 7412, "most_used_application": {"application_name": "github-copilot", "application_sub_type": "unsanctioned", "users": 1, "data_transferred": 2124, "data_used_in_models": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAMAAACdt4HsAAAAkFBMVEX6+vr///8AAAD39/fe3t7x8fH8/Pz09PTt7e3c3Nzh4eHk5OTp6enn5+fr6+sGBgZZWVmLi4sVFRVDQ0PExMTQ0NA1NTWxsbGBgYE3NzcmJiZTU1N3d3e+vr6dnZ3IyMhpaWmIiIi2trYcHBylpaWUlJRLS0tfX18/Pz8eHh4qKiqhoaGCgoJCQkJ4eHhvb29GJ7hfAAACVklEQVR4nO1X23LbIBDVchNCtqM4ce0009ZJJpnGTcf//3dl0YVFgCSnL52pz4vFsud4WVawKoor/mVoseKAUFoYeTF7DSEKUS5n88pxNu+sQ6NcJGIhv0b3ioXYFtaq9QK6LC39jiWwtxOGz/JtqDxFR1gFPqNg+ZCj9xJ/w0cFOaHAQcENYzeF24XfnvbqDFUXQ17AuP/vt1699fzNUA5uus7xNcAra0j1kJV7E2Y5I6DABM5wjvitQiYEjrMP1HmdFDjlsiBa+dA7EjjiOF2RCppI4F5Uz6Fthz7ptyLYAofaDUObck4qwS9jgQ9mS3ckUDinVADGVU4iBeyR2n5Yg4LU4SBgnDCVqAPA6vqATUJAQlw0jPzQqKCKt4CrsYAdaPz9OrbaoRlnkddlv+T2PIODV8OnHbG6dzIshZLmLHj7ETo5QWPgNtQm8hrHHeBbUE01cekSjR4UT2i7JVpHe0CSBTSEP41D71n4060MA5iGIq79IugaZwW87xHkUgGVFPCvRFqAsTPhqIsFcHDvH8+fErj1j9urAHv5lAB5NAkB1R9sMlnKB3/HyNMKYgE1lDI2FV8igRw6x2cgrYJra1p7Ncd/H/5IkDuW4wV+cjOP0/yW/mSfRudqiU3lQzt9zrFf/PwquuK5vVv96cdj9s4nL3PBuz7k+5DkHWXvBzM2eyrX+cqCpDkJnC+n2qy1k3hLs+9wTq3nOkXcSFsj25C8Fe5cqpa07aVp2zyQv1x5/dx3KeVmacfOuRQ63AMtLv5k4LIWemOxst8bsz32Ff85/gDZDhiDlYmjPQAAAABJRU5ErkJggg==", "enterprise_plan_offered": true, "threats": 0}}, {"use_case": "Audio Generator", "use_case_id": "audio-generator", "total_applications": 3, "unsanctioned_applications": 3, "tolerated_applications": 0, "sanctioned_applications": 0, "unsanctioned_users": 2, "tolerated_users": 0, "sanctioned_users": 0, "total_users": 2, "total_bytes": 272057, "most_used_application": {"application_name": "deepl-base", "application_sub_type": "unsanctioned", "users": 1, "data_transferred": 199887, "data_used_in_models": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAIVElEQVR4nO2Ze3QVxR3HP7M7996QFwkESDCJDdJwiCIRiSAC5gCFiCCexpSHUlEQJfioooIpUjkUqodXKQdQXioKPqmiIkVbK6XURxW0B4iYAIY8IEEkhLzuvbsz/eMG5JoHKIU9lPv5c3dn9rvf+c1vfjMr5OVZmosYw2kBThMywGkBThMywGkBThMywGkBThMywGkBThMywGkBThMywGkBThMywGkBThMywGkBThMywGkBTnPRGyDP9EFbKbTPAkNguiRCiHOp67xx2ghQWmPVe2kbE81dY4aTdX0vtNZYfv/50HfOaTYCNGD7fLg9HkZmZzFt4ijSLktGKc2bf93G75etY8fOPSBNpGmeR8n/W0RT/wUsywKlub5Xdx7PvY2B117VqOHxmjqWv7qRhc+tp7S0HMPtxjAuvGnRyADLb5GakshjE0cxetgAPG5Xix0UlVUwd+WrPPvGZurrfRjm97PKsm2w7IY3AcLAkCbGecofls8PQiBdzae6IANsv59BfXuydu5jtGvTGgCvz88X+YV06ZRMTFREsx1t2vpvxjw0h6qaOgxDoJWiU3JHuqQkorXGb9kcrapmV8E31NbVI+UZ59+fhACuvSqNOq+P7bsKmk3aQSq00gzL7H3y4wHmrn6NJ55azqhbsnhx7rRmX3hDvwxSkhLYsasAYUhsy2b6PWP49c2/wFYKARiGwbbtu5gwfT5f7StGShnINX4/2KpBkUTK73OK1hrbb4FSIARCSsyGKLMsG8MwUEqBZQUauCSmYSCl5PknH2F/STmD7pgK6CZNaDQMWgenBGUrbEPw/rbPWbLuLcZnZxHmcTfqyLJtgvoXgjCPm8qqarLuysO2LIb0y2Dm/bezYtYUBo+fitfnR2vof013rutxOcdr6nhv2+d8va8Y6ZIopXBJyY2ZvejepROl5d+yccunlB/+DpfbRWpKIiWHDnNJhziGZfbC57N4Z8snFBUfxCUlgU9p+dfnaePw0Qm/YtB1PXBLyTOvvMOI3Bn85vZswjwuOl96CUnx7Vpsb9k2+fuKqao8xmef7SQqIpypd40ko1sX/vGv7cycMp68u0dT/u1Rwjwenrh3LGOnPsWmDz4mIjKcVbOncMuQ/pSUH6ZdbAyPTjjMzZN/x/6SQ7w0L4+a+no6JyXQKsxDTHQkj5TlMGLSDHYWFqG1Rp/m1+9p64Awj5s+6Wn0vCKVFbMeYvqkW8lbuJoBox5g9ENz8PpOXw+********************************/+zpdBo+j27AJ/GfPPhbl5eJ2u7j1poGMHJrJ+OkLSB10O71G3kd4mIdZD4xDCIHH46Lf1VeweO0G0oaMY+jE39I2JpqZ9wfun8lv7x+VibTWJLRvy4Pjspk4tZCoyPAfVxFqTXjD9Kmp89I7vStCQFbfDDIzuqOU4pL4OJIT2vPzTsn0SU/DthW5o4eTO3o4lm0THRnO9RndaBMThRCCotJyFq15k9raWg6+9082fvgJ/TOuJLZ1VCA3nK0Bx47XsPXznYS5XTz9ykbiYqNZlJdLetfLSIqPw93CEgOBGejz+9H1PjAEOTf0x1aKL7/ay8De6QDsLS6j4kglQgh25O/Fb1kcP16NZSs0mt17i05G2o78Qo4eq0YpDVrTKsxDRCsPtZVVYJrEREfi9fmxbRuBQCmN8vkDy0ITRVsj9T9co2ctW8v8Pz1PXGICi2fcS86QfpimyZWpKUHPCSEaRYMQ4HZJrk2/HJRi1LABjL1pECtf30TB3gNIw6Cqppb6eh9L126gTWxrcrL68+qmLRz4ppS3P/yYcb8czIGDFbz81gd0Tf0ZA3qls+K1d6msCpjQvm0Mz8x8gKUvbKBf7+4M6duTP655g6PHqhEC2sRE0eeaKzENQcmhbzlQVoE4pWALNkBrqmpqgy7Fx8USERXJyBszGTU0s9mRtm1FbZ0XRMBs0FRV19E6MoLNK+dgK0VlVTWLX9zA9IWrMVyS/MIi7pg2j/nT7uaLDcvRaAqLSlm/eSvC4+Ltv33EtPkreWziaKZPuhXbVnz8ZX4gpwiQ0qSgqJS2sa15f81cAF7fvJVZS15ASpM6r58eaZ3Ztm4hAAueW8+U2UuRp6xiQYWQsm0S49sxf9o9ZA/uixACpRQFRWVc2rF9k8sfQMWRSmY/vY6l697ixFqotSahXRsS49uhlMbr81FxpJKDFUcwTAPDaFjLfX7i4mLpnNwRy7L5uqiEqqoapEsGagDLIqljB5IS2lNdU8ee/cV4vT6iIsPZ/udlfFN2iKET8ujetTN+22Z3YRGWZSGlSZeUJCLDW6G1xjAEZRXfUXLwcFAENC6FbRsBDMvszYzJt9HzitRmR72u3stLG//Ok8tfpmBfMabbHVQL2LZC23ZDSAiEYZwsYoKiRzU8hwgy51RN2IFCyJQmWkNEKw+7N66i+FAFfXLug4Y2pjRPTsUTexoEoEGYZqP3N7kZ0lpj+3xERUdxZ3YWD4/PIbFDXCPh81a9xiOzl4Dbdc5L22B9IKXB5DEjOHa8mtXr/4L5E3ekTRpwAqUUyucnOSmBh8fncGd2FhGtwk7ez1uwij8seRF5yrXzieX1BTY7p9mwtUSLhZBhGMgwDwcOHub+mYsZOO5R3t3yadB9HDwZkh73WX08nOGZoJQm0uPmkx27GZE7g5EPziZ/74EL+iDkBC1OgaY4kR/ad4gjrnU0e/YXI4wL92z1RxtwAlsptNYXfBT85NRtXsCjfir/H19xFoQMcFqA04QMcFqA04QMcFqA04QMcFqA04QMcFqA04QMcFqA04QMcFqA04QMcFqA04QMcFqA01z0BvwXg/AfDOsW0VAAAAAASUVORK5CYII=", "enterprise_plan_offered": true, "threats": 0}}, {"use_case": "Meeting Assistant", "use_case_id": "meeting-assistant", "total_applications": 2, "unsanctioned_applications": 0, "tolerated_applications": 1, "sanctioned_applications": 1, "unsanctioned_users": 0, "tolerated_users": 1, "sanctioned_users": 1, "total_users": 2, "total_bytes": 259927, "most_used_application": {"application_name": "notion-base", "application_sub_type": "sanctioned", "users": 1, "data_transferred": 5097, "data_used_in_models": false, "icon": "data:image/png;base64,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", "enterprise_plan_offered": true, "threats": 1}}, {"use_case": "Developer Platform", "use_case_id": "developer-platform", "total_applications": 1, "unsanctioned_applications": 1, "tolerated_applications": 0, "sanctioned_applications": 0, "unsanctioned_users": 1, "tolerated_users": 0, "sanctioned_users": 0, "total_users": 1, "total_bytes": 3827, "most_used_application": {"application_name": "openai-base", "application_sub_type": "unsanctioned", "users": 1, "data_transferred": 3827, "data_used_in_models": true, "icon": "data:image/png;base64,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", "enterprise_plan_offered": true, "threats": 0}}, {"use_case": "Enterprise Search", "use_case_id": "enterprise-search", "total_applications": 1, "unsanctioned_applications": 1, "tolerated_applications": 0, "sanctioned_applications": 0, "unsanctioned_users": 1, "tolerated_users": 0, "sanctioned_users": 0, "total_users": 1, "total_bytes": 3827, "most_used_application": {"application_name": "openai-base", "application_sub_type": "unsanctioned", "users": 1, "data_transferred": 3827, "data_used_in_models": true, "icon": "data:image/png;base64,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", "enterprise_plan_offered": true, "threats": 0}}, {"use_case": "Image Editor & Generator", "use_case_id": "image-editor-generator", "total_applications": 1, "unsanctioned_applications": 1, "tolerated_applications": 0, "sanctioned_applications": 0, "unsanctioned_users": 1, "tolerated_users": 0, "sanctioned_users": 0, "total_users": 1, "total_bytes": 3827, "most_used_application": {"application_name": "openai-base", "application_sub_type": "unsanctioned", "users": 1, "data_transferred": 3827, "data_used_in_models": true, "icon": "data:image/png;base64,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", "enterprise_plan_offered": true, "threats": 0}}, {"use_case": "Video Editor & Generator", "use_case_id": "video-editor-generator", "total_applications": 1, "unsanctioned_applications": 1, "tolerated_applications": 0, "sanctioned_applications": 0, "unsanctioned_users": 1, "tolerated_users": 0, "sanctioned_users": 0, "total_users": 1, "total_bytes": 3827, "most_used_application": {"application_name": "openai-base", "application_sub_type": "unsanctioned", "users": 1, "data_transferred": 3827, "data_used_in_models": true, "icon": "data:image/png;base64,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", "enterprise_plan_offered": true, "threats": 0}}, {"use_case": "Productivity Assistant", "use_case_id": "productivity-assistant", "total_applications": 0, "unsanctioned_applications": 0, "tolerated_applications": 0, "sanctioned_applications": 0, "unsanctioned_users": 0, "tolerated_users": 0, "sanctioned_users": 0, "total_users": 0, "most_used_application": {"threats": 0}}]}, "status": 200, "headers": {"cache-time": "1738011139009", "content-length": "13249", "content-type": "application/json"}}