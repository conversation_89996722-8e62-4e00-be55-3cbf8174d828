{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/netsec/dns_request_histogram", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1732826845773, 1735418845773]}]}, "histogram": {"property": "event_time", "range": "minute", "enableEmptyInterval": true, "value": "3"}}, "response": {"header": {"createdAt": "2025-01-27T20:47:30Z", "dataCount": 0, "requestId": "8e592a7d-859a-4172-bf11-77daf184986f", "clientRequestId": "e114521b-37a5-445e-89ab-3403587b4be4", "queryInput": {"time_range": "custom", "event_time": {"from": "2024-11-28T20:47:25Z", "to": "2024-12-28T20:47:25Z", "from_epoch": 1732826845000, "to_epoch": 1735418845000}}, "isResourceDataOverridden": false, "fieldList": [{"property": "event_time", "alias": "event_time", "dataType": "timestamp", "dataClass": "timestamp", "sequence": "1", "type": "timestamp"}, {"property": "histogram_time", "alias": "histogram_time", "dataType": "timestamp", "dataClass": "timestamp", "sequence": "2", "type": "timestamp"}, {"property": "source_type", "alias": "source_type", "dataType": "string", "dataClass": "string", "sequence": "3", "type": "string"}, {"property": "application_type", "alias": "application_type", "dataType": "string", "dataClass": "string", "sequence": "4", "type": "string"}, {"property": "platform_type", "alias": "platform_type", "dataType": "string", "dataClass": "string", "sequence": "5", "type": "string"}, {"property": "total_count", "alias": "total_count", "dataType": "integer", "dataClass": "integer", "sequence": "6", "type": "integer"}, {"property": "malicious_count", "alias": "malicious_count", "dataType": "integer", "dataClass": "integer", "sequence": "7", "type": "integer"}, {"property": "source_type_label", "alias": "source_type_label", "dataType": "string", "dataClass": "string", "sequence": "8", "type": "string"}, {"property": "application_type_label", "alias": "application_type_label", "dataType": "string", "dataClass": "string", "sequence": "9", "type": "string"}, {"property": "platform_type_label", "alias": "platform_type_label", "dataType": "string", "dataClass": "string", "sequence": "10", "type": "string"}], "status": {"subCode": 204}, "name": "netsec/dns_request_histogram", "cache_operation": "IGNORED"}, "data": []}, "status": 200, "headers": {"content-length": "556", "content-type": "application/json"}}