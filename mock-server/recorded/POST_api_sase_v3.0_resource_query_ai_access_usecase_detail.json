{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/ai_access/usecase_detail", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418963303, 1738010963303]}, {"property": "use_case", "operator": "in", "values": ["Productivity Assistant"]}]}}, "response": {"header": {"createdAt": "2025-01-27T20:49:27Z", "dataCount": 2, "requestId": "b1d7269a-4fb5-402c-a328-c6cde18c4991", "clientRequestId": "a0510451-8db4-4319-b0d7-feceefb67d6b", "queryInput": {"time_range": "custom", "event_time": {"from": "2024-12-28T20:49:23Z", "to": "2025-01-27T20:49:23Z", "from_epoch": 1735418963000, "to_epoch": 1738010963000}}, "isResourceDataOverridden": false, "fieldList": [{"property": "application_name", "alias": "application_name", "dataType": "string", "dataClass": "string", "sequence": "1", "type": "string"}, {"property": "icon", "alias": "icon", "dataType": "string", "dataClass": "string", "sequence": "2", "type": "string"}, {"property": "application_summary", "alias": "application_summary", "dataType": "string", "dataClass": "string", "sequence": "3", "type": "string"}, {"property": "application_sub_type", "alias": "application_sub_type", "dataType": "string", "dataClass": "string", "sequence": "4", "type": "string"}, {"property": "rules", "alias": "rules", "dataType": "integer", "dataClass": "integer", "sequence": "5", "type": "integer"}, {"property": "total_users", "alias": "total_users", "dataType": "integer", "dataClass": "integer", "sequence": "6", "type": "integer"}, {"property": "blocked_users", "alias": "blocked_users", "dataType": "integer", "dataClass": "integer", "sequence": "7", "type": "integer"}, {"property": "allowed_users", "alias": "allowed_users", "dataType": "integer", "dataClass": "integer", "sequence": "8", "type": "integer"}, {"property": "threats", "alias": "threats", "dataType": "integer", "dataClass": "integer", "sequence": "9", "type": "integer"}, {"property": "data_transfered", "alias": "data_transfered", "dataType": "integer", "dataClass": "integer", "sequence": "10", "type": "integer"}, {"property": "app_risk", "alias": "app_risk", "dataType": "integer", "dataClass": "integer", "sequence": "11", "type": "integer"}, {"property": "enterprise_plan_offered", "alias": "enterprise_plan_offered", "dataType": "boolean", "dataClass": "boolean", "sequence": "12", "type": "boolean"}, {"property": "data_used_in_models", "alias": "data_used_in_models", "dataType": "boolean", "dataClass": "boolean", "sequence": "13", "type": "boolean"}], "status": {"subCode": 200}, "name": "ai_access/usecase_detail", "cache_operation": "IGNORED"}, "data": [{"application_name": "bing-ai-base", "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAH8klEQVR4nO2ba4hdVxXHf2uf+5pM3k3Spnk1TSH4SmiECq0NxUJiS8GKgloRI5aq1W8qflE/iIgoCoqI+ilgfSX9INGSFI3YUtGkoHm0Q2zzMs3TySQzk5m5955z9lp+2PfeuTOZScaZPbkDzYI1c+45w521fue/111773PFzIy3sblOB9Bpuw2g0wF02m4D6HQAnbbbADodQKctKoBj15TXBzXmW866FWK90e/eyvnR8RSAR1ckPLuhxMqKxHr7WbMoCjCDfRdycg9OYd95zzOv1vnj+Zy53mbGAQCYCqICJpRFuFwzvvVaxtcOpZwZnrsY4tUAA3TUE4Mi8JcLnmcO1Hn+TI7OQQ7xACigAn7URYWKCAM1+O7RnO+/nuHnGIS4ADyYBkfBfPDEoATsOunZc8ZH+5cxLCIAwVSCCiY4FhUSE3ad9NTmEINZqwETeQE4e804O4eKYrQ+INzxmyeWGwxnBsyNHiEaADRAuBkCM8DmRvIQE0BzCLQTkLGv547wRy2qAlDGVpWJMrZJznfIItYAAgBG82sKfUy+cyh5iF0DfGOMN2x8rjbZhQ5aRADSUsCNbPwivDfY9Rb87Qo8vAyeWAndSbSobmrxi+BU/q4Nwj/74XtvgIrxch/sPg9PrYbH7hS6bgGIaI2QqWA+dH2TeuN6u2UKIlB2wY8PG988pnzqXzl7Lin1WV5fiTwZojUnaHn7ueZxmwI2L4a184y80RsVBEqJ0TNsfPXfGU8dqfPC5ZxslupGPAV4A29Y3pwEWfC8eS5cJ7cxALoTeHSFkBkYhomhYhScUXTG4SHPF9+o8dGeIfb2Z7HCbVk8BXiwnJBgbpDT5uG1NXy8PXEXLChYQyABQtMLiZEkxsHhjE+fGOTp04P05vHGRUQAzbs91ptAxhyPk/P6bnhgqVC30cS14V4MdUohMZwzft0/wsdP9XE2izOljDsE8obM27w5FJoQzNv1n4XAkytBmomHwRCOnaKiqAvHCxwcraZ86cxl+v3MlRBVAYy74+0qsPbrExS09y0R1ncbmRkmBBU4DSpwhhcNQ0KUhQ6OVGv88NKVGYcduQZcL33GQbBJAHQlsG2FI23UAO8ULyHx5lBIUEoNCAsc7B24xoHh6ozCjjoEJqoB1ylhEgAAjy93dBfAo40aEKTvRUGUohgFjKIYRQHB2H3l6ow663gAbpDwdecneShlwzxh+zJh0JSsLXkVpSAh8UITAkqXGD3VKv+p16cdd7zZYFMBN5kMmU6aPwDfuLdEsWj8/mqdi6p4lKIoRdrvfvCSQGqeI9UR7imXpxV3HABGqPihm2H8gtd1s8AbAFhYEL6zvsIXVpXYP5iyZ7DKoapSV49zbSoQSCSo6WStNu3Q4yrAayu35gfU+DUBm+KCyJqSY8eyCjuWVTid5bw0XOOXp/oZKCmlilHIlQKGiDHgp98hRh8Ck30yW+OHWevVlG155hj+a8bFF6oMVnJWbkvofpcj8Yab4RJTRACK5TrhuG8dN4bHVB/MM4O9h0b48R/6ee1EDZcLWa9w8ucp9rGEdQ87LAu1YLoWVQGa2w3vRSv5KRA4diHjp38eYv/hKr6mzJ9XIK8pooINC6efz1l+D8xfZSxNpp9GPAC5YbnecE3EplAEr1aVX71aZffBEfoHlHIxwauQ1wVLBCsIVobqIFx4xbPxE7CuXJl23NEAqFc09+iNNjwaNWAiAajBi2/W2HmwyqlLOYkXukoOr2AOkqQBIBG0AKWKMHgayrnnnfO6px13PACZBr/JpodOMAR6+nJ2HhrhwOkUS4WuYkjcC4gD5xzmDJc0QYAWwHvlvmKJ1aXp9QAQCYA46KqATz3JTRSgBtIA0FczfvtmjX3HawyPeEqJoIng87BMJiKIGOIE5wznaIFwTuhe4nls+R24GWyzxQEAbHpvFy8fHKbgJu+uzUKtSHPjTxdznjtW59xATlGhnDh8Y7EkJB6mxyKCCw+etJSQJJCb8fgD89kyb8GMYo82BB7Z2s1z+69xrWeEpOyu3xbzoU1efF+FX/Qqvf9N0dSoJGGxVCX0j9K4mQFCQwHNoSCCOaOawTvuTfjy9sXIDDdZowHYvFh46LMr+MfOS1w9MoQ2VnvNAosFd5dYuXURS949jz5vuEwRJ6FoNnNoSz78HlWDkwCplsHGtUV+8Pn53Llw5uFHA9ANfHBNQu/Td1PrGaLv6BDp1Zxil2PRhgpL3jMfqTh8zePMWv1bu1Ck/aeE4wABcjW8Ch9+sMRXPtLFskVxJrIS8wsTQx6+fgbO5uGRGG0sf5kamhqahV4heKNxShXNPJoqWhd8KmgdNBU0hbwmVEeMtUscz26rsH1LMVa4QGQAAD0j8O0zRuoJfbq3xnND1mqWxgLwaKZYavhW4gFCdQSKKjx5f4nPPFJm6fz4zxVEBwDwygD85JyRqZHoFAFkhq+HxLOakFZh010FPre1wv3rZm+PLN7eYJu9fxFUnPCzc0ZvDYr/x+SnnsPSLuGTD1b40KYSpVmJcNRmRQFNu5TCby4qf79ijORG4g3xowqw3NDUk9WVrK6UveOhVSV2bKmwevGteZB9VgE07cSI8VKfcbhfuTTsqdYNnyvkRlmV5UXYfEfCB9aU2bjsFu6Nc4sANC03uFw3rtSNug8ru0tLwvIuR7FD31y4pQDmot3+xkinA+i03QbQ6QA6bbcBdDqATtvbHsD/AG4HOW+sWlLFAAAAAElFTkSuQmCC", "application_summary": "Bing AI is an AI-powered search engine that has intelligent search capabilities, redefining the way users access information on the internet. This App-ID covers the traffic for Bing AI chat application.", "application_sub_type": "sanctioned", "rules": 1, "total_users": 3, "blocked_users": 0, "allowed_users": 3, "threats": 0, "data_transfered": 53665, "app_risk": 4, "enterprise_plan_offered": false, "data_used_in_models": true}, {"application_name": "google-gemini", "icon": "data:image/png;base64,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", "application_summary": "Gemini is a family of multimodal large language models developed by Google, serving as the successor to LaMDA and PaLM2. This App-ID covers the web and mobile traffic of Google Gemini application.", "application_sub_type": "unsanctioned", "rules": 3, "total_users": 7, "blocked_users": 0, "allowed_users": 7, "threats": 1, "data_transfered": 2114509, "app_risk": 4, "enterprise_plan_offered": true, "data_used_in_models": true}]}, "status": 200, "headers": {"content-length": "6785", "content-type": "application/json"}}