{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/netsec/wildfire_count_histogram", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418845772, 1738010845772]}]}, "histogram": {"property": "event_time", "range": "minute", "enableEmptyInterval": true, "value": "3"}}, "response": {"header": {"createdAt": "2025-01-27T20:47:27Z", "dataCount": 7, "requestId": "dedc9deb-3c09-4bb9-8af5-b0b322001db6", "clientRequestId": "7f3c1636-1a43-4c4a-aa05-34bd06755427", "queryInput": {"time_range": "custom", "event_time": {"from": "2024-12-28T20:47:25Z", "to": "2025-01-27T20:47:25Z", "from_epoch": 1735418845000, "to_epoch": 1738010845000}}, "isResourceDataOverridden": false, "fieldList": [{"property": "event_time", "alias": "event_time", "dataType": "timestamp", "dataClass": "timestamp", "sequence": "1", "type": "timestamp"}, {"property": "histogram_time", "alias": "histogram_time", "dataType": "timestamp", "dataClass": "timestamp", "sequence": "2", "type": "timestamp"}, {"property": "source_type", "alias": "source_type", "dataType": "string", "dataClass": "string", "sequence": "3", "type": "string"}, {"property": "security_service_type", "alias": "security_service_type", "dataType": "string", "dataClass": "string", "sequence": "4", "type": "string"}, {"property": "application_type", "alias": "application_type", "dataType": "string", "dataClass": "string", "sequence": "5", "type": "string"}, {"property": "platform_type", "alias": "platform_type", "dataType": "string", "dataClass": "string", "sequence": "6", "type": "string"}, {"property": "wildfire_files_submitted_count", "alias": "wildfire_files_submitted_count", "dataType": "integer", "dataClass": "integer", "sequence": "7", "type": "integer"}, {"property": "wildfire_malicious_verdict_count", "alias": "wildfire_malicious_verdict_count", "dataType": "integer", "dataClass": "integer", "sequence": "8", "type": "integer"}, {"property": "source_type_label", "alias": "source_type_label", "dataType": "string", "dataClass": "string", "sequence": "9", "type": "string"}, {"property": "security_service_type_label", "alias": "security_service_type_label", "dataType": "string", "dataClass": "string", "sequence": "10", "type": "string"}, {"property": "application_type_label", "alias": "application_type_label", "dataType": "string", "dataClass": "string", "sequence": "11", "type": "string"}, {"property": "platform_type_label", "alias": "platform_type_label", "dataType": "string", "dataClass": "string", "sequence": "12", "type": "string"}], "status": {"subCode": 200}, "name": "netsec/wildfire_count_histogram", "cache_operation": "IGNORED"}, "data": [{"event_time": 1735720800000, "histogram_time": 1735720800000, "source_type": "other", "security_service_type": "wildfire", "application_type": "private_apps", "platform_type": "ngfw", "wildfire_files_submitted_count": 1, "wildfire_malicious_verdict_count": 1, "source_type_label": "OTHER", "security_service_type_label": "WildFire", "application_type_label": "PRIVATE APPS", "platform_type_label": "NGFW"}, {"event_time": 1737967200000, "histogram_time": 1737967200000, "source_type": "user", "security_service_type": "wildfire", "application_type": "saas", "platform_type": "ngfw", "wildfire_files_submitted_count": 1, "wildfire_malicious_verdict_count": 1, "source_type_label": "USERS", "security_service_type_label": "WildFire", "application_type_label": "SAAS APPS", "platform_type_label": "NGFW"}, {"event_time": 1737967200000, "histogram_time": 1737967200000, "source_type": "user", "security_service_type": "wildfire", "application_type": "internet", "platform_type": "prisma_access", "wildfire_files_submitted_count": 2, "wildfire_malicious_verdict_count": 2, "source_type_label": "USERS", "security_service_type_label": "WildFire", "application_type_label": "INTERNET APPS", "platform_type_label": "Prisma Access"}, {"event_time": 1737967200000, "histogram_time": 1737967200000, "source_type": "user", "security_service_type": "wildfire", "application_type": "private_apps", "platform_type": "ngfw", "wildfire_files_submitted_count": 11, "wildfire_malicious_verdict_count": 11, "source_type_label": "USERS", "security_service_type_label": "WildFire", "application_type_label": "PRIVATE APPS", "platform_type_label": "NGFW"}, {"event_time": 1737967200000, "histogram_time": 1737967200000, "source_type": "user", "security_service_type": "wildfire", "application_type": "internet", "platform_type": "ngfw", "wildfire_files_submitted_count": 0, "wildfire_malicious_verdict_count": 0, "source_type_label": "USERS", "security_service_type_label": "WildFire", "application_type_label": "INTERNET APPS", "platform_type_label": "NGFW"}, {"event_time": 1737708000000, "histogram_time": 1737708000000, "source_type": "user", "security_service_type": "wildfire", "application_type": "private_apps", "platform_type": "prisma_access", "wildfire_files_submitted_count": 0, "wildfire_malicious_verdict_count": 0, "source_type_label": "USERS", "security_service_type_label": "WildFire", "application_type_label": "PRIVATE APPS", "platform_type_label": "Prisma Access"}, {"event_time": 1737967200000, "histogram_time": 1737967200000, "source_type": "user", "security_service_type": "wildfire", "application_type": "saas", "platform_type": "prisma_access", "wildfire_files_submitted_count": 8, "wildfire_malicious_verdict_count": 8, "source_type_label": "USERS", "security_service_type_label": "WildFire", "application_type_label": "SAAS APPS", "platform_type_label": "Prisma Access"}]}, "status": 200, "headers": {"content-length": "866", "content-type": "application/json"}}