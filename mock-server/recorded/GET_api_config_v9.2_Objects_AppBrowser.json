{"url": "https://dev-paas-f5.prismaaccess.paloaltonetworks.com/api/config/v9.2/Objects/AppBrowser?type=snippet&snippet=app-tagging", "method": "GET", "request": {}, "response": {"customApps": {"@status": "success", "@code": "7", "result": {"@currentTime": "2025-01-27 20:49:23"}}, "disabledApps": [], "ok": true, "pattern": ["snmp-req-oid-obj-name", "snmp-req-oid-obj-val", "rtmp-req-message-body", "ftp-rsp-banner", "ftp-rsp-message", "oracle-req-data-text", "http-req-cookie", "http-req-mime-form-data", "http-rsp-status-line", "http-rsp-headers", "http-rsp-non-2xx-response-body", "imap-req-cmd-line", "giop-req-message-body", "giop-rsp-message-body", "email-headers", "dns-rsp-authority-section", "dns-rsp-ptr-answer-data", "dns-rsp-answer-section", "pe-dos-headers", "file-elf-body", "h225-payload", "pe-file-header", "pe-optional-header", "pe-section-header", "pe-body-data", "ssl-rsp-certificate", "ssl-rsp-cert-subject<PERSON>b<PERSON>ey", "ssl-req-random-bytes", "ssl-req-certificate", "ssl-req-client-hello", "ssl-req-chello-sni", "http-req-ms-subdomain", "http-req-x-accesstoken-decoded", "http-req-cookie-decoded", "http-req-authorization-bearer-decoded", "http-req-authentication-decoded", "http-req-idtoken-decoded", "http-req-x-amz-security-token-decoded", "http-req-x-humanloop-active-organization", "http-req-x-figma-team-id", "http-req-workspaceid", "http-req-chatgpt-account-id", "http-req-openai-organization", "http-req-workspace-token", "http-req-x-w-account", "http-req-x-workspace-id", "ssl-rsp-server-hello", "gdbremote-req-context", "gdbremote-rsp-context", "imap-req-params-after-first-param", "imap-req-authenticate-data", "smtp-rsp-content", "nettcp-req-context", "brass-req-tcp-payload", "brass-rsp-tcp-payload", "file-unknown-body", "file-tiff-body", "file-html-body", "file-office-content", "file-pdf-body", "file-flv-body", "file-swf-body", "file-riff-body", "file-mov-body", "file-java-body", "file-mp3-body", "jpeg-file-segment-header", "jpeg-file-segment-data", "jpeg-file-scan-data", "irc-req-prefix", "irc-req-params", "telnet-req-client-data", "telnet-rsp-server-data", "unknown-req-udp-payload", "unknown-rsp-udp-payload", "unknown-req-tcp-payload", "unknown-rsp-tcp-payload", "dns-req-header", "dns-rsp-header", "dns-req-section", "dns-rsp-queries-section", "dns-req-answer-section", "dns-req-query-name", "dns-req-authority-section", "dns-req-addition-section", "dns-rsp-addition-section", "dhcp-req-chaddr", "dhcp-rsp-chaddr", "ms-ds-smb-req-share-name", "ssh-req-banner", "ssh-rsp-banner", "msrpc-req-bind-data", "mssql-db-req-body", "mssql-db-rsp-body", "ike-req-headers", "ike-rsp-headers", "ike-req-payload-text", "ike-rsp-payload-text", "snmp-req-community-text", "modbus-req-body", "X.400-message-body", "ms-ds-smb-req-v1-create-filename", "ms-ds-smb-req-v2-create-filename", "gtpv2-req-pco-realm", "gtpv2-rsp-pco-realm", "icmp-req-data", "icmp-rsp-data", "tcp-context-free", "udp-context-free", "pre-app-req-data", "pre-app-rsp-data", "file-zip-archived-filenames", "sip-req-headers", "smtp-email-headers", "file-data", "smtp-req-protocol-payload", "smtp-rsp-protocol-payload", "imap-req-protocol-payload", "imap-rsp-protocol-payload", "pop3-req-protocol-payload", "pop3-rsp-protocol-payload", "ftp-req-protocol-payload", "ftp-rsp-protocol-payload", "netbios-dg-req-protocol-payload", "netbios-dg-rsp-protocol-payload", "netbios-ns-req-protocol-payload", "netbios-ns-rsp-protocol-payload", "icmp-req-protocol-payload", "icmp-rsp-protocol-payload", "ssl-req-protocol-payload", "ssl-rsp-protocol-payload", "open-vpn-req-protocol-payload", "irc-req-protocol-payload", "irc-rsp-protocol-payload", "dns-req-protocol-payload", "dns-rsp-protocol-payload", "ms-ds-smb-req-protocol-payload", "ms-ds-smb-rsp-protocol-payload", "msrpc-req-protocol-payload", "msrpc-rsp-protocol-payload", "http-req-user-agent-header", "http-rsp-reason", "cip-ethernet-ip-req-ansi-symbol", "cip-ethernet-ip-req-command-specific-data", "net-cx-rsp-payload", "net-cx-req-payload", "ldap-req-searchrequest-baseobject", "ldap-rsp-searchresentry-objectname", "ms-rdp-req-cookie", "cip-ethernet-ip-req-path", "mqtt-req-connect-message-client-id", "mqtt-req-publish-message-topic", "mqtt-req-publish-message", "mqtt-req-subscribe-message-topic", "rmi-iiop-rsp-payload", "ldap-rsp-message-body", "dicom-req-payload", "dicom-rsp-payload", "websocket-req-context", "siemens-s7-req-header", "siemens-s7-rsp-header", "siemens-s7-req-parameter", "siemens-s7-rsp-parameter", "siemens-s7-req-data", "siemens-s7-rsp-data", "siemens-s7-req-tis-parameter", "siemens-s7-rsp-tis-parameter", "http-req-version-string"], "tagApps": {"grammarly": {"@name": "grammarly", "@uuid": "bbda137a-0653-4046-9f72-ff36c61eb41e", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["best-practice", "dfsfsdf", "custom123", "Sanctioned"]}}, "notion-base": {"@name": "notion-base", "@uuid": "dbfed36b-cfbb-4467-a7ec-119e4557465b", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "openai-chatgpt": {"@name": "openai-chatgpt", "@uuid": "5b815e73-d920-4e7e-9d53-1775b1bc5aa5", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Tolerated"]}}, "openai-base": {"@name": "openai-base", "@uuid": "74065f88-8714-4b7c-8f75-b73f3a0f1985", "@type": "snippet", "@loc": "app-tagging", "tag": {}}, "tabnine": {"@name": "tabnine", "@uuid": "6e20c424-4cb7-4a6f-a2e2-d1e5bc7937b1", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "acrolinx": {"@name": "acrolinx", "@uuid": "4434e901-94c6-43b8-9099-27ac2355dd3f", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Tolerated"]}}, "acobot-base": {"@name": "acobot-base", "@uuid": "6feace18-fd13-49ef-ab66-b21e44fb37aa", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "google-base": {"@name": "google-base", "@uuid": "ca2c767c-4407-4c2a-b392-a532e4d4b662", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "codeium": {"@name": "codeium", "@uuid": "286245ec-a49a-4832-a2a5-ab1eca367cb2", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Tolerated"]}}, "claude-base": {"@name": "claude-base", "@uuid": "f5e6b42f-84f8-43bb-ad40-2272422485b9", "@type": "snippet", "@loc": "app-tagging", "tag": {}}, "bing-ai-base": {"@name": "bing-ai-base", "@uuid": "e45e1ff5-2966-43ca-9272-c93e91feebd3", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "coveo-base": {"@name": "coveo-base", "@uuid": "8a084ec9-8545-48a4-8bf0-8935eb578233", "@type": "snippet", "@loc": "app-tagging", "tag": {}}, "sourcegraph-cody": {"@name": "sourcegraph-cody", "@uuid": "b0381df8-94f3-42fb-89c1-71c2d7932296", "@type": "snippet", "@loc": "app-tagging", "tag": {}}, "ssl": {"@name": "ssl", "@uuid": "e26be0e2-8b1e-4cb0-9ac4-4061db4f8328", "@type": "snippet", "@loc": "app-tagging", "tag": {}}, "azure-ai-services": {"@name": "azure-ai-services", "@uuid": "5522d942-9ebe-4043-86f4-46200b99d2dd", "@type": "snippet", "@loc": "app-tagging", "tag": {}}, "1-page": {"@name": "1-page", "@uuid": "eb198476-bc0a-4f11-bc86-508d626f61a5", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "ck12": {"@name": "ck12", "@uuid": "b4e52ad2-200f-4048-906c-63c05a334804", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "claimremedi": {"@name": "claimremedi", "@uuid": "d9060166-d7ac-4799-9332-440cf94ada81", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "weights-and-biases-base": {"@name": "weights-and-biases-base", "@uuid": "0301f911-150c-4601-bae4-215b1500cc0a", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Tolerated"]}}, "openai-api": {"@name": "openai-api", "@uuid": "fa64d30a-dd17-43e5-89d1-0893375134df", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "1010data": {"@name": "1010data", "@uuid": "47a7e704-a054-4346-b73d-bf50c5e69c75", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Tolerated"]}}, "deepl-base": {"@name": "deepl-base", "@uuid": "76fe4fe1-6d4e-4a18-a8c1-0ae81147ea27", "@type": "snippet", "@loc": "app-tagging", "tag": {}}, "jasper-ai-base": {"@name": "jasper-ai-base", "@uuid": "68ed1025-cb3c-45df-bdac-19b1b600c30c", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Tolerated"]}}, "notion-delete": {"@name": "notion-delete", "@uuid": "0c2bbef1-b5e8-4646-956d-a9e6cbfa167a", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "deepl-translator": {"@name": "deepl-translator", "@uuid": "9d5c7b6b-d0e8-45b9-9016-bb775c6b2399", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Tolerated"]}}, "hyro": {"@name": "hyro", "@uuid": "0d39423a-9570-4e8c-b0a3-d6561054c3db", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "vpcart": {"@name": "vpcart", "@uuid": "811cddc2-0cc8-48cd-b90f-69f0e6d30798", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "01com": {"@name": "01com", "@uuid": "6ecdd84a-6504-499e-8966-fbd465e59114", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "azure-openai-studio": {"@name": "azure-openai-studio", "@uuid": "ab4986e8-42e6-445a-a281-7ac0bc83ab4e", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Sanctioned"]}}, "reclaim.ai-base": {"@name": "reclaim.ai-base", "@uuid": "cdab0297-80b2-4f48-96d6-be07c73cebea", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Tolerated"]}}, "google-play": {"@name": "google-play", "@uuid": "53383dfb-75eb-459e-8a76-2fe046f6ce68", "@type": "snippet", "@loc": "app-tagging", "tag": {"member": ["Tolerated"]}}}, "tags": [{"@name": "Sanctioned", "@loc": "predefined", "@type": "snippet", "color": "color17"}, {"@name": "Tolerated", "@loc": "predefined", "@type": "snippet", "color": "color19"}, {"@name": "[Generative AI]", "@loc": "predefined", "@type": "snippet", "color": "color20"}, {"@name": "[Audio Generator]", "@loc": "predefined", "@type": "snippet", "color": "color21"}, {"@name": "[Conversational Agent]", "@loc": "predefined", "@type": "snippet", "color": "color22"}, {"@name": "[Code Assistant & Generator]", "@loc": "predefined", "@type": "snippet", "color": "color23"}, {"@name": "[Developer Platform]", "@loc": "predefined", "@type": "snippet", "color": "color24"}, {"@name": "[Enterprise Search]", "@loc": "predefined", "@type": "snippet", "color": "color25"}, {"@name": "[Image Editor & Generator]", "@loc": "predefined", "@type": "snippet", "color": "color26"}, {"@name": "[Meeting Assistant]", "@loc": "predefined", "@type": "snippet", "color": "color27"}, {"@name": "[Productivity Assistant]", "@loc": "predefined", "@type": "snippet", "color": "color28"}, {"@name": "[Video Editor & Generator]", "@loc": "predefined", "@type": "snippet", "color": "color29"}, {"@name": "[Writing Assistant]", "@loc": "predefined", "@type": "snippet", "color": "color30"}, {"@name": "empty", "@loc": "predefined", "@type": "snippet"}, {"@name": "dfsfsdf", "@uuid": "16ae376f-542d-4f5e-8348-92d86333d45e", "@type": "snippet", "@loc": "app-tagging"}]}, "status": 200, "headers": {"cache-control": "no-cache", "content-type": "application/json; charset=utf-8", "x-trace-id": "c42e97fb-a6e6-4f7d-aa85-07cea0a9a0a8"}}