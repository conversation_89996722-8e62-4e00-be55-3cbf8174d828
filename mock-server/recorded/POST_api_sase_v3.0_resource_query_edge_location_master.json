{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/edge_location_master", "method": "POST", "request": {"filter": {"operator": "AND", "rules": [{"property": "node_type", "operator": "not_in", "values": [50]}]}, "properties": [{"function": "distinct", "property": "edge_location_display_name"}]}, "response": {"header": {"createdAt": "2025-01-27T20:47:26Z", "dataCount": 0, "requestId": "87fe495e-c263-4768-a198-b929969db508", "clientRequestId": "6b9cda68-7380-4209-8a79-981547178855", "isResourceDataOverridden": false, "fieldList": [{"property": "edge_location_display_name", "alias": "edge_location_display_name", "dataType": "string", "dataClass": "string", "sequence": "1", "type": "string"}], "status": {"subCode": 204}, "name": "edge_location_master", "cache_operation": "IGNORED"}, "data": []}, "status": 200, "headers": {"content-length": "314", "content-type": "application/json"}}