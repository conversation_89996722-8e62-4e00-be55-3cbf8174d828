{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/netsec/v2/threat_count", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1738000338687, 1738011138687]}]}}, "response": {"header": {"createdAt": "2025-01-27T20:52:19Z", "dataCount": 1, "requestId": "17cfe8e4-7dce-4040-ac99-ed2dfce8aa02", "clientRequestId": "8f1a8301-7aa2-433a-b6bf-27707e87bbf7", "isResourceDataOverridden": false, "status": {"subCode": 200}, "name": "netsec/v2/threat_count", "cache_time": 1738011139026, "cache_age_in_seconds": 0, "cache_operator": "inline", "cache_operation": "WRITE"}, "data": [{"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "google-analytics", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQBxgCIAuCByAQBxgVIBkyGIyDswSu2xPS1E7e+vQGo/5xsYabAtmCNQ==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 8, "blocked_count": 0, "user_count": 7, "sub_tenant_id": "*********", "allowed_count": 8, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "slack-downloading", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQAxgCIAuCBxMQAxgVIBkyC8fQ5wa9k6gB/4R9", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 3, "blocked_count": 0, "user_count": 3, "sub_tenant_id": "*********", "allowed_count": 3, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "slack-base", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBMSU7Qw=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 1, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "openai-chatgpt", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "yes", "application_sub_type": "tolerated", "severity": "High", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwsQARgVIBkyA9bHeg==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 91, "blocked_count": 91, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "facebook-video", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBMKdhGI=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 2, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "gmail-downloading", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBK+s5Q4=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 2, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "tableau-base", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQAhgCIAuCBxAQAhgVIBkyCOq7sguhycMD", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 9, "blocked_count": 0, "user_count": 2, "sub_tenant_id": "*********", "allowed_count": 9, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "url_filtering", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "nordvpn", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBLmxgwQ=", "version": 1, "security_service_type_label": "URL Filtering", "threat_count": 1, "blocked_count": 1, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ms-onedrive-base", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBKGs/Q4=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 3, "blocked_count": 3, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "confluence-base", "application_type_label": "SAAS APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBM+W6ws=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 5, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 5, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "notion-base", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "yes", "application_sub_type": "sanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBM7Hjw8=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 1, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "slack-downloading", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBMKoyw8=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 1, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "openai-chatgpt", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "yes", "application_sub_type": "tolerated", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBK624Qk=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 2, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "EXTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "tableau-base", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "external", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBNadjgU=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 1, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "saas", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "EXTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "tableau-base", "application_type_label": "SAAS APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "external", "source_type": "other", "user_count_sketch": "CHAQChgCIAuCBywQChgVIBkyJLqc9wPk+TyrrBnyrasB2J7CAa6euwGL7qsByJKeAoeFcpqeWw==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 10, "blocked_count": 0, "user_count": 10, "sub_tenant_id": "*********", "allowed_count": 10, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "flash", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBPaS6Aw=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 1, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "sharepoint-base", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBL3B2QE=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 1, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "imap", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQIBgCIAuCB2kQHxgVIBkyYdO1Bp/6SISpwQGo8AfZom2i5PABuI8VnLKUAaCUcNmQEfTcBZL0J+u9JvyuSpSTLsrPQqeYMfmrD4nzbLXVCdi+GvijCMKcUPu6YdO/C+vDG4Km1gHr50qb6TyqrSHAvQc=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 256, "blocked_count": 256, "user_count": 31, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "soap", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQNxgCIAuCB4sBECkYFSAZMoIBl6gXp/kj45IVmJBe890z4PcboIcKv8d2ncIGrtqbAaLtSaG7DdqpN9m4gwGm0y28hx6nu1q16gP9mUyglYgBtdotvrphxZhDh9RYsizkpTmMpwmX3RjuphKbolL2JujwfvONN6z+MN3MOaDenzK/jfQFgIDBAYO1hyiB1dMJvPOTAw==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 629, "blocked_count": 0, "user_count": 41, "sub_tenant_id": "*********", "allowed_count": 629, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQBhgCIAuCBx8QBhgVIBkyF+fQGJjTrwGJt+QEscS7AtG6vgW337ZM", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 16, "blocked_count": 16, "user_count": 6, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "google-cloud-search", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBKTZ7gk=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 3, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 3, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "http-video", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQBBgCIAuCBxcQBBgVIBkyD67kM43mqwWTgeAB8OCDBA==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 5, "blocked_count": 5, "user_count": 4, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQ5AEYAiALggeYBRDcARgVIBkyjgXPpQP6gAW8pwnTlBORUcHdBM70C6rLBO/DEKXTF93mAdfNGP7GE9GnBb6IDZG6AYH+H7idFdKXAsCkA9n/E72CA6bDDeDfCuORCqztBavkDI41t12eqgKHjAKlmQK65hKk/xHxlwPzUtoe0csRwH2c+gHZ6AKEqAvGgAuniwGRiw7b0APj+AXe5QHcxQGugwKv3BmmrBu1YsrWB7yTDc/uArmIDNCeDJiWCu+sAsX0BqKCCY7SDLJCrIoB5d8BkdgMv6gG/aoC1Crn3QKUVK6FFdmNBZzHBu7GEseoK/jkHrrqCeCYAbkpy6EI0vMBqNUNgqkE+scCjuYF+IkDqqgFrdkJhpEI6/YKmJUVjpwBCfaoAYDjDsnLId73AuW3BbnrA6vsD4i+A5mxCJyYAdK0BaLBAcmtBLi/Ab+aGZGDJun0CcuwHM+fBICAC5u7EsW3CuOJBauqBd33YJ2HC4WNDLVl0YkMtMIKsp8BwLcGpdIJ4r4rrdMRzasTjZ0Gg40I7LwT+MMDn4oO39MGrOkUxl3JowGDlwLfzAnI7gOq2xKWdOZe9bcHoEG1kxXflAfU8AeCixC6gQ/E5QbXvwTMzBL6ygaZ/wPqxRP47gbaoQXjmxzExQjBzQaqogGvjBXttAH0/AGB6gmgxgWQpBev+RCuB4PGArvxCZ4i/tUEqcYFuf4CsewHsOgLj4oGvr8VobIOxPwFoIYBtYoT/uQMn8kg9LIGiIgC0f0P8BDZ6gzxoBaQ3xzd7h7AsQPFlhHxfqj1Auj0Ar/HmDCAt84BgcQiwP+hBcOMwAb/z84E//SuAv7GkwaDirkDv8WwAv6SggHG+c8CuuIxg/WABP3OpwqCsMYJv6fxAf+blQE=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 636, "blocked_count": 0, "user_count": 220, "sub_tenant_id": "*********", "allowed_count": 636, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "http-video", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBM7Lvwc=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 1, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "websocket", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQAhgCIAuCBw8QAhgVIBkyB+yZWaX8vwQ=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 0, "user_count": 2, "sub_tenant_id": "*********", "allowed_count": 2, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "unknown-tcp", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQBBgCIAuCBxYQBBgVIBkyDqSnrgL+mMoDh9A/htsU", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 26, "blocked_count": 0, "user_count": 4, "sub_tenant_id": "*********", "allowed_count": 26, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "websocket", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwsQARgVIBkyA+yZWQ==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 1, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "prisma-cloud-compute-defender", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBJya2Qk=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 1, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "netflix-streaming", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBKzVpQQ=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 1, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "google-maps", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQAxgCIAuCBxMQAxgVIBkyC/qypwLXl1zY8YEE", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 6, "blocked_count": 0, "user_count": 3, "sub_tenant_id": "*********", "allowed_count": 6, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ssh", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQAxgCIAuCBxQQAxgVIBkyDMWfmwWyqoMB9O6ZCQ==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 14, "blocked_count": 14, "user_count": 3, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "imap", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQJxgCIAuCB3gQJBgVIBkycKymRMrYPs+pG7/WIbTyggGygFOc3HXM9zi5uASy1lCi8wK9yTDm80nTgDmXgn+7uwrm0EH/p264jSG4vwiryE+koQmj2h2wY9zfSprbOveDEJ2KA7HlOYO1GdCXUKDzFPuQiQL6gKpGg9D/FP+99gg=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 422, "blocked_count": 422, "user_count": 36, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "webdav", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBIaFywU=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 2, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Malware", "aggregation_type": "last_3_hours", "application_name": "sharepoint-base", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBKPhpwY=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 1, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Critical", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBLe+vAQ=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 7, "blocked_count": 7, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQBBgCIAuCBxcQBBgVIBkyD9u7TJ6r2AHqhI4B3raHVw==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 7, "blocked_count": 7, "user_count": 4, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "unknown-tcp", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwsQARgVIBkyA7vkOg==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 2, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQXRgCIAuCB4kCEFUYFSAZMoACtbwNh+IDscUlobkUrVKrrhaBjQ+T8QOqii2UiTX8kEKiXcvhMfuuDYKCG4iSObCWGdWEAsrnCcb8AubnA+OOArKPFPKBE8ulG6P2kQGmggqvjBvPnwPBnhDKVIKEDaK7Eqo7+dwGjcsS6pML/KAOzIcY7NRXrukCt6Ubx/s/0toRvKoTu7QD8vEIyKgJ7/hGpLUH0awqt8gluecE1csMrIsHr5AUqtMWproEsLs3u6BPyM0Pj7E70ust7roF4o8Q2Oko++QstZQa/IwP4P4mvb0bwucIl/wO5b89hsUht/Iquf8Y48gLttcI7mOJnd8x/8XqBMGlzwa/wtoEv5+FLQ==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 272, "blocked_count": 0, "user_count": 85, "sub_tenant_id": "*********", "allowed_count": 272, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "google-base", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "sanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "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", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 6088, "blocked_count": 0, "user_count": 2458, "sub_tenant_id": "*********", "allowed_count": 6088, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "websocket", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBMKZlAw=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 1, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQBxgCIAuCByEQBxgVIBkyGfjMWpn+1QKi+fEC1p+4BO2ujQKMhX2m5SU=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 122, "blocked_count": 122, "user_count": 7, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "url_filtering", "threat_category": "Grayware", "aggregation_type": "last_3_hours", "application_name": "incomplete", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBLLFoQk=", "version": 1, "security_service_type_label": "URL Filtering", "threat_count": 1, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 1, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "soap", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQJBgCIAuCB2sQHxgVIBkyY5H5IOfTPpeIiwH52geA+5IB8p8CwMMbkokG2797x5Uv8O8Dh/Vtt78HkP9lwuFWlesJ3oEF8aY524Uiv8gctflk06c/ruEkz5uaAd3NQKquKIfy+gKF9xeNql6wvMQ5gbjiDg==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 565, "blocked_count": 0, "user_count": 31, "sub_tenant_id": "*********", "allowed_count": 565, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "EXTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "external", "source_type": "other", "user_count_sketch": "CHAQBxgCIAuCBx8QBhgVIBkyF7XPgQP92QLjq7sCpMeQAYSnjQSG2aZI", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 15, "blocked_count": 0, "user_count": 6, "sub_tenant_id": "*********", "allowed_count": 15, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "EXTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "external", "source_type": "other", "user_count_sketch": "CHAQJxgCIAuCB20QIBgVIBkyZfL3CaPOngKShRiE9IYB5Jdry9oRpPZPgLCVAfeJDYjwI/r+MKKeNvLREJyGd+bhCf6tjgHC1Tj4rQL46EC+siDQ044B1psGhdQUlO0b+M8C6IEPlfp1qPhuwbscsIMt8t0ei6UG", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1034, "blocked_count": 1034, "user_count": 32, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "EXTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "unknown-udp", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "allowed", "source_sub_type": "external", "source_type": "other", "user_count_sketch": "CHAQAhgCIAuCBw8QAhgVIBkyB4KOtwqgnFc=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 0, "user_count": 2, "sub_tenant_id": "*********", "allowed_count": 2, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "EXTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "portmapper", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "allowed", "source_sub_type": "external", "source_type": "other", "user_count_sketch": "CHAQBBgCIAuCBxcQBBgVIBkyD/7WEvHG1AXD3o0Fz5HhcQ==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 12, "blocked_count": 0, "user_count": 4, "sub_tenant_id": "*********", "allowed_count": 12, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "EXTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "external", "source_type": "other", "user_count_sketch": "CHAQAhgCIAuCBxAQAhgVIBkyCLjyywHQjqMI", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 2, "user_count": 2, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "EXTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Critical", "action_category": "blocked", "source_sub_type": "external", "source_type": "other", "user_count_sketch": "CHAQHBgCIAuCB1kQGBgVIBkyUeuHHqKT+QHe+BHKu1j92QLo/CPM1gKCgrIBrdZipMeQAeDSca/MP8DF7QHqwg3L/2DimoQB/a46/uo4vLIdoLqKAtnLAevtgAGJ/oRDvquKEg==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 130, "blocked_count": 130, "user_count": 24, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "EXTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "sip", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "allowed", "source_sub_type": "external", "source_type": "other", "user_count_sketch": "CHAQAhgCIAuCBxAQAhgVIBkyCIHMnwWt2+sI", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 12, "blocked_count": 0, "user_count": 2, "sub_tenant_id": "*********", "allowed_count": 12, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "advanced", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "C2", "aggregation_type": "last_3_hours", "application_name": "dns-base", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQBRgCIAuCBxQQAxgVIBkyDJzl/QHIys0Eq6qiAQ==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 189, "blocked_count": 189, "user_count": 3, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "url_filtering", "threat_category": "C2", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "allowed", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBJiirww=", "version": 1, "security_service_type_label": "URL Filtering", "threat_count": 1, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 1, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "url_filtering", "threat_category": "<PERSON><PERSON>", "aggregation_type": "last_3_hours", "application_name": "ssl", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBPnu4Qk=", "version": 1, "security_service_type_label": "URL Filtering", "threat_count": 1, "blocked_count": 1, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "dns_security", "threat_category": "C2", "aggregation_type": "last_3_hours", "application_name": "dns-base", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBKPyngI=", "version": 1, "security_service_type_label": "DNS Security", "threat_count": 3, "blocked_count": 3, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBMq97gQ=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 1, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "unknown-udp", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "allowed", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBNvH2gw=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 4, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 4, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "internet", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "INTERNET APPS", "platform_type": "ngfw", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQAhgCIAuCBwwQARgVIBkyBOfj7ws=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 390, "blocked_count": 390, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "internet", "security_service_sub_type": "advanced", "platform_type_label": "Prisma Access", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "C2", "aggregation_type": "last_3_hours", "application_name": "unknown-tcp", "application_type_label": "INTERNET APPS", "platform_type": "prisma_access", "dest_ip_type": "Public", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Critical", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBPyXnQk=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 1, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "IOT DEVICES", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Critical", "action_category": "blocked", "source_sub_type": "iot", "source_type": "iot", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBJn/4A8=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 2, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "IOT DEVICES"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "IOT DEVICES", "security_service_type": "dns_security", "threat_category": "Grayware", "aggregation_type": "last_3_hours", "application_name": "dns-base", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "blocked", "source_sub_type": "iot", "source_type": "iot", "user_count_sketch": "CHAQDBgCIAuCByAQBhgVIBkyGK68/APR7M0B+fqYApvfrASMyJUBoJvOAQ==", "version": 1, "security_service_type_label": "DNS Security", "threat_count": 1187, "blocked_count": 1187, "user_count": 6, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "IOT DEVICES"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "IOT DEVICES", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ms-ds-smbv3", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "iot", "source_type": "iot", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBMG7zAE=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 1, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "IOT DEVICES"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "IOT DEVICES", "security_service_type": "dns_security", "threat_category": "Grayware", "aggregation_type": "last_3_hours", "application_name": "dns-base", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "iot", "source_type": "iot", "user_count_sketch": "CHAQAhgCIAuCBwwQARgVIBkyBK68/AM=", "version": 1, "security_service_type_label": "DNS Security", "threat_count": 5, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 5, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "IOT DEVICES"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ms-ds-smbv3", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQJhgCIAuCB3kQJRgVIBkycf/ea9yBKYvBJNuFSJjnI+jEAejmAu27LrPVJuLbOoycPOypB4P1TKmMLs+1EtaMMOeAcOykiwHVNMe7eee4fpKxI82tB8WZCPepKe+9BIfzC/62LcPPH7HPrQHd+AP7tATX6WXeiCbZsocCtscrxKka", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 303, "blocked_count": 0, "user_count": 37, "sub_tenant_id": "*********", "allowed_count": 303, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQDxgCIAuCBzcQDhgVIBkyL+ziowGv3yXAm88C8YuPBM/AMK226ALb2iSq1ATm2SuCo6cClL4sjqkl09JI7OsF", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 59, "blocked_count": 0, "user_count": 14, "sub_tenant_id": "*********", "allowed_count": 59, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ms-local-security-management", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQCRgCIAuCBykQCRgVIBkyIbHqvAGouLQC9suGAZmC8gKRwf0E8sgC9+BChc4Pl5+OAg==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 21, "blocked_count": 0, "user_count": 9, "sub_tenant_id": "*********", "allowed_count": 21, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ftp", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBNnkqAM=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 26, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 26, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "google-base", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "sanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQLBgCIAuCB48BECwYFSAZMoYBwZUYrslygpY884EBoO8bypIv+pUB0fwQ7OMu2ckFtrADzsQZp+cCxOwvmc5CwpsF/viQAdKxLtvHMJuKMfGOBuLhCIvzLOuCCOeISLSuFvK+Mc3XMvRY/K8V87oV1d+XAahVnN8oluoP9b6cAanwCp7AbuatAsfq5AGP8n28l0WlwwOGkg8=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 149, "blocked_count": 0, "user_count": 44, "sub_tenant_id": "*********", "allowed_count": 149, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ms-ds-smbv3", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBKnG2QU=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 21, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 21, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ms-ds-smbv3", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBKnG2QU=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 2, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQAhgCIAuCBxAQAhgVIBkyCMLy/wWzmJUB", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 6, "blocked_count": 6, "user_count": 2, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ms-ds-smbv3", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQFhgCIAuCB1AQFhgVIBkySP/ea9yBKebGbJjnI73nMpWxYfu6kAH4wUDM7xXdwpUCnPB557h+krEjza0HvMMx770EyPlYjsixAbCnkAGP+rICxKka1YDIVQ==", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 102, "blocked_count": 0, "user_count": 22, "sub_tenant_id": "*********", "allowed_count": 102, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ftp", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQAhgCIAuCBwwQARgVIBkyBNnkqAM=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 33, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 33, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ms-local-security-management", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQBRgCIAuCBxsQBRgVIBkyE7HqvAGouLQCoI/2COmpRZztnQI=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 7, "blocked_count": 0, "user_count": 5, "sub_tenant_id": "*********", "allowed_count": 7, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "panos-global-protect", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQAhgCIAuCBxAQAhgVIBkyCMi5gQLR7LcN", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 2, "user_count": 2, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "imap", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQAhgCIAuCBxAQAhgVIBkyCKyr5gyXk75m", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 3, "blocked_count": 3, "user_count": 2, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ms-ds-smbv3", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBNnkqAM=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 2, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "BRANCH USERS", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "branch", "source_type": "user", "user_count_sketch": "CHAQExgCIAuCB0IQERgVIBkyOuX/wAGW5ogCz8a6AuOGV9CTrwHLmXWtyg3Y31zyrf8Bjle3p80B4/IE/tHBAdWPUb3lCuujDqG8xzE=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 84, "blocked_count": 0, "user_count": 17, "sub_tenant_id": "*********", "allowed_count": 84, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "USERS"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "EXTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "external", "source_type": "other", "user_count_sketch": "CHAQBRgCIAuCBxsQBRgVIBkyE7+hxgbbib8D+NrMAtC2T+Db1gE=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 5, "blocked_count": 5, "user_count": 5, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "EXTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "external", "source_type": "other", "user_count_sketch": "CHAQCBgCIAuCBycQCBgVIBkyH/ft5QHng2nguaEBzrDhAujB5QSNiZVDwI/rFoGjqRg=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 8, "blocked_count": 8, "user_count": 8, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Public", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "EXTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Critical", "action_category": "blocked", "source_sub_type": "external", "source_type": "other", "user_count_sketch": "CHAQAhgCIAuCBxAQAhgVIBkyCLK9igGWyLkJ", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 4, "blocked_count": 4, "user_count": 2, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "dns_security", "threat_category": "Grayware", "aggregation_type": "last_3_hours", "application_name": "dns-base", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQChgCIAuCBxkQBRgVIBkyEYqpTfPd8wTIvb4Ez5xeqs9C", "version": 1, "security_service_type_label": "DNS Security", "threat_count": 1031, "blocked_count": 1031, "user_count": 5, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "dns_security", "threat_category": "Malware", "aggregation_type": "last_3_hours", "application_name": "dns-base", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQAhgCIAuCBwwQARgVIBkyBI7D5Qo=", "version": 1, "security_service_type_label": "DNS Security", "threat_count": 36, "blocked_count": 36, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "msrpc-base", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQAxgCIAuCBxAQAhgVIBkyCIXPggW6kroG", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 104, "blocked_count": 0, "user_count": 2, "sub_tenant_id": "*********", "allowed_count": 104, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "soap", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBM+a1wY=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 6, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 6, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Critical", "action_category": "allowed", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBJn/4A8=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 2, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ldap", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQAhgCIAuCBwwQARgVIBkyBNyJyQQ=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 124, "blocked_count": 124, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBM+a1wY=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2989, "blocked_count": 2989, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ssl", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBM+a1wY=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 1, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBM+a1wY=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 11, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 11, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "advanced", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "C2", "aggregation_type": "last_3_hours", "application_name": "dns-base", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Critical", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQBhgCIAuCBx8QBhgVIBkyF8ugrAf30SrIxtcBheScA72CpwK27oYz", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 93, "blocked_count": 93, "user_count": 6, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ssh", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "allowed", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQMhgCIAuCB4oBECoYFSAZMoEBlYge7tEdmu8+rNAcjcIp65gSg8w1y4y1AcWraISXC8TCJe/8NIj0A6fsLfnSLOOvC8+/IqH9D4fcSbCpKr+FFfGYIpq6Gab7I8XxGqPGZufME+DxGOLjdJfjL7OxFoGwCK+YOeC2Eu2YLqTgHdTFNdSAKvLFRLPw7AH3vijUk41g", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1539, "blocked_count": 0, "user_count": 42, "sub_tenant_id": "*********", "allowed_count": 1539, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "outlook-web", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Medium", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBM+a1wY=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 23, "blocked_count": 23, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "Prisma Access", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ms-ds-smbv3", "application_type_label": "PRIVATE APPS", "platform_type": "prisma_access", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBKeN8QU=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 2, "blocked_count": 0, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 2, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQAhgCIAuCBxAQAhgVIBkyCM+a1wbK5IkJ", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 607, "blocked_count": 607, "user_count": 2, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "http-proxy", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Critical", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBM+a1wY=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 13, "blocked_count": 13, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "incomplete", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Critical", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBM+a1wY=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 16, "blocked_count": 16, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "web-browsing", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Critical", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQAhgCIAuCBxAQAhgVIBkyCM+a1wbK5IkJ", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 173, "blocked_count": 173, "user_count": 2, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "mysql", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQBxgCIAuCBx8QBhgVIBkyF7fItgTCxdsC1Ir/AcWjeL2imgPyvahC", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 18, "blocked_count": 0, "user_count": 6, "sub_tenant_id": "*********", "allowed_count": 18, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "smtp-base", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQARgCIAuCBwwQARgVIBkyBKyowgQ=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 1, "blocked_count": 1, "user_count": 1, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ssh", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "High", "action_category": "blocked", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQAxgCIAuCBxMQAxgVIBkyC+PbCvP/tQH5vpYF", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 196, "blocked_count": 196, "user_count": 3, "sub_tenant_id": "*********", "allowed_count": 0, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}, {"tenant_id": "*********", "source_ip_type": "Private", "application_type": "private_apps", "security_service_sub_type": "base", "platform_type_label": "NGFW", "source_sub_type_label": "INTERNAL HOST", "security_service_type": "threat_protection", "threat_category": "Vulnerability", "aggregation_type": "last_3_hours", "application_name": "ms-ds-smbv3", "application_type_label": "PRIVATE APPS", "platform_type": "ngfw", "dest_ip_type": "Private", "is_genai": "no", "application_sub_type": "unsanctioned", "severity": "Low", "action_category": "allowed", "source_sub_type": "internal", "source_type": "other", "user_count_sketch": "CHAQFhgCIAuCB0UQEhgVIBkyPc7L7QGnyRmD+/cBxvRQo7YH/MEVqNIV7J9Ctp4sobOCA9XXhgG6/Qf15AmcuTj8seUBs8849c/5AvaHtWg=", "version": 1, "security_service_type_label": "Threat Prevention", "threat_count": 575, "blocked_count": 0, "user_count": 18, "sub_tenant_id": "*********", "allowed_count": 575, "record_time": "1738010543.4073", "event_time": "2025-01-27T20:40:00Z", "source_type_label": "OTHER"}]}, "status": 200, "headers": {"cache-time": "1738011139026", "content-length": "12801", "content-type": "application/json"}}