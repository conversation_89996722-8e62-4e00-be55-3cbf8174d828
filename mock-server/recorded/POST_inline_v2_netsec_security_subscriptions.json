{"url": "https://sase-saas-api.staging3.cirrotester.com/inline/v2/netsec/security_subscriptions", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418843052, 1738010843052]}, {"property": "is_gen_ai_only", "operator": "equals", "values": [true]}]}}, "response": {"inline_dlp": {"status": "enabled", "ngfw_devices_by_status": {"total_active": 405, "total_expired": 0}, "prisma_access_status": "enabled", "incidents_by_severity": [{"severity": "very_high", "total_incidents": 0}]}, "saas_inline": {"status": "enabled", "total_bytes": 14406085444, "apps_by_severity": [{"severity": "very_high", "total_apps": 45}]}, "saas_api": {"status": "enabled", "total_apps": 22, "total_connected_apps": 1, "incidents_by_severity": [{"severity": "very_high", "total_incidents": 0}]}, "endpoint_dlp": {"status": "not_applicable", "incidents_by_severity": [{"severity": "very_high", "total_incidents": 0}]}, "email_dlp": {"status": "not_applicable", "total_connected_apps": 0, "incidents_by_severity": [{"severity": "very_high", "total_incidents": 0}]}, "posture_security": {"status": "not_applicable", "total_apps": 0, "total_connected_apps": 0, "violations_by_severity": [{"severity": "very_high", "total_violations": 0}]}, "summary": {"total_assets": 1806, "assets_by_action": [{"action": "allowed", "total_assets": 0}, {"action": "blocked", "total_assets": 1806}, {"action": "quarantined", "total_assets": 0}, {"action": "sharing_revoked", "total_assets": 0}]}}, "status": 200, "headers": {"content-length": "1100", "content-type": "application/json; charset=utf-8"}}