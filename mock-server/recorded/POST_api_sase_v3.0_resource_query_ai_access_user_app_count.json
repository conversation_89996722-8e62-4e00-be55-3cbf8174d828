{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/resource/query/ai_access/user_app_count", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1738000338687, 1738011138687]}]}}, "response": {"header": {"createdAt": "2025-01-27T20:52:21Z", "dataCount": 1, "requestId": "09421287-0bdc-4467-86ec-3a21d3c0fb5e", "clientRequestId": "73dbda12-75ab-4914-9d05-7e160822ed46", "isResourceDataOverridden": false, "fieldList": [{"property": "blocked_applications", "alias": "blocked_applications", "dataType": "integer", "dataClass": "integer", "sequence": "1", "type": "integer"}, {"property": "allowed_applications", "alias": "allowed_applications", "dataType": "integer", "dataClass": "integer", "sequence": "2", "type": "integer"}, {"property": "total_applications", "alias": "total_applications", "dataType": "integer", "dataClass": "integer", "sequence": "3", "type": "integer"}, {"property": "allowed_users", "alias": "allowed_users", "dataType": "integer", "dataClass": "integer", "sequence": "4", "type": "integer"}, {"property": "blocked_users", "alias": "blocked_users", "dataType": "integer", "dataClass": "integer", "sequence": "5", "type": "integer"}, {"property": "total_users", "alias": "total_users", "dataType": "integer", "dataClass": "integer", "sequence": "6", "type": "integer"}], "status": {"subCode": 200}, "name": "ai_access/user_app_count", "cache_time": 1738011139002, "cache_age_in_seconds": 2, "cache_operator": "inline", "cache_operation": "WRITE"}, "data": [{"blocked_applications": 1, "allowed_applications": 9, "total_applications": 10, "allowed_users": 38, "blocked_users": 1, "total_users": 39}]}, "status": 200, "headers": {"cache-time": "1738011139002", "content-length": "454", "content-type": "application/json"}}