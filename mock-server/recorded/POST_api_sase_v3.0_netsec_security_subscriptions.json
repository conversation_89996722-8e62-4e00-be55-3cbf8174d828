{"url": "https://pa-api-us-qa01.tools.panclouddev.com/api/sase/v3.0/netsec/security_subscriptions", "method": "POST", "request": {"filter": {"rules": [{"property": "event_time", "operator": "between", "values": [1735418846109, 1738010846109]}]}}, "response": {"data": [{"pa_license": true, "ngfw_license": false, "security_service_type_label": "Threat Prevention", "security_service_type": "threat_prevention", "security_service_sub_type": "advanced", "active": 0, "expired": 0, "no_license": 0}, {"ngfw_license": false, "security_service_type_label": "Threat Prevention", "security_service_type": "threat_prevention", "security_service_sub_type": "base", "active": 0, "expired": 0, "no_license": 0}, {"pa_license": true, "ngfw_license": false, "security_service_type_label": "Threat Prevention", "security_service_type": "threat_prevention", "security_service_sub_type": "combined", "active": 0, "expired": 0, "no_license": 0}, {"pa_license": true, "ngfw_license": false, "security_service_type_label": "URL Filtering", "security_service_type": "url_filtering", "security_service_sub_type": "advanced", "active": 0, "expired": 0, "no_license": 0}, {"ngfw_license": false, "security_service_type_label": "URL Filtering", "security_service_type": "url_filtering", "security_service_sub_type": "base", "active": 0, "expired": 0, "no_license": 0}, {"pa_license": true, "ngfw_license": false, "security_service_type_label": "URL Filtering", "security_service_type": "url_filtering", "security_service_sub_type": "combined", "active": 0, "expired": 0, "no_license": 0}, {"pa_license": true, "ngfw_license": false, "security_service_type_label": "WildFire", "security_service_type": "wildfire", "security_service_sub_type": "advanced", "active": 0, "expired": 0, "no_license": 0}, {"ngfw_license": false, "security_service_type_label": "WildFire", "security_service_type": "wildfire", "security_service_sub_type": "base", "active": 0, "expired": 0, "no_license": 0}, {"pa_license": true, "ngfw_license": false, "security_service_type_label": "WildFire", "security_service_type": "wildfire", "security_service_sub_type": "combined", "active": 0, "expired": 0, "no_license": 0}, {"pa_license": true, "ngfw_license": false, "security_service_type_label": "DNS Security", "security_service_type": "dns_security", "security_service_sub_type": "advanced", "active": 0, "expired": 0, "no_license": 0}, {"ngfw_license": false, "security_service_type_label": "DNS Security", "security_service_type": "dns_security", "security_service_sub_type": "base", "active": 0, "expired": 0, "no_license": 0}, {"pa_license": true, "ngfw_license": false, "security_service_type_label": "DNS Security", "security_service_type": "dns_security", "security_service_sub_type": "combined", "active": 0, "expired": 0, "no_license": 0}]}, "status": 200, "headers": {"content-length": "271", "content-type": "application/json"}}