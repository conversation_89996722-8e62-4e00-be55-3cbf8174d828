const express = require('express');
const bodyParser = require('body-parser');
const fs = require('fs-extra');
const path = require('path');
const https = require('https');
const cors = require('cors');

const app = express();
const port = 3001;

const privateKeyPath = path.join(__dirname, '..', '..' ,'certs', 'privkey.pem');
const certificatePath = path.join(__dirname, '..', '..', 'certs', 'cert.pem');

// Add these lines to read SSL certificate and key
const privateKey = fs.readFileSync(privateKeyPath, 'utf8');
const certificate = fs.readFileSync(certificatePath, 'utf8');
const credentials = { key: privateKey, cert: certificate };

// Enable CORS for all routes
app.use(cors());

app.use(bodyParser.json({ limit: '50mb' }));
app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }));

app.post('/record', (req, res) => {
  const recordData = req.body;
  if (recordData && recordData.request && recordData.response) {
    const urlPath = new URL(recordData.url).pathname;
    const fileName = `${recordData.method}${urlPath.replace(/\//g, '_')}.json`;
    const filePath = path.join(__dirname, 'recorded', fileName);

    fs.outputJsonSync(filePath, recordData, { spaces: 2 });
  }
  res.status(200).json({ message: 'Record saved successfully' });
});
// Mock API routes
app.all('*', (req, res) => {
  console.log("Mock API routes.....", req.url);

  const recordedDir = path.join(__dirname, 'recorded');
  const files = fs.readdirSync(recordedDir);

  const matchingFile = files.find(file => {
    const data = fs.readJsonSync(path.join(recordedDir, file));
    const storedPath = new URL(data.url).pathname;
    return data.method === req.method && storedPath === req.path;
  });

  if (matchingFile) {
    const data = fs.readJsonSync(path.join(recordedDir, matchingFile));
    res.status(data.status).json(data.response);
  } else {
    res.status(404).json({ error: 'Not found' });
  }
});

// Create HTTPS server
const httpsServer = https.createServer(credentials, app);

httpsServer.listen(port, () => {
  console.log(`Mock HTTPS server listening at https://localhost:${port}`);
});